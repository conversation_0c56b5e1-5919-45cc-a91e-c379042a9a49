@isTest
private class BatchJobSyncFieldDataTest {
    @testSetup
    static void setup() {
        // Create test accounts
        List<Account> testAccounts = new List<Account>();
        for (Integer i = 0; i < 200; i++) {
            testAccounts.add(new Account(
                                 Name = 'Test Account ' + i,
                                 SalesChannel__c = 'Retail',
                                 PrimarySegment__c = 'Biotech',
                                 PrimarySubsegment__c = 'Biotech - General',
                                 SamplesYear__c = i,
                                 AccountNumber = 'SampleData'
            ));
        }
        insert testAccounts;
    }

    @isTest
    static void testBatchJobExecution() {
        // Query to be used in the batch job
        String query = 'SELECT Id, AccountNumber, Site FROM Account';

        //Define Map
        Map<String, String> fieldsToUpdateMap = new Map<String, String>();
        fieldsToUpdateMap.put('Id', 'Id');
        fieldsToUpdateMap.put('AccountNumber', 'Site');

        Test.startTest();
        BatchJobSyncFieldData batchJob = new BatchJobSyncFieldData(query, fieldsToUpdateMap);
        Id batchJobId = Database.executeBatch(batchJob, 200);
        Test.stopTest();

        // Verify results
        List<Account> updatedAccounts = [SELECT Id, AccountNumber, Site FROM Account];

        System.assertEquals(200, updatedAccounts.size(), 'Expected 200 accounts to be processed');

        for (Account acc : updatedAccounts) {
            System.assertEquals(acc.AccountNumber, acc.Site, 'AccountNumber should match Site');
        }
    }

    @isTest
    static void testEmailSending() {
        // Query to be used in the batch job
        String query = 'SELECT Id, AccountNumber, Site FROM Account';

        //Define Map
        Map<String, String> fieldsToUpdateMap = new Map<String, String>();
        fieldsToUpdateMap.put('Id', 'Id');
        fieldsToUpdateMap.put('AccountNumber', 'Site');

        Test.startTest();
        BatchJobSyncFieldData batchJob = new BatchJobSyncFieldData(query, fieldsToUpdateMap);
        Id batchJobId = Database.executeBatch(batchJob, 200);
        Test.stopTest();
    }
}