/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  25 Feb 2025

   @ Description	:   The Service Layer Pattern is a very powerful tool that can help a team create a strong bias towards reusable code. If this concept is new to your team, the Service Layer Badge on Trailhead is a great place to start.
                        The underlying pattern is old and has a long background in many programming languages (including Java).
                        The recommended approach is to create a single service class for each Salesforce object for which you have code (“AccountService.cls”, “CampaignMemberService.cls”, etc). Within that class you create static methods which implement different bits of business logic. Your main design constraint is
                        to try and make the service methods themselves as reusable as possible. Keep them generic!

   @ Developer Notes  : AccountService accountService = new AccountService();

                        1) String accountName = accountService.getAccountName(accountId);
                        2) String accountName = new AccountService().getAccountName(accountId);


   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                    https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master
                    https://bluecanvas.io/blog/how-to-structure-and-manage-your-apex-codebase-for-scale-and-agility

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 25 Feb 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/
public inherited sharing class NoteService {
  @TestVisible
  private static DmlHelper dmlHelper = new DmlHelper();
  @TestVisible
  private static NoteSelector noteSelector = new NoteSelector();

  public List<Note__c> getNotes(Set<Id> noteParentIds) {
    List<Note__c> notes = noteSelector.selectNotesByParentRecordIds(
      noteParentIds
    );
    return notes.isEmpty() ? null : notes;
  }

  public List<Note__c> getNotesByIds(Set<Id> noteIds) {
    List<Note__c> notes = noteSelector.selectNotesByIds(noteIds);
    return notes.isEmpty() ? null : notes;
  }

  public Note__c getNote(Id noteId) {
    List<Note__c> notes = noteSelector.selectNotesByIds(new Set<Id>{ noteId });
    return notes.isEmpty() ? null : notes[0];
  }

  public void updateNotes(List<Note__c> notesToUpdate, String Source) {
    for (Note__c noteToUpdate : notesToUpdate) {
    }
    dmlHelper.updateObjects(notesToUpdate, Source);
  }

  public void createNotes(List<Note__c> notesToInsert, String Source) {
    for (Note__c noteToInsert : notesToInsert) {
    }
    dmlHelper.insertObjects(notesToInsert, Source);
  }
}