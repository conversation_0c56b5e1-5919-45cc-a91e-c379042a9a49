/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  06 May 2024

   @ Description	:   A test class containing test methods for apex automation on the MultiSelectReporting__c Object

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   MultiSelectReportingSelector, MultiSelectReportingService

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 06 May 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
@IsTest
public class MultiSelectReportingTestPack {
  //Test MultiSelectReportingService class
  @isTest
  static void test_MultiSelectReportingService() {
    Test.startTest();
    // Given
    Id validMultiSelectReportingId = MockerUtils.generateId(MultiSelectReporting__c.SObjectType);
    Id validContactId = MockerUtils.generateId(Contact.SObjectType);
    List<Id> multiSelectReportingIds = new List<Id>{
      MockerUtils.generateId(MultiSelectReporting__c.SObjectType),
      MockerUtils.generateId(MultiSelectReporting__c.SObjectType)
    };

    //Setup MultiSelectReporting__c Test Data
    MultiSelectReporting__c expectedMultiSelectReportingRecord = (MultiSelectReporting__c) MockerUtils.updateObjectState(
      new MultiSelectReporting__c(
        Id = validMultiSelectReportingId,
        PicklistName__c = 'Method',
        PicklistNameAPI__c = 'Method__c',
        PicklistValue__c = 'Lamp'
      ),
      new Map<String, Object>{ 'Name' => 'MSEL-9999' }
    );

    //Setup Contact Test Data
    Map<SObject, String> testMap = new Map<SObject, String>();

    Contact testContact = new Contact(Id = validContactId, LastName = 'Soap');

    testMap.put(testContact, 'LAMP;PCR');

    List<MultiSelectReporting__c> multiSelectReportingsToInsert = new List<MultiSelectReporting__c>{ expectedMultiSelectReportingRecord };
    List<MultiSelectReporting__c> multiSelectReportingsToDelete = new List<MultiSelectReporting__c>{ expectedMultiSelectReportingRecord };

    List<String> accountNames = new List<String>{ 'Dummy Account 1', 'Dummy Account 2' };

    Mocker mocker = Mocker.startStubbing();

    DmlHelper dmlHelperMock = (DmlHelper) mocker.mock(DmlHelper.class);

    dmlHelperMock.insertObjects(multiSelectReportingsToInsert, 'MultiSelectReportingService.createMultiSelectReporting');
    Mocker.MethodRecorder insertObjectsRec = mocker.when().withAnyValues().getMethodRecorder();

    dmlHelperMock.deleteObjects(multiSelectReportingsToDelete, 'MultiSelectReportingService.deleteMultiSelectReporting');
    Mocker.MethodRecorder deleteObjectsRec = mocker.when().withAnyValues().getMethodRecorder();

    MultiSelectReportingSelector multiSelectReportingSelectorMock = (MultiSelectReportingSelector) mocker.mock(MultiSelectReportingSelector.class);

    mocker.when(multiSelectReportingSelectorMock.selectMultiSelectReportingByIds(new Set<Id>{ validMultiSelectReportingId }))
      .thenReturn(new List<MultiSelectReporting__c>{ expectedMultiSelectReportingRecord });

    mocker.when(multiSelectReportingSelectorMock.selectMultiSelectReportingByContactIds(new Set<Id>{ validContactId }))
      .withAnyValues()
      .thenReturn(new List<MultiSelectReporting__c>{ expectedMultiSelectReportingRecord });

    mocker.when(multiSelectReportingSelectorMock.selectMultiSelectReportingByContactIdsAndFieldName(new Set<Id>{ validContactId }, 'Method__c'))
      .withAnyValues()
      .thenReturn(new List<MultiSelectReporting__c>{ expectedMultiSelectReportingRecord });

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    MultiSelectReportingService.multiSelectReportingSelector = multiSelectReportingSelectorMock;
    MultiSelectReportingService.dmlHelper = dmlHelperMock;

    // When 1
    //test method void createMultiSelectReporting(List<MultiSelectReporting__c> multiSelectReportingsToInsert, String Source) on MultiSelectReportingService
    new MultiSelectReportingService()
      .createMultiSelectReporting(multiSelectReportingsToInsert, 'MultiSelectReportingTestPack.test_MultiSelectReportingService()');

    // Then 1
    System.assertEquals(1, insertObjectsRec.getCallsCount());

    // When 2
    //test method void deleteMultiSelectReporting(List<MultiSelectReporting__c> multiSelectReportingsToDelete, String Source) on MultiSelectReportingService
    new MultiSelectReportingService()
      .deleteMultiSelectReporting(multiSelectReportingsToDelete, 'MultiSelectReportingTestPack.test_MultiSelectReportingService()');

    // Then 2
    System.assertEquals(1, deleteObjectsRec.getCallsCount());

    // When 3
    //test method public List<MultiSelectReporting__c> parseMultiSelectPicklist(Map<SObject, String> mapSObjectToMultiSelectPicklistValue, String Source, String fieldName, String fieldNameAPI) on MultiSelectReportingService
    List<MultiSelectReporting__c> parsedListContactValues = new MultiSelectReportingService()
      .parseMultiSelectPicklist(testMap, 'Contact', 'Method', 'Method__c');

    // Then 3
    System.assertEquals(2, parsedListContactValues.size());

    // When 4
    //test method public List<MultiSelectReporting__c> parseMultiSelectPicklist(Map<SObject, String> mapSObjectToMultiSelectPicklistValue, String Source, String fieldName, String fieldNameAPI) on MultiSelectReportingService
    List<MultiSelectReporting__c> parsedListAccountValues = new MultiSelectReportingService()
      .parseMultiSelectPicklist(testMap, 'Account', 'Method', 'Method__c');

    // Then 4
    System.assertEquals(2, parsedListAccountValues.size());

    Test.stopTest();
  }

  //Test MultiSelectReportingSelector class
  @isTest
  static void test_MultiSelectReportingSelector() {
    MultiSelectReportingSelector multiSelectReportingSelector = new MultiSelectReportingSelector();
    Integer queryLimit = 100;
    Set<Id> multiSelectReportingIds = new Set<Id>{
      MockerUtils.generateId(MultiSelectReporting__c.SObjectType),
      MockerUtils.generateId(MultiSelectReporting__c.SObjectType)
    };
    Set<Id> contactIds = new Set<Id>{ MockerUtils.generateId(Contact.SObjectType), MockerUtils.generateId(Contact.SObjectType) };
    Test.startTest();
    multiSelectReportingSelector.setQueryLimit(queryLimit);
    multiSelectReportingSelector.selectMultiSelectReportingByIds(multiSelectReportingIds);
    multiSelectReportingSelector.selectMultiSelectReportingByContactIds(contactIds);
    multiSelectReportingSelector.selectMultiSelectReportingByContactIdsAndFieldName(contactIds, 'Application__c');
    Test.stopTest();
  }
}