/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  08 July 2024

   @ Description	:   The Service Layer Pattern is a very powerful tool that can help a team create a strong bias towards reusable code. If this concept is new to your team, the Service Layer Badge on Trailhead is a great place to start.
                        The underlying pattern is old and has a long background in many programming languages (including Java).
                        The recommended approach is to create a single service class for each Salesforce object for which you have code (“AccountService.cls”, “CampaignMemberService.cls”, etc). Within that class you create static methods which implement different bits of business logic. Your main design constraint is
                        to try and make the service methods themselves as reusable as possible. Keep them generic!

   @ Developer Notes  : LeadService leadService = new LeadService();

                        1) String leadName = leadService.getLeadName(leadId);
                        2) String leadName = new LeadService().getLeadName(leadId);


   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                    https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master
                    https://bluecanvas.io/blog/how-to-structure-and-manage-your-apex-codebase-for-scale-and-agility

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 08 July 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public inherited sharing class LeadService {
    @TestVisible
    private static DmlHelper dmlHelper = new DmlHelper();
    @TestVisible
    private static LeadSelector leadSelector = new LeadSelector();

    public String getLeadName(Id leadId) {
        List<Lead> leads = leadSelector.selectLeadsByIds(new Set<Id> { leadId });
        return leads.isEmpty() ? null : leads[0].Name;
    }

    public void updateLeads(List<Lead> leadsToUpdate, String Source) {
        for (Lead leadToUpdate : leadsToUpdate) {
            leadToUpdate.ValidationBypassDateTime__c = System.now();
        }
        dmlHelper.updateObjects(leadsToUpdate, Source);
    }

    public void createLeads(List<Lead> leadsToInsert, String Source) {
        for (Lead leadToInsert : leadsToInsert) {
            leadToInsert.ValidationBypassDateTime__c = System.now();
        }
        dmlHelper.insertObjects(leadsToInsert, Source);
    }
}