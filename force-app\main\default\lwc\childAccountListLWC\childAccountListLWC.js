import { LightningElement, api, wire } from "lwc";
import { NavigationMixin } from "lightning/navigation";
import getChildAccounts from "@salesforce/apex/GeneralLWCController.getChildAccounts";

const COLUMNS = [
  {
    label: "Account Name",
    fieldName: "Name",
    type: "button",
    typeAttributes: {
      label: { fieldName: "Name" },
      variant: "base"
    },
    initialWidth: 700 // Set width to 700px
  },
  { label: "Sales Channel", fieldName: "SalesChannel__c", type: "text", initialWidth: 150 },
  { label: "Sales Team", fieldName: "SalesTeam__c", type: "text", initialWidth: 150 },
  { label: "Owner", fieldName: "OwnerName", type: "text", initialWidth: 200 }
];

export default class ChildAccountsListLWC extends NavigationMixin(LightningElement) {
  @api recordId; // This will hold the parent account Id
  columns = COLUMNS;
  childAccountsData;
  childAccountsDataSize;
  cardTitle = "Child Accounts";

  @wire(getChildAccounts, { parentId: "$recordId" })
  wiredChildAccounts({ error, data }) {
    if (data) {
      this.childAccountsDataSize = data.length;
      this.cardTitle = this.cardTitle + " (" + this.childAccountsDataSize + ")";

      this.childAccountsData = data.map((row) => ({
        ...row,
        nameUrl: `/${row.Id}`,
        SalesChannel__c: row.SalesChannel__c,
        SalesTeam__c: row.SalesTeam__c,
        OwnerName: row.Owner.Name
      }));
    } else if (error) {
      console.error("Error fetching child accounts", error);
    }
  }

  handleRowAction(event) {
    const row = event.detail.row;
    this.navigateToAccountRecord(row.Id);
  }

  navigateToAccountRecord(recordId) {
    this[NavigationMixin.Navigate]({
      type: "standard__recordPage",
      attributes: {
        recordId: recordId,
        objectApiName: "Account",
        actionName: "view"
      }
    });
  }
}