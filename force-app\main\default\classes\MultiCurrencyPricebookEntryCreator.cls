/********************************************************************************************

   @ Func Area	:  Price Book Management

   @ Author	:  <PERSON>

   @ Date		:  14 February 2025

   @ Description	:   A class containing methods for creating PricebookEntry records for multiple currencies

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :       Set<String> currencies = new Set<String>{'USD', 'EUR', 'GBP'};
                                Map<String, Decimal> rates = new Map<String, Decimal>{
                                    'USD' => 1.0,
                                    'EUR' => 0.85,
                                    'GBP' => 0.73
                                };
                                
                                MultiCurrencyPricebookEntryCreator creator = new MultiCurrencyPricebookEntryCreator(currencies, rates);
                                
                                // For single product
                                creator.createEntriesForProduct('01t....');
                                
                                // For multiple products
                                Set<Id> productIds = new Set<Id>{'01t....', '01t....'};
                                creator.createEntriesForMultipleProducts(productIds);

   @ Test Class	:   BatchJobMultiCurrencyPricebookEntryTest

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 14 February 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/
public class MultiCurrencyPricebookEntryCreator {
  // Currencies to create PricebookEntry records for
  private Set<String> targetCurrencies;
  // Exchange rates for currency conversion
  private Map<String, Decimal> exchangeRates;
  // Standard pricebook ID
  private Id pricebookId;
  private String pricebookName;

  public MultiCurrencyPricebookEntryCreator(
    Set<String> currencies,
    Map<String, Decimal> rates,
    String pricebookName
  ) {
    this.targetCurrencies = currencies;
    this.exchangeRates = rates;
    this.pricebookName = pricebookName;
    this.pricebookId = [
      SELECT Id
      FROM Pricebook2
      WHERE Name = :pricebookName
      LIMIT 1
    ]
    .Id;
  }

  public void createEntriesForProduct(Id productId) {
    try {
      Product2 productQueried = [
        SELECT Id, MOALUSListPrice__c, CGSPriceBook__c, DTCPriceBook__c
        FROM Product2
        WHERE Id = :productId
        LIMIT 1
      ];

      // List to hold new PricebookEntry records
      List<PricebookEntry> newEntries = new List<PricebookEntry>();

      //System.debug('targetCurrencies: ' + targetCurrencies);

      if (pricebookName == 'Standard Price Book') {
        // Create entries for each target currency
        for (String currencyCode : targetCurrencies) {
          System.debug('targetCurrencies -> currencyCode : ' + currencyCode);
          // Skip if entry is in org's default currency
          /* if (currencyCode == standardEntry.CurrencyIsoCode)
           continue;*/

          Decimal moalListPriceUSD = productQueried.MOALUSListPrice__c;

          if (moalListPriceUSD == null || moalListPriceUSD == 0) {
            moalListPriceUSD = 1;
          }

          // Calculate price in target currency
          Decimal convertedPrice =
            moalListPriceUSD * exchangeRates.get(currencyCode);

          PricebookEntry newEntry = new PricebookEntry(
            Pricebook2Id = pricebookId,
            Product2Id = productId,
            UnitPrice = convertedPrice.setScale(2),
            IsActive = true,
            CurrencyIsoCode = currencyCode
          );

          newEntries.add(newEntry);
        }
      }

      if (pricebookName == 'CGS' && productQueried.CGSPriceBook__c) {
        // Create entries for each target currency
        for (String currencyCode : targetCurrencies) {
          // Skip if entry is in org's default currency
          /* if (currencyCode == standardEntry.CurrencyIsoCode)
           continue;*/

          Decimal moalListPriceUSD = productQueried.MOALUSListPrice__c;

          if (moalListPriceUSD == null || moalListPriceUSD == 0) {
            moalListPriceUSD = 1;
          }

          // Calculate price in target currency
          Decimal convertedPrice =
            moalListPriceUSD * exchangeRates.get(currencyCode);

          PricebookEntry newEntry = new PricebookEntry(
            Pricebook2Id = pricebookId,
            Product2Id = productId,
            UnitPrice = convertedPrice.setScale(2),
            UseStandardPrice = false,
            IsActive = true,
            CurrencyIsoCode = currencyCode
          );

          newEntries.add(newEntry);
        }
      }

      if (pricebookName == 'DTC' && productQueried.DTCPriceBook__c) {
        // Create entries for each target currency
        for (String currencyCode : targetCurrencies) {
          // Skip if entry is in org's default currency
          /* if (currencyCode == standardEntry.CurrencyIsoCode)
           continue;*/

          Decimal moalListPriceUSD = productQueried.MOALUSListPrice__c;

          if (moalListPriceUSD == null || moalListPriceUSD == 0) {
            moalListPriceUSD = 1;
          }

          // Calculate price in target currency
          Decimal convertedPrice =
            moalListPriceUSD * exchangeRates.get(currencyCode);

          PricebookEntry newEntry = new PricebookEntry(
            Pricebook2Id = pricebookId,
            Product2Id = productId,
            UnitPrice = convertedPrice.setScale(2),
            UseStandardPrice = false,
            IsActive = true,
            CurrencyIsoCode = currencyCode
          );

          newEntries.add(newEntry);
        }
      }

      // Insert new entries
      if (!newEntries.isEmpty()) {
        insert newEntries;
        /*
        System.debug(
          'Successfully created ' +
            newEntries.size() +
            ' PricebookEntry records'
        );
        */
      }
    } catch (Exception e) {
      System.debug('Error creating PricebookEntry records: ' + e.getMessage());
      throw new MultiCurrencyPricebookException(
        'Failed to create PricebookEntry records: ' + e.getMessage()
      );
    }
  }

  public void createEntriesForMultipleProducts(Set<Id> productIds) {
    //System.debug('productIds SIZE: ' + productIds.size());
    for (Id productId : productIds) {
      createEntriesForProduct(productId);
    }
  }

  // Custom exception class
  public class MultiCurrencyPricebookException extends Exception {
  }
}