<template>
  <lightning-card title={cardTitle} icon-name="standard:account">
    <div class="slds-m-around_medium">
      <template if:true={childAccountsData}>
        <lightning-datatable key-field="Id" data={childAccountsData} columns={columns} hide-checkbox-column onrowaction={handleRowAction}>
        </lightning-datatable>
      </template>
      <template if:true={error}> Error: {error} </template>
    </div>
  </lightning-card>
</template>