/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  29 November 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> accountIds = new Set<Id>();
                       accountIds.add('001O300000FGc3tIAD');
                       accountIds.add('001O300000FGc3tIZZ');
                       AccountSelector accountSelector = new AccountSelector();
                       List<Account> accounts = accountSelector.selectAccountsByIds(accountIds);
                       List<Account> accounts = new AccountSelector().selectAccountsByIds(accountIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ck<PERSON>
   @ Last Modified On  : 29 November 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class OpportunityContactRoleSelector {
    private String query;
    private String fromObject = ' FROM OpportunityContactRole ';
    private String queryLimit = ' LIMIT 1000';

    //Constructor to setup the base query
    public OpportunityContactRoleSelector() {
        buildBaseQuery();
    }

    //Put your fields you intend to almost always select with your account queries here
    private void buildBaseQuery() {
        this.query = 'SELECT Id, ContactId, OpportunityId, IsPrimary, Role';
    }

    //Set the limit for your query you're building
    public void setQueryLimit(Integer passedLimit) {
        String newQueryLimit = String.valueOf(passedLimit);
        this.queryLimit = ' LIMIT ' + newQueryLimit;
        //system.debug('OpportunityContactRoleSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
    }

    //Select your accounts by a set of account ids
    public List<OpportunityContactRole> selectOpportunityContactRoleByOppIds(Set<Id> opportunityIds) {
        buildBaseQuery();
        this.query += fromObject + 'WHERE OpportunityId IN :opportunityIds' + this.queryLimit;
        //system.debug('selectOpportunityContactRoleByOppIds() this.query -> ' + this.query);
        return Database.query(this.query);
    }
}