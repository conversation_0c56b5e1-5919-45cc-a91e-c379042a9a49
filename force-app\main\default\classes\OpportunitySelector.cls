/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  28 January 2025

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> opportunityLineItemScheduleIds = new Set<Id>();
                       opportunityLineItemScheduleIds.add('001O300000FGc3tIAD');
                       opportunityLineItemScheduleIds.add('001O300000FGc3tIZZ');
                       OpportunityLineItemScheduleSelector opportunityLineItemScheduleSelector = new OpportunityLineItemScheduleSelector();
                       List<OpportunityLineItemSchedule> opportunityLineItemSchedules = opportunityLineItemScheduleSelector.selectOpportunityLineItemSchedulesByIds(opportunityLineItemScheduleIds);
                       List<OpportunityLineItemSchedule> opportunityLineItemSchedules = new OpportunityLineItemScheduleSelector().selectOpportunityLineItemSchedulesByIds(opportunityLineItemScheduleIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 28 January 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class OpportunitySelector {
  private String query;
  private String fromObject = ' FROM Opportunity ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public OpportunitySelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your account queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Name, Account.Name, AccountId, Amount, AwaitingFunding__c, CalendarYear__c, CloseDate, ContractId, CreatedById, CreatedBy.Name, CYUnweightedRevenue__c, CYWeightedRevenue__c, Description, IsExcludedFromTerritory2Filter, ExpectedRevenue, ForecastCategoryName, LastModifiedById, LastModifiedBy.Name, LeadSource, LossReason__c, LossReasonDetail__c, NextCalendarYear__c, NextCYUnweightedRevenue__c, NextCYWeightedRevenue__c, NextStep, NumberofUnitsAnnually__c, CurrencyIsoCode, OwnerId, Owner.Name, Pricebook2Id, CampaignId, Probability, StageName, SyncedQuoteId, Territory2Id, TigerTeam__c, TigerTeamLink__c, Type, ValidationBypassDateTime__c, HasOpportunityLineItem';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    /*
    system.debug(
      'OpportunitySelector.setQueryLimit() new queryLimit -> ' + this.queryLimit
    );
    */
  }

  //Select your Opportunities by a set of ids
  public List<Opportunity> selectOpportunitiesByIds(Set<Id> opportunityIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id IN :opportunityIds' + this.queryLimit;
    //system.debug('selectOpportunitiesByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your Opportunities by a set of ids
  public List<Opportunity> selectOpportunityById(Id opportunityId) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id = :opportunityId' + this.queryLimit;
    //system.debug('selectOpportunitiesByOppId() this.query -> ' + this.query);
    return Database.query(this.query);
  }
}