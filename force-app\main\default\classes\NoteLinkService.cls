/********************************************************************************************

   @ Func Area	:  Note Linking Management

   @ Author	:  <PERSON>

   @ Date	:  11 June 2025

   @ Description	:   This class is responsible for processing new cases before they are committed to the Database.  The logic
                        will check if newly inserted cases meet defined trigger criteria and then perform updates to the case in
                        the before insert/update context

   @ Developer Notes   :

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 11 June 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/
public with sharing class NoteLinkService {
  @TestVisible
  private static DmlHelper dmlHelper;
  @TestVisible
  private static NoteLinkSelector noteLinkSelector;
  @TestVisible
  private static NoteService noteService;

  /**
   * Default constructor
   */
  public NoteLinkService() {
    dmlHelper = new DmlHelper();
    noteLinkSelector = new NoteLinkSelector();
    noteService = new NoteService();
  }

  /**
   * Constructor with dependencies for testing
   */
  @TestVisible
  public NoteLinkService(
    DmlHelper dmlHelper,
    NoteLinkSelector noteLinkSelector,
    NoteService noteService
  ) {
    dmlHelper = dmlHelper;
    noteLinkSelector = noteLinkSelector;
    noteService = noteService;
  }

  /**
   * Link a note to multiple records
   * @param noteId The ID of the custom note
   * @param recordIds List of record IDs to link to
   * @return List of created NoteLink__c records
   */
  public List<NoteLink__c> linkNoteToRecords(Id noteId, List<Id> recordIds) {
    System.debug(
      'NoteLinkService.linkNoteToRecords: enter with noteId=' +
        noteId +
        ', recordIds=' +
        recordIds
    );
    List<NoteLink__c> noteLinks = new List<NoteLink__c>();

    try {
      // Get object types for each record ID
      Map<Id, String> recordIdToObjectType = getObjectTypesForIds(recordIds);
      System.debug(
        'NoteLinkService.linkNoteToRecords: recordIdToObjectType=' +
        recordIdToObjectType
      );

      for (Id recordId : recordIds) {
        String objectType = recordIdToObjectType.get(recordId);
        System.debug(
          'NoteLinkService.linkNoteToRecords: Processing recordId=' +
            recordId +
            ', objectType=' +
            objectType
        );

        NoteLink__c noteLink = new NoteLink__c(
          Note__c = noteId,
          RelatedRecordId__c = String.valueOf(recordId),
          RelatedObjectType__c = objectType
          //Link_Name__c = getRecordDisplayName(recordId) // Optional helper method
        );
        noteLinks.add(noteLink);
        System.debug(
          'NoteLinkService.linkNoteToRecords: Created noteLink=' + noteLink
        );
      }

      // Instead of a try-catch statement in this class, rather make use of the DML helper to try and insert the note link.
      System.debug(
        'NoteLinkService.linkNoteToRecords: Inserting ' +
          noteLinks.size() +
          ' note links'
      );
      dmlHelper.insertObjects(noteLinks, 'NoteLinkService.linkNoteToRecords');
      System.debug(
        'NoteLinkService.linkNoteToRecords: Successfully inserted note links'
      );
    } catch (Exception e) {
      System.debug(
        'NoteLinkService.linkNoteToRecords: Error=' +
          e.getMessage() +
          ', Stack=' +
          e.getStackTraceString()
      );
      throw e;
    }

    return noteLinks;
  }

  /**
   * Link a note to a single record
   * @param noteId The ID of the custom note
   * @param recordId The record ID to link to
   * @return The created NoteLink__c record
   */
  public NoteLink__c linkNoteToRecord(Id noteId, Id recordId) {
    System.debug(
      'NoteLinkService.linkNoteToRecord: enter ' + noteId + ' ' + recordId
    );
    List<NoteLink__c> results = linkNoteToRecords(
      noteId,
      new List<Id>{ recordId }
    );
    System.debug('NoteLinkService.linkNoteToRecord: exit ' + results);
    return results.isEmpty() ? null : results[0];
  }

  /**
   * Remove link between a note and specific records
   * @param noteId The ID of the custom note
   * @param recordIds List of record IDs to unlink
   */
  public void unlinkNoteFromRecords(Id noteId, List<Id> recordIds) {
    Set<Id> recordIdSet = new Set<Id>();
    for (Id recordId : recordIds) {
      recordIdSet.add(recordId);
    }

    List<NoteLink__c> linksToDelete = noteLinkSelector.selectNoteLinksToDelete(
      noteId,
      recordIdSet
    );

    if (!linksToDelete.isEmpty()) {
      dmlHelper.deleteObjects(
        linksToDelete,
        'NoteLinkService.unlinkNoteFromRecords'
      );
    }
  }

  /**
   * Remove link between a note and a single record
   * @param noteId The ID of the custom note
   * @param recordId The record ID to unlink
   */
  public void unlinkNoteFromRecord(Id noteId, Id recordId) {
    System.debug(
      'NoteLinkService.unlinkNoteFromRecord: enter ' + noteId + ' ' + recordId
    );
    unlinkNoteFromRecords(noteId, new List<Id>{ recordId });
    System.debug(
      'NoteLinkService.unlinkNoteFromRecord: exit ' + noteId + ' ' + recordId
    );
  }

  /**
   * Get all records linked to a specific note
   * @param noteId The ID of the custom note
   * @return Map of record IDs to their object types
   */
  public Map<Id, String> getLinkedRecords(Id noteId) {
    System.debug(
      'NoteLinkService.getLinkedRecords: enter with noteId: ' + noteId
    );
    Map<Id, String> linkedRecords = new Map<Id, String>();

    List<NoteLink__c> noteLinks = noteLinkSelector.selectNoteLinksByNoteId(
      noteId
    );

    System.debug(
      'NoteLinkService.getLinkedRecords: found ' +
        noteLinks.size() +
        ' note links'
    );

    for (NoteLink__c link : noteLinks) {
      System.debug(
        'NoteLinkService.getLinkedRecords: processing link: ' + link
      );
      System.debug(
        'NoteLinkService.getLinkedRecords: RelatedRecordId__c: ' +
          link.RelatedRecordId__c +
          ', RelatedObjectType__c: ' +
          link.RelatedObjectType__c
      );

      linkedRecords.put(
        Id.valueOf(link.RelatedRecordId__c),
        link.RelatedObjectType__c
      );
    }

    System.debug(
      'NoteLinkService.getLinkedRecords: exit with ' +
        linkedRecords.size() +
        ' linked records: ' +
        linkedRecords
    );
    return linkedRecords;
  }

  /**
   * Get all notes linked to a specific record
   * @param recordId The record ID
   * @return List of Note__c records
   */
  public List<Note__c> getNotesForRecord(Id recordId) {
    List<NoteLink__c> noteLinks = noteLinkSelector.getNotesForRecord(recordId);

    Set<Id> noteIds = new Set<Id>();
    for (NoteLink__c link : noteLinks) {
      noteIds.add(link.Note__c);
    }

    return noteService.getNotesByIds(noteIds);
  }

  /**
   * Remove all links for a note (useful when deleting a note)
   * @param noteId The ID of the custom note
   */
  public void removeAllLinksForNote(Id noteId) {
    List<NoteLink__c> linksToDelete = noteLinkSelector.selectNoteLinksByNoteId(
      noteId
    );

    if (!linksToDelete.isEmpty()) {
      dmlHelper.deleteObjects(
        linksToDelete,
        'NoteLinkService.removeAllLinksForNote'
      );
    }
  }

  // Helper Methods

  /**
   * Get object types for a list of record IDs
   * @param recordIds List of record IDs
   * @return Map of record ID to object API name
   */
  private Map<Id, String> getObjectTypesForIds(List<Id> recordIds) {
    Map<Id, String> recordIdToObjectType = new Map<Id, String>();

    for (Id recordId : recordIds) {
      String objectType = recordId.getSObjectType().getDescribe().getName();
      recordIdToObjectType.put(recordId, objectType);
    }

    return recordIdToObjectType;
  }

  /**
   * Get display name for a record (optional helper)
   * @param recordId The record ID
   * @return Display name of the record
   */
  public String getRecordDisplayName(Id recordId) {
    String objectType = recordId.getSObjectType().getDescribe().getName();

    // You can enhance this based on your needs
    try {
      SObject record = Database.query(
        'SELECT Name FROM ' + objectType + ' WHERE Id = :recordId LIMIT 1'
      );
      return (String) record.get('Name');
    } catch (Exception e) {
      // Fallback for objects without Name field
      return objectType + ': ' + recordId;
    }
  }
}

// Example Usage Class
/*
public class CustomNoteLinkingExample {
    
    public static void demonstrateUsage() {
        // Create a custom note
        Note__c myNote = new Note__c(
            Title__c = 'Sample Note',
            Body__c = 'This note will be linked to multiple records'
        );
        insert myNote;
        
        // Get some record IDs (Account and Case examples)
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        Case testCase = [SELECT Id FROM Case LIMIT 1];
        
        // Link the note to multiple records
        List<Id> recordsToLink = new List<Id>{testAccount.Id, testCase.Id};
        CustomNoteLinkingService.linkNoteToRecords(myNote.Id, recordsToLink);
        
        // Link to a single record
        // CustomNoteLinkingService.linkNoteToRecord(myNote.Id, testAccount.Id);
        
        // Get all records linked to this note
        Map<Id, String> linkedRecords = CustomNoteLinkingService.getLinkedRecords(myNote.Id);
        System.debug('Linked records: ' + linkedRecords);
        
        // Get all notes for a specific record
        List<Note__c> notesForAccount = CustomNoteLinkingService.getNotesForRecord(testAccount.Id);
        System.debug('Notes for account: ' + notesForAccount.size());
        
        // Unlink from specific records
        CustomNoteLinkingService.unlinkNoteFromRecords(myNote.Id, new List<Id>{testCase.Id});
        
        // Remove all links for a note
        // CustomNoteLinkingService.removeAllLinksForNote(myNote.Id);
    }
}
*/