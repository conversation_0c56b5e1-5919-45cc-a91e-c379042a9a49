/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  31 May 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                    there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                    to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> caseIds = new Set<Id>();
                        caseIds.add('500O3000008HgXhIAK');
                        caseIds.add('500O3000008Hh3xIAC');
                        CaseSelector caseSelector = new CaseSelector();
                        List<Case> cases = caseSelector.selectCasesById(caseIds);
                        List<Case> cases = new CaseSelector().selectCasesById(caseIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 31 May 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class CaseSelector {
  private String query;
  private String fromObject = ' FROM Case ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public CaseSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your case queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, CaseNumber, Subject, AccountId, Account.Name, ContactId, Status, Type, Origin, Automation__c, OwnerId, Owner.Name, Owner.Email, CreatedDate, ClosedDate, ValidationBypassDateTime__c, UnreadEmail__c, LastEmailReceived__c';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your cases by a set of case ids
  public List<Case> selectCasesById(Set<Id> caseIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id IN :caseIds' + this.queryLimit;
    //system.debug('selectCasesById() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your cases by a set of Entitlement ids
  public List<Case> selectCasesByEntitlementId(Set<Id> entitlementIds) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE EntitlementId IN :entitlementIds' +
      this.queryLimit;
    //system.debug('selectCasesById() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Would continue to build queries and setters for everything you theoretically need.
}