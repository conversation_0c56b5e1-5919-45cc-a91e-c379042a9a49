/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  31 May 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> accountIds = new Set<Id>();
                       accountIds.add('001O300000FGc3tIAD');
                       accountIds.add('001O300000FGc3tIZZ');
                       AccountSelector accountSelector = new AccountSelector();
                       List<Account> accounts = accountSelector.selectAccountsByIds(accountIds);
                       List<Account> accounts = new AccountSelector().selectAccountsByIds(accountIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ck<PERSON>
   @ Last Modified On  : 31 May 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class MultiSelectReportingSelector {
  private String query;
  private String fromObject = ' FROM MultiSelectReporting__c ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public MultiSelectReportingSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Name, Contact__c, Object__c, PicklistName__c, PicklistNameAPI__c, PicklistValue__c, CreatedById, CreatedBy.Name';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('MultiSelectReportingSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your accounts by a set of ids
  public List<MultiSelectReporting__c> selectMultiSelectReportingByIds(Set<Id> multiSelectReportingIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id IN :multiSelectReportingIds' + this.queryLimit;
    //system.debug('selectContactsByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your MultiSelectReporting__c records by a set of contact ids
  public List<MultiSelectReporting__c> selectMultiSelectReportingByContactIds(Set<Id> contactIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Contact__c IN :contactIds' + this.queryLimit;
    //system.debug('selectContactsByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your MultiSelectReporting__c records by a set of contact ids & fieldName
  public List<MultiSelectReporting__c> selectMultiSelectReportingByContactIdsAndFieldName(Set<Id> contactIds, String fieldName) {
    buildBaseQuery();
    //String likeQuery = '%' + fieldName + '%';
    String additionalQueryParameter = ' AND PicklistNameAPI__c = :fieldName';

    this.query += fromObject + 'WHERE Contact__c IN :contactIds' + additionalQueryParameter + this.queryLimit;
    //system.debug('selectMultiSelectReportingByContactIdsAndFieldName() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Would continue to build queries and setters for everything you theoretically need.
}