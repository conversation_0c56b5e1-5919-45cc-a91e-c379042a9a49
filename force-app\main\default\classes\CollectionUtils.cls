/********************************************************************************************

   @ Func Area	:  Collection Utility Methods

   @ Author	:  <PERSON>

   @ Date	:  04 February 2025

   @ Description	:   This Utility classs is responsible for processing collections of objects and evaluating for null or empty values    

   @ Developer Notes   :        // Example usage with Id collections
                                List<Id> accountIds = new List<Id>();
                                for(Account acc : [SELECT Id FROM Account LIMIT 5]) {
                                    accountIds.add(acc.Id);
                                }

                                // Checking List<Id>
                                if (CollectionUtils.isEmpty(accountIds)) {
                                    // Handle empty Id list case
                                }

                                // With SObject Lists
                                List<Account> accounts = [SELECT Id FROM Account LIMIT 5];
                                if (CollectionUtils.isNotEmpty(accounts)) {
                                    // Process accounts
                                }

                                // With Id Sets
                                Set<Id> accountIdSet = new Set<Id>(accountIds);
                                if (CollectionUtils.isEmpty(accountIdSet)) {
                                    // Handle empty Id set case
                                }

                                // With SObject Maps
                                Map<Id, Account> accountMap = new Map<Id, Account>([SELECT Id FROM Account LIMIT 5]);
                                if (CollectionUtils.isNotEmpty(accountMap)) {
                                    // Process account map
                                }
   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                        https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 04 February 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/
public class CollectionUtils {
  /**
   * Checks if a object is null or empty using generics
   * @param field1 The object to validate
   * @return Boolean True if the object is null or empty, false otherwise
   */
  public static Boolean isNullOrBlank(Object field1) {
    // Check if either field is null
    if (field1 == null) {
      return true;
    }

    // Convert to string and check if empty
    String str1 = String.valueOf(field1).trim();

    return String.isBlank(str1);
  }

  /**
   * Checks if a List is null or empty using generics
   * @param listToCheck The List to validate
   * @return Boolean True if the List is null or empty, false otherwise
   */
  public static Boolean isEmpty(List<Object> listToCheck) {
    return listToCheck == null || listToCheck.isEmpty();
  }

  /**
   * Specific method for checking List<Id>
   * @param listToCheck The List of Ids to validate
   * @return Boolean True if the List is null or empty, false otherwise
   */
  public static Boolean isEmpty(List<Id> listToCheck) {
    return listToCheck == null || listToCheck.isEmpty();
  }

  /**
   * Generic method for checking any typed List
   * @param listToCheck The List to validate
   * @return Boolean True if the List is null or empty, false otherwise
   */
  public static Boolean isEmpty(List<SObject> listToCheck) {
    return listToCheck == null || listToCheck.isEmpty();
  }

  /**
   * Checks if a List is not null and contains elements
   * @param listToCheck The List to validate
   * @return Boolean True if the List contains elements, false if null or empty
   */
  public static Boolean isNotEmpty(List<Object> listToCheck) {
    return listToCheck != null && !listToCheck.isEmpty();
  }

  /**
   * Specific method for checking if List<Id> is not empty
   * @param listToCheck The List of Ids to validate
   * @return Boolean True if the List contains elements, false if null or empty
   */
  public static Boolean isNotEmpty(List<Id> listToCheck) {
    return listToCheck != null && !listToCheck.isEmpty();
  }

  /**
   * Generic method for checking if any typed List is not empty
   * @param listToCheck The List to validate
   * @return Boolean True if the List contains elements, false if null or empty
   */
  public static Boolean isNotEmpty(List<SObject> listToCheck) {
    return listToCheck != null && !listToCheck.isEmpty();
  }

  /**
   * Checks if a Set is null or empty
   * @param setToCheck The Set to validate
   * @return Boolean True if the Set is null or empty, false otherwise
   */
  public static Boolean isEmpty(Set<Object> setToCheck) {
    return setToCheck == null || setToCheck.isEmpty();
  }

  /**
   * Specific method for checking Set<Id>
   * @param setToCheck The Set of Ids to validate
   * @return Boolean True if the Set is null or empty, false otherwise
   */
  public static Boolean isEmpty(Set<Id> setToCheck) {
    return setToCheck == null || setToCheck.isEmpty();
  }

  /**
   * Specific method for checking List<String>
   * @param listToCheck The List of Strings to validate
   * @return Boolean True if the List is null or empty, false otherwise
   */
  public static Boolean isEmpty(List<String> listToCheck) {
    return listToCheck == null || listToCheck.isEmpty();
  }

  /**
   * Checks if a List<String> is not null and contains elements
   * @param listToCheck The List to validate
   * @return Boolean True if the List contains elements, false if null or empty
   */
  public static Boolean isNotEmpty(List<String> listToCheck) {
    return listToCheck != null && !listToCheck.isEmpty();
  }

  /**
   * Checks if a Set is not null and contains elements
   * @param setToCheck The Set to validate
   * @return Boolean True if the Set contains elements, false if null or empty
   */
  public static Boolean isNotEmpty(Set<Object> setToCheck) {
    return setToCheck != null && !setToCheck.isEmpty();
  }

  /**
   * Specific method for checking if Set<Id> is not empty
   * @param setToCheck The Set of Ids to validate
   * @return Boolean True if the Set contains elements, false if null or empty
   */
  public static Boolean isNotEmpty(Set<Id> setToCheck) {
    return setToCheck != null && !setToCheck.isEmpty();
  }

  /**
   * Checks if a Map is null or empty
   * @param mapToCheck The Map to validate
   * @return Boolean True if the Map is null or empty, false otherwise
   */
  public static Boolean isEmpty(Map<Object, Object> mapToCheck) {
    return mapToCheck == null || mapToCheck.isEmpty();
  }

  /**
   * Specific method for checking Map<Id, SObject>
   * @param mapToCheck The Map to validate
   * @return Boolean True if the Map is null or empty, false otherwise
   */
  public static Boolean isEmpty(Map<Id, SObject> mapToCheck) {
    return mapToCheck == null || mapToCheck.isEmpty();
  }

  /**
   * Specific method for checking Map<Id, String>
   * @param mapToCheck The Map to validate
   * @return Boolean True if the Map is null or empty, false otherwise
   */
  public static Boolean isEmpty(Map<Id, String> mapToCheck) {
    return mapToCheck == null || mapToCheck.isEmpty();
  }

  /**
   * Checks if a Map is not null and contains elements
   * @param mapToCheck The Map to validate
   * @return Boolean True if the Map contains elements, false if null or empty
   */
  public static Boolean isNotEmpty(Map<Object, Object> mapToCheck) {
    return mapToCheck != null && !mapToCheck.isEmpty();
  }

  /**
   * Specific method for checking if Map<Id, SObject> is not empty
   * @param mapToCheck The Map to validate
   * @return Boolean True if the Map contains elements, false if null or empty
   */
  public static Boolean isNotEmpty(Map<Id, SObject> mapToCheck) {
    return mapToCheck != null && !mapToCheck.isEmpty();
  }

  /**
   * Specific method for checking if Map<Id, String> is not empty
   * @param mapToCheck The Map to validate
   * @return Boolean True if the Map contains elements, false if null or empty
   */
  public static Boolean isNotEmpty(Map<Id, String> mapToCheck) {
    return mapToCheck != null && !mapToCheck.isEmpty();
  }
}
