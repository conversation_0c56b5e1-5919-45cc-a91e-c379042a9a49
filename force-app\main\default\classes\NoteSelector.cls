/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  25 Feb 2025

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> accountIds = new Set<Id>();
                       accountIds.add('001O300000FGc3tIAD');
                       accountIds.add('001O300000FGc3tIZZ');
                       AccountSelector accountSelector = new AccountSelector();
                       List<Account> accounts = accountSelector.selectAccountsByIds(accountIds);
                       List<Account> accounts = new AccountSelector().selectAccountsByIds(accountIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ck<PERSON>
   @ Last Modified On  : 25 Feb 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class NoteSelector {
  private String query;
  private String fromObject = ' FROM Note__c ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public NoteSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your account queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Name, Title__c, ParentRecordID__c, OwnerId, Owner.Name, IsPrivate__c, Content__c, ContentPlainText__c ,LastModifiedById, LastModifiedBy.Name, CreatedById, CreatedBy.Name, CreatedDate, LastModifiedDate, Account__c, Campaign__c, Case__c, Contact__c, Lead__c, Opportunity__c';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('NoteSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your accounts by a set of account ids
  public List<Note__c> selectNotesByIds(Set<Id> noteIds) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE Id IN :noteIds' +
      ' ORDER BY CreatedDate DESC' +
      this.queryLimit;
    //system.debug('selectNotesByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your accounts by a set of account parent Ids
  public List<Note__c> selectNotesByParentRecordIds(Set<Id> parentRecordIds) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE ParentRecordID__c IN :parentRecordIds' +
      ' ORDER BY CreatedDate DESC' +
      this.queryLimit;
    //system.debug('selectNotesByParentRecordIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Would continue to build queries and setters for everything you theoretically need.
}