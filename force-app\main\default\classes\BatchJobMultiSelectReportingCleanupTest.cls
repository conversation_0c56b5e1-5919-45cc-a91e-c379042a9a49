@isTest
private class BatchJobMultiSelectReportingCleanupTest {
  @testSetup
  static void setup() {
    // Create test MultiSelectReporting__c records
    List<MultiSelectReporting__c> testRecords = new List<MultiSelectReporting__c>();
    for (Integer i = 0; i < 10; i++) {
      testRecords.add(
        new MultiSelectReporting__c(
          Account__c = null,
          Contact__c = null,
          PicklistName__c = 'Application',
          PicklistNameAPI__c = 'Application__c',
          PicklistValue__c = 'Epigenetics'
        )
      );
    }
    insert testRecords;
  }

  @isTest
  static void testBatchJobExecution() {
    Test.startTest();
    BatchJobMultiSelectReportingCleanup batchJob = new BatchJobMultiSelectReportingCleanup();
    Id batchJobId = Database.executeBatch(batchJob, 200);
    Test.stopTest();

    // Verify results
    List<MultiSelectReporting__c> orphanedRecords = [
      SELECT Id, Name, Contact__c
      FROM MultiSelectReporting__c
      WHERE Contact__c = NULL AND Account__c = NULL
    ];

    System.assertEquals(0, orphanedRecords.size(), 'Expected no orphaned records to be found!');
  }

  @isTest
  static void testSchedulableExecution() {
    Test.startTest();
    String jobName = 'Batch Job-MultiSelectReportingCleanup-Daily_Test';
    String cronExp = '0 0 0 * * ?';
    System.schedule(jobName, cronExp, new BatchJobMultiSelectReportingCleanup());

    // Get the scheduled job
    CronTrigger ct = [
      SELECT Id, CronExpression, TimesTriggered, NextFireTime
      FROM CronTrigger
      WHERE CronJobDetail.Name = :jobName
    ];

    System.assertEquals(cronExp, ct.CronExpression, 'Cron expression should match');
    System.assertEquals(0, ct.TimesTriggered, 'Job should not have run yet');
    System.assertNotEquals(null, ct.NextFireTime, 'Next fire time should be set');

    Test.stopTest();
  }
}