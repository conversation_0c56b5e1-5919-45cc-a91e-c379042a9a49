<template>
  <lightning-card
    title="Admin Launch Pad"
    variant="Narrow"
    icon-name="standard:custom"
  >
    <ol class="slds-setup-assistant">
      <li class="slds-setup-assistant__item">
        <article class="slds-setup-assistant__step">
          <div class="slds-setup-assistant__step-summary">
            <div class="slds-media">
              <div
                class="slds-setup-assistant__step-summary-content slds-media__body"
              >
                <h3
                  class="slds-setup-assistant__step-summary-title slds-text-heading_small"
                >
                  Automation
                </h3>
                <p>
                  All areas related to Automation, declarative or otherwise.
                </p>
              </div>
              <div class="slds-media__figure slds-media__figure_reverse">
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Flows"
                  onclick={navigateToFlows}
                ></lightning-button>
                <!--<lightning-button variant="brand-outline" class="slds-m-left_x-small" label="Email Alerts" onclick={navigateToEmailAlerts}></lightning-button>-->
                <!--<lightning-button variant="brand-outline" class="slds-m-left_x-small" label="Email Services" onclick={navigateToEmailServices}></lightning-button>-->
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Custom Metadata"
                  onclick={navigateToCustomMetadata}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Debug Logs"
                  onclick={navigateToDebugLogs}
                ></lightning-button>
              </div>
            </div>
          </div>
        </article>
      </li>
      <li class="slds-setup-assistant__item">
        <article class="slds-setup-assistant__step">
          <div class="slds-setup-assistant__step-summary">
            <div class="slds-media">
              <div
                class="slds-setup-assistant__step-summary-content slds-media__body"
              >
                <h3
                  class="slds-setup-assistant__step-summary-title slds-text-heading_small"
                >
                  Navigate To Common Action Items
                </h3>
                <p>Common areas of functionality for an admin.</p>
              </div>
              <div class="slds-media__figure slds-media__figure_reverse">
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Object Manager"
                  onclick={navigateToObjectManager}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Inbound Changesets"
                  onclick={navigateToInboundChangeset}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Outbound Changesets"
                  onclick={navigateToOutboundChangeset}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Permission Sets"
                  onclick={navigateToPermissionSets}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Profiles"
                  onclick={navigateToProfiles}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Users"
                  onclick={navigateToUsers}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Groups"
                  onclick={navigateToGroups}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Queues"
                  onclick={navigateToQueues}
                ></lightning-button>
              </div>
            </div>
          </div>
        </article>
      </li>
      <li class="slds-setup-assistant__item">
        <article class="slds-setup-assistant__step">
          <div class="slds-setup-assistant__step-summary">
            <div class="slds-media">
              <div
                class="slds-setup-assistant__step-summary-content slds-media__body"
              >
                <h3
                  class="slds-setup-assistant__step-summary-title slds-text-heading_small"
                >
                  Navigate To Views
                </h3>
                <p>Common list views for core objects.</p>
              </div>
              <div class="slds-media__figure slds-media__figure_reverse">
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Account Recent list View"
                  onclick={navigateToAccountListView}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Opportunity Recent list View"
                  onclick={navigateToOpportunityListView}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Case Recent list View"
                  onclick={navigateToCaseListView}
                ></lightning-button>
              </div>
            </div>
          </div>
        </article>
      </li>
      <li class="slds-setup-assistant__item">
        <article class="slds-setup-assistant__step">
          <div class="slds-setup-assistant__step-summary">
            <div class="slds-media">
              <div
                class="slds-setup-assistant__step-summary-content slds-media__body"
              >
                <h3
                  class="slds-setup-assistant__step-summary-title slds-text-heading_small"
                >
                  Create New Records
                </h3>
                <p>Create new records for all core objects.</p>
              </div>
              <div class="slds-media__figure slds-media__figure_reverse">
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="New Account"
                  onclick={navigateToNewAccountPage}
                ></lightning-button>
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="New Opportunity"
                  onclick={navigateToNewOpportunityPage}
                ></lightning-button>
              </div>
            </div>
          </div>
        </article>
      </li>
      <li class="slds-setup-assistant__item">
        <article class="slds-setup-assistant__step">
          <div class="slds-setup-assistant__step-summary">
            <div class="slds-media">
              <div
                class="slds-setup-assistant__step-summary-content slds-media__body"
              >
                <h3
                  class="slds-setup-assistant__step-summary-title slds-text-heading_small"
                >
                  Home
                </h3>
                <p>Navigate to the Setup Home.</p>
              </div>
              <div class="slds-media__figure slds-media__figure_reverse">
                <lightning-button
                  variant="brand-outline"
                  class="slds-m-left_x-small"
                  label="Navigate to Setup Home"
                  onclick={navigateToHomePage}
                ></lightning-button>
              </div>
            </div>
          </div>
        </article>
      </li>
    </ol>
  </lightning-card>
</template>