/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  29 November 2024

   @ Description	:   The Service Layer Pattern is a very powerful tool that can help a team create a strong bias towards reusable code. If this concept is new to your team, the Service Layer Badge on Trailhead is a great place to start.
                        The underlying pattern is old and has a long background in many programming languages (including Java).
                        The recommended approach is to create a single service class for each Salesforce object for which you have code (“AccountService.cls”, “CampaignMemberService.cls”, etc). Within that class you create static methods which implement different bits of business logic. Your main design constraint is
                        to try and make the service methods themselves as reusable as possible. Keep them generic!

   @ Developer Notes  : AccountService accountService = new AccountService();

                        1) String accountName = accountService.getAccountName(accountId);
                        2) String accountName = new AccountService().getAccountName(accountId);


   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                    https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master
                    https://bluecanvas.io/blog/how-to-structure-and-manage-your-apex-codebase-for-scale-and-agility

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 29 November 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public inherited sharing class OpportunityContactRoleService {
    @TestVisible
    private static DmlHelper dmlHelper = new DmlHelper();

    @TestVisible
    private static OpportunityContactRoleSelector opportunityContactRoleSelector = new OpportunityContactRoleSelector();

    public List<OpportunityContactRole> getOpportunityContactRoles(Set<Id> opportunityIds) {
        List<OpportunityContactRole> oppConRoles = opportunityContactRoleSelector.selectOpportunityContactRoleByOppIds(opportunityIds);
        return oppConRoles;
    }
}