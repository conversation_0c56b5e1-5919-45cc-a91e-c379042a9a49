/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  31 May 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> accountIds = new Set<Id>();
                       accountIds.add('001O300000FGc3tIAD');
                       accountIds.add('001O300000FGc3tIZZ');
                       AccountSelector accountSelector = new AccountSelector();
                       List<Account> accounts = accountSelector.selectAccountsByIds(accountIds);
                       List<Account> accounts = new AccountSelector().selectAccountsByIds(accountIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ck<PERSON>
   @ Last Modified On  : 31 May 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class AccountSelector {
  private String query;
  private String fromObject = ' FROM Account ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public AccountSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your account queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Name, Type, Phone, Website, SalesChannel__c, SalesTeam__c, Owner.Name, ValidationStatus__c';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('AccountSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your accounts by a set of account ids
  public List<Account> selectAccountsByIds(Set<Id> accountIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id IN :accountIds' + this.queryLimit;
    //system.debug('selectAccountsByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your accounts by a set of account parent Ids
  public List<Account> selectAccountsByParentIds(Set<Id> accountParentIds) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE ParentId IN :accountParentIds' +
      this.queryLimit;
    //system.debug('selectAccountsByParentIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your accounts by a set of account names
  public List<Account> selectAccountsByName(Set<String> accountNames) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Name IN :accountNames' + this.queryLimit;
    //system.debug('selectAccountsByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  /**
   * Find accounts by exact name match
   * @param accountName The name to match exactly
   * @return List of Account records with exact name match
   */
  public List<Account> selectAccountsByExactName(String accountName) {
    if (String.isBlank(accountName)) {
      return new List<Account>();
    }

    AccountSelector selector = new AccountSelector();
    selector.setQueryLimit(1);
    selector.buildBaseQuery();

    String query =
      selector.query +
      selector.fromObject +
      'WHERE Name = :accountName' +
      selector.queryLimit;
    return Database.query(query);
  }

  /**
   * Find accounts by fuzzy name match using LIKE operator
   * @param accountName The name to match with LIKE operator
   * @return List of Account records with fuzzy name match
   */
  public List<Account> selectAccountsByFuzzyName(String accountName) {
    if (String.isBlank(accountName)) {
      return new List<Account>();
    }

    AccountSelector selector = new AccountSelector();
    selector.setQueryLimit(1);
    selector.buildBaseQuery();

    String fuzzyName = '%' + accountName + '%';
    String query =
      selector.query +
      selector.fromObject +
      'WHERE Name LIKE :fuzzyName ORDER BY Name' +
      selector.queryLimit;
    return Database.query(query);
  }

  //Would continue to build queries and setters for everything you theoretically need.
}