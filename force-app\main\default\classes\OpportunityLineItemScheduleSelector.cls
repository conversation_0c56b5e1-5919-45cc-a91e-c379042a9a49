/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  27 January 2025

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> opportunityLineItemScheduleIds = new Set<Id>();
                       opportunityLineItemScheduleIds.add('001O300000FGc3tIAD');
                       opportunityLineItemScheduleIds.add('001O300000FGc3tIZZ');
                       OpportunityLineItemScheduleSelector opportunityLineItemScheduleSelector = new OpportunityLineItemScheduleSelector();
                       List<OpportunityLineItemSchedule> opportunityLineItemSchedules = opportunityLineItemScheduleSelector.selectOpportunityLineItemSchedulesByIds(opportunityLineItemScheduleIds);
                       List<OpportunityLineItemSchedule> opportunityLineItemSchedules = new OpportunityLineItemScheduleSelector().selectOpportunityLineItemSchedulesByIds(opportunityLineItemScheduleIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 27 January 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/
//TODO: Add a method to select all OpportunityLineItemSchedules
public inherited sharing class OpportunityLineItemScheduleSelector {
  private String query;
  private String fromObject = ' FROM OpportunityLineItemSchedule ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public OpportunityLineItemScheduleSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your account queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, OpportunityLineItemId, ScheduleProbability__c, EstimatedQuantityPacks__c, SalesPricePerPack__c, Quantity, Revenue, EstimatedRevenue__c, WeightedRevenue__c, ScheduleDate, Description';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('OpportunityLineItemScheduleSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your OpportunityLineItemSchedules by a set of ids
  public List<OpportunityLineItemSchedule> selectOpportunityLineItemSchedulesByOLIIds(
    Set<Id> opportunityLineItemIds
  ) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE OpportunityLineItemId IN :opportunityLineItemIds' +
      this.queryLimit;
    //system.debug('selectOpportunityLineItemSchedulesByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }
}