/*
   How to execute via Developer Console
   //Define Map
   private Map<String, String> fieldsToUpdateMap = new Map<String, String>();
   fieldsToUpdateMap.put('Id','Id');
   fieldsToUpdateMap.put('Sales_Channel__c','SalesChannel__c');
   fieldsToUpdateMap.put('Market_Applications__c','MarketApplications__c');
   fieldsToUpdateMap.put('Primary_Segment__c','PrimarySegment__c');
   fieldsToUpdateMap.put('Primary_Subsegment__c','PrimarySubsegment__c');
   fieldsToUpdateMap.put('Samples_Year__c','SamplesYear__c');

   // Define your parameters
   String query = 'SELECT Id, Sales_Channel__c, SalesChannel__c, Market_Applications__c, MarketApplications__c, Primary_Segment__c, PrimarySegment__c, Primary_Subsegment__c, PrimarySubsegment__c, Samples_Year__c, SamplesYear__c FROM Account';

   // Instantiate and execute the batch job
   BatchJobSyncFieldData batchJob = new BatchJobSyncFieldData(query, fieldsToUpdateMap);
   ID batchProcessId = Database.executeBatch(batchJob, 200);

   // Log the batch job ID
   System.debug('Batch job started with ID: ' + batchProcessId);
 */

global class BatchJobSyncFieldData implements Database.Batchable<sObject>, Database.Stateful {
    private String query;
    private String objectName;
    private Map<String, String> fieldsToUpdateMap = new Map<String, String>();
    // Class-level variable to store information across batches
    private Integer totalRecordsProcessed = 0;
    private Integer totalRecordsProcessedSuccess = 0;
    private Integer totalRecordsProcessedFail = 0;
    private List<String> updatedRecordIds = new List<String>();
    private List<FailedRecord> failedRecords = new List<FailedRecord>();

    global BatchJobSyncFieldData(String query, Map<String, String> fieldsToUpdateMap) {
        this.query = query;
        this.fieldsToUpdateMap = fieldsToUpdateMap;
    }

    global Database.QueryLocator start(Database.BatchableContext BC) {
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, List<sObject> scope) {
        List<SObject> sObjectRecsToBeUpdated = new List<SObject>();
        Schema.SObjectType targetType;

        for (sObject record : scope) {
            // Serialize to JSON
            String jsonString = JSON.serialize(record);
            // Convert sObject to a map
            Map<String, Object> fieldDetailsMap = (Map<String, Object>)JSON.deserializeUntyped(jsonString);
            // If you don't know the sObject type in advance
            String sObjectTypeString = ((Map<String, Object>)fieldDetailsMap.get('attributes')).get('type').toString();
            // Determine the SObject e.g. Account
            targetType = Schema.getGlobalDescribe().get(sObjectTypeString);

            //Clear up Map by removing attributes leaving only field data
            fieldDetailsMap.remove('attributes');

            // Instantiate an sObject with the type passed in as an argument at run time.
            Sobject sObjectRecord = targetType.newSObject();
            //Loop over fieldDetailsMap keyset (i.e. fields queried and returned in scope)
            for (String fieldName: fieldDetailsMap.keySet()) {
                // Iterate over key-value pairs of fieldsToUpdateMap passed into batch job (This map is key: fieldAPIName Master value: fieldAPIName Slave)
                for (String key : fieldsToUpdateMap.keySet()) {
                    if (fieldName == key) {
                        String fieldToBeSynced = fieldsToUpdateMap.get(key);
                        sObjectRecord.put(fieldToBeSynced, fieldDetailsMap.get(fieldName));
                    }
                }
            }

            sObjectRecsToBeUpdated.add(sObjectRecord);
        }
        // Perform updates
        if (!sObjectRecsToBeUpdated.isEmpty()) {
            try {
                Database.SaveResult[] results = Database.update(sObjectRecsToBeUpdated, false);
                // iterate over the list of returned results
                for (Database.SaveResult result : results) {
                    if (result.isSuccess()) {
                        totalRecordsProcessed++;
                        totalRecordsProcessedSuccess++;
                        updatedRecordIds.add(result.getId());
                    } else {
                        for (Database.Error error : result.getErrors()) {
                            String recordId = result.getId();
                            String errorMessage = error.getMessage();
                            totalRecordsProcessed++;
                            totalRecordsProcessedFail++;
                            failedRecords.add(new FailedRecord(result.getId(), errorMessage));
                        }
                    }
                }
            } catch (DMLException e) {
            }
        }
    }

    global void finish(Database.BatchableContext BC) {
        // Prepare email content
        String emailBody = 'The batch job to copy fields has completed.\n\n';
        emailBody += 'Total records processed: ' + totalRecordsProcessed + '\n';
        emailBody += 'Total records updated SUCCESS: ' + totalRecordsProcessedSuccess + '\n';
        emailBody += 'Total records updated FAILED: ' + totalRecordsProcessedFail + '\n';

        // Create CSV content
        String csvContent = 'Record ID,Error Message\n';
        for (FailedRecord fr : failedRecords) {
            csvContent += fr.recordId + ',"' + fr.errorMessage.replace('"', '""') + '"\n';
        }

        // Create email attachment
        Messaging.EmailFileAttachment attachment = new Messaging.EmailFileAttachment();
        attachment.setFileName('FailedRecords.csv');
        attachment.setBody(Blob.valueOf(csvContent));
        attachment.setContentType('text/csv');

        // Send email with attachment
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        String[] toAddresses = new String[] {'<EMAIL>'};
        mail.setToAddresses(toAddresses);
        mail.setSubject('Batch Job Complete -> BatchJobSyncFieldData: Results');
        mail.setPlainTextBody(emailBody);
        mail.setFileAttachments(new Messaging.EmailFileAttachment[] { attachment });

        Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
    }

    // Inner class to store failed record information
    private class FailedRecord {
        public String recordId;
        public String errorMessage;

        public FailedRecord(String recordId, String errorMessage) {
            this.recordId = recordId;
            this.errorMessage = errorMessage;
        }
    }
}