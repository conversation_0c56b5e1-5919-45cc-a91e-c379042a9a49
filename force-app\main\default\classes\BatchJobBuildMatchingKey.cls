/********************************************************************************************

   @ Func Area	:  Apex <PERSON>ch Job

   @ Author	:  <PERSON>

   @ Date		:  08 August 2024

   @ Description	:     Batch Job (1/3)
                        This Batch job will query for all accounts and then create a unique matching ket by concatenating the following fields
                        SalesChannel__c, SalesTeam__c, ShippingCountry, ShippingCountryCode, ShippingState, ShippingStateCode, ShippingPostalCode,

                        The same process will occur against the Custom Object SalesTerritories__c which has all the territory assingment data used to create the Account unique matching key above.

                        If a match is found then the Account will be updated with a lookup relationship to the SalesTerritories__c record

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

                            How to execute via Developer Console:

                            // Define your parameters
                            String query = 'SELECT Id, SalesChannel__c, SalesTeam__c, ShippingCountry, ShippingCountryCode, ShippingState, ShippingStateCode, ShippingPostalCode, SalesTerritories__c FROM Account';

                            // Instantiate and execute the batch job
                            BatchJobBuildMatchingKey batchJob = new BatchJobBuildMatchingKey(query);
                            ID batchProcessId = Database.executeBatch(batchJob, 200);

                            // Log the batch job ID
                            System.debug('Batch job started with ID: ' + batchProcessId);

   @ Test Class	:   BatchJobBuildMatchingKeyTest

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 30 October 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
global class BatchJobBuildMatchingKey implements Database.Batchable<sObject>, Database.Stateful {
  private String query;
  private String objectName;
  // Class-level variable to store information across batches
  private List<Account> accsProcessed = new List<Account>();
  private Integer totalRecordsProcessed = 0;

  global BatchJobBuildMatchingKey(String query) {
    this.query = query;
    //System.debug('BatchJobBuildMatchingKey Query -> ' + query);
  }

  global Database.QueryLocator start(Database.BatchableContext BC) {
    return Database.getQueryLocator(query);
  }

  global void execute(Database.BatchableContext BC, List<sObject> scope) {
    List<SObject> sObjectRecsToBeUpdated = new List<SObject>();
    List<Account> accsToBeUpdated = new List<Account>();
    Schema.SObjectType targetType;

    for (sObject record : scope) {
      Account acc = (Account) record;
      accsProcessed.add(acc);
      accsToBeUpdated.add(acc);
    }

    if (accsToBeUpdated.size() > 0) {
      totalRecordsProcessed = accsProcessed.size();
      TA_Account_BuildMatchingKey classInstance = new TA_Account_BuildMatchingKey();
      classInstance.buildSalesTerritoryMatchingKey(accsToBeUpdated);
    }
  }

  global void finish(Database.BatchableContext BC) {
    // Prepare email content
    String emailBody = 'Batch Job: BatchJobBuildMatchingKey has completed.\n\n';
    emailBody += 'Total records processed: ' + totalRecordsProcessed + '\n';

    // Send email
    Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();

    System_Configuration__mdt specificRecord = [
      SELECT BatchJobNotifications__c
      FROM System_Configuration__mdt
      WHERE DeveloperName = 'GlobalSystemConfig'
      LIMIT 1
    ];

    List<String> toAddresses = specificRecord.BatchJobNotifications__c.split(
      ','
    );

    mail.setToAddresses(toAddresses);
    mail.setSubject('Batch Job BatchJobBuildMatchingKey Complete (1/3)');
    mail.setPlainTextBody(emailBody);

    Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{ mail });

    /*********************************************************************** */

    //Now Chain the second Batch Job

    //Last version of query
    /*
    String query =
    'SELECT Id, SalesChannel__c, SalesTeam__c, ' +
    'ShippingCountry, ShippingCountryCode, ShippingState, ' +
    'ShippingStateCode, ShippingPostalCode, SalesTerritories__c, ' +
    'SalesTerritoryMatchingKey__c ' +
    'FROM Account ' +
    'WHERE ChildCount__c = 0 ' +
    'AND IsExcludedFromRealign = FALSE ' +
    'AND SalesTeam__c != \'CGS\'';
    */

    //New version of query
    String query =
      'SELECT Id, Name, SalesChannel__c, SalesTeam__c, ' +
      'ShippingCountry, ShippingCountryCode, ShippingState, ' +
      'ShippingStateCode, ShippingPostalCode, SalesTerritories__c, ' +
      'SalesTerritoryMatchingKey__c, RecommendedSalesTerritory__c ' +
      'FROM Account ' +
      'WHERE IsExcludedFromRealign = FALSE ' +
      'AND (ChildCount__c = 0 ' +
      'OR ChildCount__c = null)';

    //Kick off the second batch job
    BatchJobTerritoryAssignment secondBatch = new BatchJobTerritoryAssignment(
      query
    );
    ID batchJobId = Database.executeBatch(secondBatch, 200);

    /*
    // Log the batch job ID
    System.debug(
      'Batch Job BatchJobBuildMatchingKey (2/3) started with ID: ' + batchJobId
    );
    */
  }
}