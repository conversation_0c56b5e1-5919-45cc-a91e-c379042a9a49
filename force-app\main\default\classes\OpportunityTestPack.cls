/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  27 November 2024

   @ Description	:   A test class containing test methods for apex automation on the Opportunity Object

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   AccountSelector

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 27 November 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public class OpportunityTestPack {
  /**
   * ---------------  Test Triggers: OpportunityTrigger ---------------
   */
  //Test OpportunityTrigger
  @isTest
  static void test_OpportunityTrigger() {
    Test.startTest();
    List<Opportunity> newList = new List<Opportunity>();

    newList.add(
      new Opportunity(
        Name = 'ACME Enterprises',
        StageName = '4) Negotiation',
        CloseDate = Date.today()
      )
    );

    insert newList;
    Test.stopTest();
  }

  //Test OpportunityLineItemTrigger
  @isTest
  static void test_OpportunityLineItemTrigger() {
    Test.startTest();
    List<OpportunityLineItem> newList = new List<OpportunityLineItem>();

    //Create a new Opportunity
    Opportunity opp = new Opportunity(
      Name = 'ACME Enterprises',
      StageName = '4) Negotiation',
      CloseDate = Date.today()
    );
    insert opp;

    // Create the standard pricebook
    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();
    // Create a product
    Product2 prod = PricebookTestUtils.createProduct('Test Product');

    // Create standard price for the product
    PricebookEntry standardPbe = PricebookTestUtils.createStandardPricebookEntry(
      prod,
      100.00
    );

    newList.add(
      new OpportunityLineItem(
        OpportunityId = opp.Id,
        PricebookEntryId = standardPbe.Id,
        Quantity = 1,
        UnitPrice = 100
      )
    );

    insert newList;
    Test.stopTest();
  }

  //Test OpportunityLineItemScheduleTrigger
  @isTest
  static void test_OpportunityLineItemScheduleTrigger() {
    Test.startTest();
    List<OpportunityLineItemSchedule> newList = new List<OpportunityLineItemSchedule>();

    //Create a new Opportunity
    Opportunity opp = new Opportunity(
      Name = 'ACME Enterprises',
      StageName = '4) Negotiation',
      CloseDate = Date.today()
    );
    insert opp;

    // Create the standard pricebook
    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();
    // Create a product
    Product2 prod = PricebookTestUtils.createProduct('Test Product');

    // Create standard price for the product
    PricebookEntry standardPbe = PricebookTestUtils.createStandardPricebookEntry(
      prod,
      100.00
    );

    OpportunityLineItem oli = new OpportunityLineItem(
      OpportunityId = opp.Id,
      PricebookEntryId = standardPbe.Id,
      Quantity = 1,
      UnitPrice = 100,
      SalesPricePerPack__c = 100,
      Quantity__c = 1
    );
    insert oli;

    newList.add(
      new OpportunityLineItemSchedule(
        OpportunityLineItemId = oli.Id,
        ScheduleDate = Date.today(),
        Type = 'Quantity',
        Quantity = 1
      )
    );

    insert newList;
    Test.stopTest();
  }

  /**
   * ---------------  Test Service Classes ---------------
   */

  //Test OpportunityService class
  @isTest
  static void test_OpportunityService() {
    Test.startTest();
    // Given
    Id validOppId = MockerUtils.generateId(Opportunity.SObjectType);

    Opportunity expectedOpp = (Opportunity) MockerUtils.updateObjectState(
      new Opportunity(Id = validOppId),
      new Map<String, Object>{ 'Id' => validOppId }
    );

    List<Opportunity> oppToInsert = new List<Opportunity>{ expectedOpp };

    List<Opportunity> oppToUpdate = new List<Opportunity>{ expectedOpp };

    Mocker mocker = Mocker.startStubbing();

    DmlHelper dmlHelperMock = (DmlHelper) mocker.mock(DmlHelper.class);

    dmlHelperMock.insertObjects(oppToInsert, 'Opportunity Service');
    Mocker.MethodRecorder insertObjectsRec = mocker.when()
      .withAnyValues()
      .getMethodRecorder();

    dmlHelperMock.updateObjects(oppToUpdate, 'Opportunity Service');
    Mocker.MethodRecorder updateObjectsRec = mocker.when()
      .withAnyValues()
      .getMethodRecorder();

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    OpportunityService.dmlHelper = dmlHelperMock;

    // When 2
    //test method public void createOpportunities(List<Opportunity> opportunitiesToInsert, String Source) on OpportunityService
    new OpportunityService()
      .createOpportunities(
        oppToInsert,
        'OpportunityTestPack.test_OpportunityService'
      );

    // Then 2
    System.assertEquals(1, insertObjectsRec.getCallsCount());

    // When 3
    //test method public void updateOpportunities(List<Opportunity> opportunitiesToUpdate, String Source) on OpportunityService
    new OpportunityService()
      .updateOpportunities(
        oppToUpdate,
        'OpportunityTestPack.test_OpportunityService'
      );

    // Then 3
    System.assertEquals(1, updateObjectsRec.getCallsCount());

    Test.stopTest();
  }

  //Test OpportunityLineItemService class
  @isTest
  static void test_OpportunityLineItemService() {
    Test.startTest();
    // Given
    Id validOppId = MockerUtils.generateId(Opportunity.SObjectType);
    Id validOliId = MockerUtils.generateId(OpportunityLineItem.SObjectType);

    OpportunityLineItem expectedOli = (OpportunityLineItem) MockerUtils.updateObjectState(
      new OpportunityLineItem(Id = validOliId, OpportunityId = validOppId),
      new Map<String, Object>{ 'OpportunityId' => validOppId }
    );

    List<OpportunityLineItem> oliToInsert = new List<OpportunityLineItem>{
      expectedOli
    };

    List<OpportunityLineItem> oliToUpdate = new List<OpportunityLineItem>{
      expectedOli
    };

    Mocker mocker = Mocker.startStubbing();

    DmlHelper dmlHelperMock = (DmlHelper) mocker.mock(DmlHelper.class);

    dmlHelperMock.insertObjects(oliToInsert, 'OpportunityLineItem Service');
    Mocker.MethodRecorder insertObjectsRec = mocker.when()
      .withAnyValues()
      .getMethodRecorder();

    dmlHelperMock.updateObjects(oliToUpdate, 'OpportunityLineItem Service');
    Mocker.MethodRecorder updateObjectsRec = mocker.when()
      .withAnyValues()
      .getMethodRecorder();

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    OpportunityLineItemService.dmlHelper = dmlHelperMock;

    // When 2
    //test method public void createOpportunityLineItems(List<OpportunityLineItem> opportunityLineItemsToInsert, String Source) on OpportunityLineItemService
    new OpportunityLineItemService()
      .createOpportunityLineItems(
        oliToInsert,
        'OpportunityLineItemTestPack.test_OpportunityLineItemService'
      );

    // Then 2
    System.assertEquals(1, insertObjectsRec.getCallsCount());

    // When 3
    //test method public void createOpportunityLineItems(List<OpportunityLineItem> opportunityLineItemsToInsert, String Source) on OpportunityLineItemService
    new OpportunityLineItemService()
      .updateOpportunityLineItems(
        oliToUpdate,
        'OpportunityLineItemTestPack.test_OpportunityLineItemService'
      );

    // Then 3
    System.assertEquals(1, updateObjectsRec.getCallsCount());

    Test.stopTest();
  }

  /**
   * ---------------  Test Selector Classes ---------------
   */

  //Test OpportunityLineItemSelector class
  @isTest
  static void test_OpportunityLineItemSelector() {
    OpportunityLineItemSelector opportunityLineItemSelector = new OpportunityLineItemSelector();
    Integer queryLimit = 100;
    Set<Id> oppIds = new Set<Id>{
      MockerUtils.generateId(Opportunity.SObjectType),
      MockerUtils.generateId(Opportunity.SObjectType)
    };
    Set<Id> oliIds = new Set<Id>{
      MockerUtils.generateId(OpportunityLineItem.SObjectType),
      MockerUtils.generateId(OpportunityLineItem.SObjectType)
    };
    Test.startTest();
    opportunityLineItemSelector.setQueryLimit(queryLimit);
    opportunityLineItemSelector.selectOpportunityLineItemsByIds(oliIds);
    opportunityLineItemSelector.selectOpportunityLineItemsByOppId(
      new List<Id>(oppIds)[0]
    );
    opportunityLineItemSelector.selectOpportunityLineItemsByOppIds(oppIds);
    Test.stopTest();
  }

  //Test OpportunitySelector class
  @isTest
  static void test_OpportunitySelector() {
    OpportunitySelector opportunitySelector = new OpportunitySelector();
    Integer queryLimit = 100;
    Set<Id> oppIds = new Set<Id>{
      MockerUtils.generateId(Opportunity.SObjectType),
      MockerUtils.generateId(Opportunity.SObjectType)
    };
    Test.startTest();
    opportunitySelector.setQueryLimit(queryLimit);
    opportunitySelector.selectOpportunitiesByIds(oppIds);
    opportunitySelector.selectOpportunityById(new List<Id>(oppIds)[0]);
    Test.stopTest();
  }

  //Test OpportunityLineItemScheduleSelector class
  @isTest
  static void test_OpportunityLineItemScheduleSelector() {
    OpportunityLineItemScheduleSelector opportunityLineItemScheduleSelector = new OpportunityLineItemScheduleSelector();
    Integer queryLimit = 100;

    Set<Id> oliIds = new Set<Id>{
      MockerUtils.generateId(OpportunityLineItem.SObjectType),
      MockerUtils.generateId(OpportunityLineItem.SObjectType)
    };
    Test.startTest();
    opportunityLineItemScheduleSelector.setQueryLimit(queryLimit);
    opportunityLineItemScheduleSelector.selectOpportunityLineItemSchedulesByOLIIds(
      oliIds
    );
    Test.stopTest();
  }

  //Test PricebookSelector class
  @isTest
  static void test_PricebookSelector() {
    PricebookSelector pricebookSelector = new PricebookSelector();
    Integer queryLimit = 100;
    Test.startTest();
    pricebookSelector.setQueryLimit(queryLimit);
    pricebookSelector.selectAllPricebook2();
    Test.stopTest();
  }

  //Test OpportunityContactRoleSelector class
  @isTest
  static void test_OpportunityContactRoleSelector() {
    OpportunityContactRoleSelector opportunityContactRoleSelector = new OpportunityContactRoleSelector();
    Integer queryLimit = 100;
    Set<Id> oppIds = new Set<Id>{
      MockerUtils.generateId(Opportunity.SObjectType),
      MockerUtils.generateId(Opportunity.SObjectType)
    };
    Test.startTest();
    opportunityContactRoleSelector.setQueryLimit(queryLimit);
    opportunityContactRoleSelector.selectOpportunityContactRoleByOppIds(oppIds);
    Test.stopTest();
  }

  /**
   * ---------------  Test Trigger Actions ---------------
   */

  @IsTest
  private static void test_TA_Opportunity_FastUpdates_beforeUpdate() {
    List<Opportunity> newList = new List<Opportunity>();
    List<Opportunity> oldList = new List<Opportunity>();

    // Create the standard pricebook
    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();

    // Create a custom pricebook
    Pricebook2 customPb = PricebookTestUtils.createCustomPricebook(
      'Example Price Book'
    );

    //generate fake Id
    Id fakeOpportunityId = TestFactory.getFakeId(Opportunity.SObjectType);
    Id fakeOpportunityId2 = TestFactory.getFakeId(Opportunity.SObjectType);

    newList.add(
      new Opportunity(
        Id = fakeOpportunityId,
        Name = 'My Opp 1',
        StageName = 'Evaluation',
        LossReason__c = 'Pricing',
        LossReasonDetail__c = 'My reason for the loss',
        Description = 'Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33 of "de Finibus Bonorum et Malorum" (The Extremes of Good and Evil) by Cicero, written in 45 BC. This book is a treatise on the theory of ethics, very popular during the Renaissance. The first line of Lorem Ipsum, "Lorem ipsum dolor sit amet..", comes from a line in section 1.10.32. The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.'
      )
    );

    newList.add(
      new Opportunity(
        Id = fakeOpportunityId2,
        Name = 'My Opp 2',
        StageName = 'Evaluation',
        LossReason__c = 'Pricing',
        LossReasonDetail__c = 'My reason for the loss',
        Description = 'Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33 of "de Finibus Bonorum et Malorum" (The Extremes of Good and Evil) by Cicero, written in 45 BC. This book is a treatise on the theory of ethics, very popular during the Renaissance. The first line of Lorem Ipsum, "Lorem ipsum dolor sit amet..", comes from a line in section 1.10.32. The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.'
      )
    );

    oldList.add(
      new Opportunity(
        Id = fakeOpportunityId,
        Name = 'My Opp 1',
        StageName = 'Qualified',
        LossReason__c = 'Pricing',
        LossReasonDetail__c = 'My reason for the loss',
        Description = ''
      )
    );

    oldList.add(
      new Opportunity(
        Id = fakeOpportunityId2,
        Name = 'My Opp 2',
        StageName = 'Qualified',
        LossReason__c = 'Pricing',
        LossReasonDetail__c = 'My reason for the loss',
        Description = ''
      )
    );

    new TA_Opportunity_FastUpdates().beforeUpdate(newList, oldList);
  }

  @IsTest
  private static void test_TA_Opportunity_FastUpdates_validateOpportunityContactRoles() {
    List<Opportunity> newList = new List<Opportunity>();
    List<Opportunity> oldList = new List<Opportunity>();

    // Create the standard pricebook
    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();

    // Create a custom pricebook
    Pricebook2 customPb = PricebookTestUtils.createCustomPricebook(
      'Example Price Book'
    );

    //generate fake Id
    Id fakeOpportunityId = TestFactory.getFakeId(Opportunity.SObjectType);
    Id fakeOpportunityId2 = TestFactory.getFakeId(Opportunity.SObjectType);

    newList.add(
      new Opportunity(
        Id = fakeOpportunityId,
        Name = 'My Opp 1',
        StageName = 'Qualified',
        LossReason__c = 'Pricing',
        LossReasonDetail__c = 'My reason for the loss'
      )
    );

    newList.add(
      new Opportunity(
        Id = fakeOpportunityId2,
        Name = 'My Opp 2',
        StageName = 'Qualified',
        LossReason__c = 'Pricing',
        LossReasonDetail__c = 'My reason for the loss'
      )
    );

    oldList.add(
      new Opportunity(
        Id = fakeOpportunityId,
        Name = 'My Opp 1',
        StageName = 'Evaluation',
        LossReason__c = 'Pricing',
        LossReasonDetail__c = 'My reason for the loss'
      )
    );

    oldList.add(
      new Opportunity(
        Id = fakeOpportunityId2,
        Name = 'My Opp 2',
        StageName = 'Evaluation',
        LossReason__c = 'Pricing',
        LossReasonDetail__c = 'My reason for the loss'
      )
    );

    new TA_Opportunity_FastUpdates().beforeUpdate(newList, oldList);
  }

  @IsTest
  private static void test_TA_Opportunity_ClearPriceBook() {
    List<Opportunity> newList = new List<Opportunity>();
    List<Opportunity> oldList = new List<Opportunity>();

    //generate fake Id
    Id fakeOpportunityId = TestFactory.getFakeId(Opportunity.SObjectType);

    Opportunity oldOpp = (Opportunity) MockerUtils.updateObjectState(
      new Opportunity(Id = fakeOpportunityId),
      new Map<String, Object>{ 'HasOpportunityLineItem' => true }
    );

    Opportunity newOpp = (Opportunity) MockerUtils.updateObjectState(
      new Opportunity(Id = fakeOpportunityId),
      new Map<String, Object>{ 'HasOpportunityLineItem' => false }
    );

    newList.add(newOpp);
    oldList.add(oldOpp);

    new TA_Opportunity_ClearPriceBook().afterUpdate(newList, oldList);
  }

  //TODO: Add test for TA_OppLineItemSchedule_FastUpdates
  @IsTest
  private static void test_TA_OppLineItemSchedule_FastUpdates() {
    List<OpportunityLineItemSchedule> newList = new List<OpportunityLineItemSchedule>();
    List<OpportunityLineItemSchedule> oldList = new List<OpportunityLineItemSchedule>();
    List<OpportunityLineItemSchedule> queriedList = new List<OpportunityLineItemSchedule>();

    // Create the standard pricebook
    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();

    // Create a custom pricebook
    Pricebook2 customPb = PricebookTestUtils.createCustomPricebook(
      'Example Price Book'
    );

    //generate fake Id
    Id fakeOpportunityId = TestFactory.getFakeId(Opportunity.SObjectType);
    Id fakeOpportunityId2 = TestFactory.getFakeId(Opportunity.SObjectType);
    Id fakeOliId = TestFactory.getFakeId(OpportunityLineItem.SObjectType);
    Id fakeOliId2 = TestFactory.getFakeId(OpportunityLineItem.SObjectType);
    Id fakePriceBookEntry = TestFactory.getFakeId(PricebookEntry.SObjectType);
    Id fakePriceBookEntry2 = TestFactory.getFakeId(PricebookEntry.SObjectType);
    Id fakeOliScheduleId = TestFactory.getFakeId(
      OpportunityLineItemSchedule.SObjectType
    );
    Id fakeOliScheduleId1 = TestFactory.getFakeId(
      OpportunityLineItemSchedule.SObjectType
    );
    Id fakeOliScheduleId2 = TestFactory.getFakeId(
      OpportunityLineItemSchedule.SObjectType
    );

    Set<Id> mySet = new Set<Id>();
    List<OpportunityLineItem> olisQueried = new List<OpportunityLineItem>();

    olisQueried.add(
      new OpportunityLineItem(
        Id = fakeOliId,
        OpportunityId = fakeOpportunityId2,
        PricebookEntryId = fakePriceBookEntry2,
        Quantity = 1,
        UnitPrice = 100,
        CYUnweightedRevenue__c = 100,
        CYWeightedRevenue__c = 100,
        NextCYUnweightedRevenue__c = 100,
        NextCYWeightedRevenue__c = 100
      )
    );

    queriedList.add(
      new OpportunityLineItemSchedule(
        OpportunityLineItemId = fakeOliId,
        ScheduleDate = Date.today(),
        SalesPricePerPack__c = 100,
        EstimatedQuantityPacks__c = 100,
        ScheduleProbability__c = 100,
        Type = 'Quantity',
        Quantity = 1
      )
    );

    newList.add(
      new OpportunityLineItemSchedule(
        OpportunityLineItemId = fakeOliId,
        ScheduleDate = Date.today(),
        SalesPricePerPack__c = 200,
        EstimatedQuantityPacks__c = 200,
        Revenue = 200,
        ScheduleProbability__c = 0,
        Type = 'Quantity',
        Quantity = 1
      )
    );

    newList.add(
      new OpportunityLineItemSchedule(
        OpportunityLineItemId = fakeOliId,
        ScheduleDate = Date.today().addYears(1),
        SalesPricePerPack__c = 200,
        EstimatedQuantityPacks__c = 200,
        Revenue = 200,
        ScheduleProbability__c = 100,
        Type = 'Quantity',
        Quantity = 1
      )
    );

    oldList.add(
      new OpportunityLineItemSchedule(
        OpportunityLineItemId = fakeOliId,
        ScheduleDate = Date.today(),
        SalesPricePerPack__c = 100,
        EstimatedQuantityPacks__c = 100,
        ScheduleProbability__c = 10,
        Type = 'Quantity',
        Quantity = 1
      )
    );

    oldList.add(
      new OpportunityLineItemSchedule(
        OpportunityLineItemId = fakeOliId,
        ScheduleDate = Date.today().addYears(1),
        SalesPricePerPack__c = 100,
        EstimatedQuantityPacks__c = 100,
        ScheduleProbability__c = 50,
        Type = 'Quantity',
        Quantity = 1
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    OpportunityLineItemSelector opportunityLineItemSelectorMock = (OpportunityLineItemSelector) mocker.mock(
      OpportunityLineItemSelector.class
    );

    mocker.when(
        opportunityLineItemSelectorMock.selectOpportunityLineItemsByIds(mySet)
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(olisQueried);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one

    TA_OppLineItemSchedule_FastUpdates.opportunityLineItemSelector = opportunityLineItemSelectorMock;

    new TA_OppLineItemSchedule_FastUpdates().beforeInsert(newList);

    newList[0].Id = fakeOliScheduleId1;
    newList[1].Id = fakeOliScheduleId2;
    oldList[0].Id = fakeOliScheduleId1;
    oldList[1].Id = fakeOliScheduleId2;

    new TA_OppLineItemSchedule_FastUpdates().beforeUpdate(newList, oldList);
  }

  @IsTest
  private static void test_TA_OppLineItem_FinanceCalcs_afterUpdate() {
    List<OpportunityLineItem> newList = new List<OpportunityLineItem>();
    List<OpportunityLineItem> oldList = new List<OpportunityLineItem>();

    // Create the standard pricebook
    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();

    // Create a custom pricebook
    Pricebook2 customPb = PricebookTestUtils.createCustomPricebook(
      'Example Price Book'
    );

    //generate fake Id
    Id fakeOpportunityId = TestFactory.getFakeId(Opportunity.SObjectType);
    Id fakeOpportunityId2 = TestFactory.getFakeId(Opportunity.SObjectType);
    Id fakeOliId = TestFactory.getFakeId(OpportunityLineItem.SObjectType);
    Id fakeOliId2 = TestFactory.getFakeId(OpportunityLineItem.SObjectType);
    Id fakePriceBookEntry = TestFactory.getFakeId(PricebookEntry.SObjectType);
    Id fakePriceBookEntry2 = TestFactory.getFakeId(PricebookEntry.SObjectType);

    Set<Id> mySet = new Set<Id>();

    newList.add(
      new OpportunityLineItem(
        Id = fakeOliId,
        OpportunityId = fakeOpportunityId,
        PricebookEntryId = fakePriceBookEntry,
        Quantity = 1,
        UnitPrice = 100,
        CYUnweightedRevenue__c = 200,
        CYWeightedRevenue__c = 200,
        NextCYUnweightedRevenue__c = 300,
        NextCYWeightedRevenue__c = 300
      )
    );

    newList.add(
      new OpportunityLineItem(
        Id = fakeOliId2,
        OpportunityId = fakeOpportunityId2,
        PricebookEntryId = fakePriceBookEntry2,
        Quantity = 1,
        UnitPrice = 100,
        CYUnweightedRevenue__c = 200,
        CYWeightedRevenue__c = 200,
        NextCYUnweightedRevenue__c = 300,
        NextCYWeightedRevenue__c = 300
      )
    );

    oldList.add(
      new OpportunityLineItem(
        Id = fakeOliId,
        OpportunityId = fakeOpportunityId,
        PricebookEntryId = fakePriceBookEntry,
        Quantity = 1,
        UnitPrice = 100,
        CYUnweightedRevenue__c = 100,
        CYWeightedRevenue__c = 100,
        NextCYUnweightedRevenue__c = 100,
        NextCYWeightedRevenue__c = 100
      )
    );

    oldList.add(
      new OpportunityLineItem(
        Id = fakeOliId2,
        OpportunityId = fakeOpportunityId2,
        PricebookEntryId = fakePriceBookEntry2,
        Quantity = 1,
        UnitPrice = 100,
        CYUnweightedRevenue__c = 100,
        CYWeightedRevenue__c = 100,
        NextCYUnweightedRevenue__c = 100,
        NextCYWeightedRevenue__c = 100
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    OpportunityLineItemSelector opportunityLineItemSelectorMock = (OpportunityLineItemSelector) mocker.mock(
      OpportunityLineItemSelector.class
    );

    mocker.when(
        opportunityLineItemSelectorMock.selectOpportunityLineItemsByIds(mySet)
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(newList);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_OppLineItem_FinanceCalcs.opportunityLineItemSelector = opportunityLineItemSelectorMock;

    new TA_OppLineItem_FinanceCalcs().afterUpdate(newList, oldList);
    new TA_OppLineItem_FinanceCalcs().afterDelete(oldList);
    new TA_OppLineItem_FinanceCalcs().afterInsert(newList);
  }

  @IsTest
  private static void test_TA_OppLineItemSchedule_SchedFinanceCalcs_afterInsert() {
    List<OpportunityLineItemSchedule> newList = new List<OpportunityLineItemSchedule>();
    List<OpportunityLineItemSchedule> oldList = new List<OpportunityLineItemSchedule>();
    List<OpportunityLineItemSchedule> queriedList = new List<OpportunityLineItemSchedule>();

    // Create the standard pricebook
    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();

    // Create a custom pricebook
    Pricebook2 customPb = PricebookTestUtils.createCustomPricebook(
      'Example Price Book'
    );

    //generate fake Id
    Id fakeOpportunityId = TestFactory.getFakeId(Opportunity.SObjectType);
    Id fakeOpportunityId2 = TestFactory.getFakeId(Opportunity.SObjectType);
    Id fakeOliId = TestFactory.getFakeId(OpportunityLineItem.SObjectType);
    Id fakeOliId2 = TestFactory.getFakeId(OpportunityLineItem.SObjectType);
    Id fakePriceBookEntry = TestFactory.getFakeId(PricebookEntry.SObjectType);
    Id fakePriceBookEntry2 = TestFactory.getFakeId(PricebookEntry.SObjectType);
    Id fakeOliScheduleId = TestFactory.getFakeId(
      OpportunityLineItemSchedule.SObjectType
    );
    Id fakeOliScheduleId1 = TestFactory.getFakeId(
      OpportunityLineItemSchedule.SObjectType
    );
    Id fakeOliScheduleId2 = TestFactory.getFakeId(
      OpportunityLineItemSchedule.SObjectType
    );

    Set<Id> mySet = new Set<Id>();
    List<OpportunityLineItem> olisQueried = new List<OpportunityLineItem>();

    olisQueried.add(
      new OpportunityLineItem(
        Id = fakeOliId,
        OpportunityId = fakeOpportunityId2,
        PricebookEntryId = fakePriceBookEntry2,
        Quantity = 1,
        UnitPrice = 100,
        CYUnweightedRevenue__c = 100,
        CYWeightedRevenue__c = 100,
        NextCYUnweightedRevenue__c = 100,
        NextCYWeightedRevenue__c = 100
      )
    );

    queriedList.add(
      new OpportunityLineItemSchedule(
        Id = fakeOliScheduleId,
        OpportunityLineItemId = fakeOliId,
        ScheduleDate = Date.today(),
        SalesPricePerPack__c = 100,
        EstimatedQuantityPacks__c = 100,
        ScheduleProbability__c = 100,
        Type = 'Quantity',
        Quantity = 1
      )
    );

    newList.add(
      new OpportunityLineItemSchedule(
        Id = fakeOliScheduleId1,
        OpportunityLineItemId = fakeOliId,
        ScheduleDate = Date.today(),
        SalesPricePerPack__c = 200,
        EstimatedQuantityPacks__c = 200,
        ScheduleProbability__c = 100,
        Type = 'Quantity',
        Quantity = 1
      )
    );

    newList.add(
      new OpportunityLineItemSchedule(
        Id = fakeOliScheduleId2,
        OpportunityLineItemId = fakeOliId,
        ScheduleDate = Date.today().addYears(1),
        SalesPricePerPack__c = 200,
        EstimatedQuantityPacks__c = 200,
        ScheduleProbability__c = 100,
        Type = 'Quantity',
        Quantity = 1
      )
    );

    oldList.add(
      new OpportunityLineItemSchedule(
        Id = fakeOliScheduleId1,
        OpportunityLineItemId = fakeOliId,
        ScheduleDate = Date.today(),
        SalesPricePerPack__c = 100,
        EstimatedQuantityPacks__c = 100,
        ScheduleProbability__c = 50,
        Type = 'Quantity',
        Quantity = 1
      )
    );

    oldList.add(
      new OpportunityLineItemSchedule(
        Id = fakeOliScheduleId2,
        OpportunityLineItemId = fakeOliId,
        ScheduleDate = Date.today().addYears(1),
        SalesPricePerPack__c = 100,
        EstimatedQuantityPacks__c = 100,
        ScheduleProbability__c = 50,
        Type = 'Quantity',
        Quantity = 1
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    OpportunityLineItemScheduleSelector opportunityLineItemScheduleSelectorMock = (OpportunityLineItemScheduleSelector) mocker.mock(
      OpportunityLineItemScheduleSelector.class
    );

    mocker.when(
        opportunityLineItemScheduleSelectorMock.selectOpportunityLineItemSchedulesByOLIIds(
          mySet
        )
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(queriedList);

    OpportunityLineItemSelector opportunityLineItemSelectorMock = (OpportunityLineItemSelector) mocker.mock(
      OpportunityLineItemSelector.class
    );

    mocker.when(
        opportunityLineItemSelectorMock.selectOpportunityLineItemsByIds(mySet)
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(olisQueried);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_OppLineItemSchedule_SchedFinanceCalcs.opportunityLineItemScheduleSelector = opportunityLineItemScheduleSelectorMock;
    TA_OppLineItemSchedule_SchedFinanceCalcs.opportunityLineItemSelector = opportunityLineItemSelectorMock;

    TA_OppLineItemSchedule_OLICalcs.opportunityLineItemSelector = opportunityLineItemSelectorMock;
    TA_OppLineItemSchedule_OLICalcs.opportunityLineItemScheduleSelector = opportunityLineItemScheduleSelectorMock;

    new TA_OppLineItemSchedule_SchedFinanceCalcs().afterInsert(newList);
    new TA_OppLineItemSchedule_SchedFinanceCalcs()
      .afterUpdate(newList, oldList);
    new TA_OppLineItemSchedule_SchedFinanceCalcs().afterDelete(oldList);

    new TA_OppLineItemSchedule_OLICalcs().afterInsert(newList);
    new TA_OppLineItemSchedule_OLICalcs().afterUpdate(newList, oldList);
    new TA_OppLineItemSchedule_OLICalcs().afterDelete(oldList);
  }

  @IsTest
  private static void test_TA_OppLineItem_SchedFinanceCalcs_afterUpdate() {
    List<OpportunityLineItem> newList = new List<OpportunityLineItem>();
    List<OpportunityLineItem> oldList = new List<OpportunityLineItem>();

    // Create the standard pricebook
    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();

    // Create a custom pricebook
    Pricebook2 customPb = PricebookTestUtils.createCustomPricebook(
      'Example Price Book'
    );

    //generate fake Id
    Id fakeOpportunityId = TestFactory.getFakeId(Opportunity.SObjectType);
    Id fakeOpportunityId2 = TestFactory.getFakeId(Opportunity.SObjectType);
    Id fakeOliId = TestFactory.getFakeId(OpportunityLineItem.SObjectType);
    Id fakeOliId2 = TestFactory.getFakeId(OpportunityLineItem.SObjectType);
    Id fakePriceBookEntry = TestFactory.getFakeId(PricebookEntry.SObjectType);
    Id fakePriceBookEntry2 = TestFactory.getFakeId(PricebookEntry.SObjectType);

    Set<Id> mySet = new Set<Id>();

    newList.add(
      new OpportunityLineItem(
        Id = fakeOliId,
        OpportunityId = fakeOpportunityId,
        PricebookEntryId = fakePriceBookEntry,
        Quantity = 1,
        UnitPrice = 100,
        CYUnweightedRevenue__c = 200,
        CYWeightedRevenue__c = 200,
        NextCYUnweightedRevenue__c = 300,
        NextCYWeightedRevenue__c = 300
      )
    );

    newList.add(
      new OpportunityLineItem(
        Id = fakeOliId2,
        OpportunityId = fakeOpportunityId2,
        PricebookEntryId = fakePriceBookEntry2,
        Quantity = 1,
        UnitPrice = 100,
        CYUnweightedRevenue__c = 200,
        CYWeightedRevenue__c = 200,
        NextCYUnweightedRevenue__c = 300,
        NextCYWeightedRevenue__c = 300
      )
    );

    oldList.add(
      new OpportunityLineItem(
        Id = fakeOliId,
        OpportunityId = fakeOpportunityId,
        PricebookEntryId = fakePriceBookEntry,
        Quantity = 1,
        UnitPrice = 100,
        CYUnweightedRevenue__c = 100,
        CYWeightedRevenue__c = 100,
        NextCYUnweightedRevenue__c = 100,
        NextCYWeightedRevenue__c = 100
      )
    );

    oldList.add(
      new OpportunityLineItem(
        Id = fakeOliId2,
        OpportunityId = fakeOpportunityId2,
        PricebookEntryId = fakePriceBookEntry2,
        Quantity = 1,
        UnitPrice = 100,
        CYUnweightedRevenue__c = 100,
        CYWeightedRevenue__c = 100,
        NextCYUnweightedRevenue__c = 100,
        NextCYWeightedRevenue__c = 100
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    OpportunityLineItemSelector opportunityLineItemSelectorMock = (OpportunityLineItemSelector) mocker.mock(
      OpportunityLineItemSelector.class
    );

    mocker.when(
        opportunityLineItemSelectorMock.selectOpportunityLineItemsByIds(mySet)
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(newList);

    mocker.when(
        opportunityLineItemSelectorMock.selectOpportunityLineItemsByOppIds(
          mySet
        )
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(newList);

    mocker.when(
        opportunityLineItemSelectorMock.selectOpportunityLineItemsByOppId(
          fakeOpportunityId
        )
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(newList);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_OppLineItem_SchedFinanceCalcs.opportunityLineItemSelector = opportunityLineItemSelectorMock;

    new TA_OppLineItem_SchedFinanceCalcs().afterUpdate(newList, oldList);
    new TA_OppLineItem_SchedFinanceCalcs().afterDelete(oldList);
    new TA_OppLineItem_SchedFinanceCalcs().afterInsert(newList);
  }
}