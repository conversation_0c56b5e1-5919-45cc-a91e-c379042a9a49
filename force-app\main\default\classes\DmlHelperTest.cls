/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  06 May 2024

   @ Description	:   A test class containing test methods for the DmlHelper Class

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   DmlHelper

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 06 May 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public class DmlHelperTest {
    //Test DmlHelper class Insert/Update methods
    @isTest
    static void test_dmlHelper() {
        DmlHelper dmlHelper = new DmlHelper();

        List<Case> newList = new List<Case>();

        newList.add(
            new Case(
                Description = 'My case to be inserted via dmlHelper'
            )
        );

        Test.startTest();
        dmlHelper.insertObjects(newList, 'DmlHelperTest.test_dmlHelper()');
        dmlHelper.updateObjects(newList, 'DmlHelperTest');
        dmlHelper.deleteObjects(newList, 'DmlHelperTest');
        Test.stopTest();
    }

    //Test for an DML Exception being thrown during insert, thus inserting an ExceptionLog
    @isTest
    static void test_dmlHelperInsertExceptionThrown() {
        DmlHelper dmlHelper = new DmlHelper();

        List<Case> newList = new List<Case>();
        //Set Case_Reason__c to a picklist value not on the resticted picklist dataset
        newList.add(
            new Case(
                Case_Reason__c = 'NonValidValue'
            )
        );

        Test.startTest();
        try {
            dmlHelper.insertObjects(newList, 'CaseTestPack.test_caseService');
        } catch (Exception e) {
            // Then
            System.assert(e instanceof DmlException, 'Exception should be an DMLException');
        }

        Test.stopTest();
    }

    //Test for an DML Exception being thrown during update, thus inserting an ExceptionLog
    @isTest
    static void test_dmlHelperUpdateExceptionThrown() {
        DmlHelper dmlHelper = new DmlHelper();

        List<Case> newList = new List<Case>();
        //Set Case_Reason__c to a picklist value not on the resticted picklist dataset
        newList.add(
            new Case(
                Description = 'My case to be inserted via dmlHelper',
                Case_Reason__c = ''
            )
        );

        Test.startTest();
        try {
            dmlHelper.insertObjects(newList, 'CaseTestPack.test_caseService');
            List<Case> casesToUpdate = [SELECT Id, Case_Reason__c FROM Case WHERE Id IN : newList];
            for (Case caseToUpdate: casesToUpdate) {
                caseToUpdate.Case_Reason__c = 'NonValidValue';
            }
            dmlHelper.updateObjects(casesToUpdate, 'test_dmlHelperExceptionThrown');
        } catch (Exception e) {
            // Then
            System.assert(e instanceof DmlException, 'Exception should be an DMLException');
        }

        Test.stopTest();
    }

    //Test for an DML Exception being thrown during update, thus inserting an ExceptionLog
    @isTest
    static void test_dmlHelperDeleteExceptionThrown() {
        DmlHelper dmlHelper = new DmlHelper();

        List<Case> newList = new List<Case>();
        //Set Case_Reason__c to a picklist value not on the resticted picklist dataset
        newList.add(
            new Case(
                Description = 'My case to be inserted via dmlHelper',
                Case_Reason__c = ''
            )
        );

        Test.startTest();
        try {
            dmlHelper.insertObjects(newList, 'CaseTestPack.test_caseService');
            List<Case> casesToUpdate = [SELECT Id, Case_Reason__c FROM Case WHERE Id IN : newList];
            for (Case caseToUpdate: casesToUpdate) {
                caseToUpdate.Case_Reason__c = 'NonValidValue';
                //caseToUpdate.Id = '15550';
            }
            List<Case> casesToDelete = New List<Case>();
            dmlHelper.deleteObjects(casesToUpdate, 'test_dmlHelperExceptionThrown');
        } catch (Exception e) {
            // Then
            System.assert(e instanceof DmlException, 'Exception should be an DMLException');
        }

        Test.stopTest();
    }
}