/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  08 July 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> accountIds = new Set<Id>();
                       accountIds.add('001O300000FGc3tIAD');
                       accountIds.add('001O300000FGc3tIZZ');
                       AccountSelector accountSelector = new AccountSelector();
                       List<Account> accounts = accountSelector.selectAccountsByIds(accountIds);
                       List<Account> accounts = new AccountSelector().selectAccountsByIds(accountIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ck<PERSON>
   @ Last Modified On  : 08 July 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class LeadSelector {
    private String query;
    private String fromObject = ' FROM Lead ';
    private String queryLimit = ' LIMIT 1000';

    //Constructor to setup the base query
    public LeadSelector() {
        buildBaseQuery();
    }

    //Put your fields you intend to almost always select with your account queries here
    private void buildBaseQuery() {
        this.query = 'SELECT Id, Name, Company, Industry, Status, Email, Phone, OwnerId, Owner.Name, LeadSource, LeadSourceDetail__c, IsConverted__c';
    }

    //Set the limit for your query you're building
    public void setQueryLimit(Integer passedLimit) {
        String newQueryLimit = String.valueOf(passedLimit);
        this.queryLimit = ' LIMIT ' + newQueryLimit;
        //system.debug('AccountSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
    }

    //Select your accounts by a set of account ids
    public List<Lead> selectLeadsByIds(Set<Id> leadIds) {
        buildBaseQuery();
        this.query += fromObject + 'WHERE Id IN :leadIds' + this.queryLimit;
        //system.debug('selectAccountsByIds() this.query -> ' + this.query);
        return Database.query(this.query);
    }


    //Would continue to build queries and setters for everything you theoretically need.
}