import { LightningElement, api, wire, track } from "lwc";
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import { CloseActionScreenEvent } from "lightning/actions";
import getAvailableUsers from "@salesforce/apex/CaseFollowLWCController.getAvailableUsers";
import getCurrentFollowers from "@salesforce/apex/CaseFollowLWCController.getCurrentFollowers";
import followCase from "@salesforce/apex/CaseFollowLWCController.followCase";
import unfollowCase from "@salesforce/apex/CaseFollowLWCController.unfollowCase";

export default class CaseFollowLWC extends LightningElement {
  _recordId;

  @api
  get recordId() {
    return this._recordId;
  }

  set recordId(value) {
    console.log("recordId setter called with value:", value);
    this._recordId = value;

    // If we have a recordId and haven't loaded followers yet, load them
    if (value && !this.hasRendered) {
      console.log("Loading followers from recordId setter");
      this.loadCurrentFollowers();
    }
  }
  @track showModal = false;
  @track availableUsers = [];
  @track currentFollowers = [];
  @track selectedUsers = [];
  @track searchTerm = "";
  @track isLoading = false;
  @track activeTab = "manage";
  @track isRecordAction = false;

  wiredUsersResult;
  hasRendered = false;

  async connectedCallback() {
    console.log("connectedCallback called");
    console.log("recordId at connectedCallback:", this.recordId);

    // Check if this is being used as a Record Action
    // Record Actions don't show the trigger button, they open directly
    const urlParams = new URLSearchParams(window.location.search);
    this.isRecordAction =
      urlParams.has("c__action") ||
      window.location.pathname.includes("/one/one.app");

    console.log("isRecordAction:", this.isRecordAction);

    // Wait a bit for recordId to be set if it's null initially
    if (!this.recordId) {
      console.log("recordId is null, waiting for it to be set...");
      // Use setTimeout to wait for the recordId to be populated
      setTimeout(() => {
        console.log("recordId after timeout:", this.recordId);
        if (this.recordId) {
          this.loadCurrentFollowers();
        } else {
          console.error("recordId is still null after timeout");
        }
      }, 100);
    } else {
      // Always load current followers when component initializes
      await this.loadCurrentFollowers();
    }

    if (this.isRecordAction) {
      this.showModal = true;
    }
  }

  renderedCallback() {
    console.log("renderedCallback called");
    console.log("recordId at renderedCallback:", this.recordId);

    // Only run this once and only if we have a recordId
    if (!this.hasRendered && this.recordId) {
      this.hasRendered = true;
      console.log(
        "Loading followers in renderedCallback with recordId:",
        this.recordId
      );
      this.loadCurrentFollowers();
    }
  }

  @wire(getAvailableUsers)
  wiredUsers(result) {
    this.wiredUsersResult = result;
    if (result.data) {
      this.availableUsers = result.data;
    } else if (result.error) {
      this.showToast("Error", "Failed to load users", "error");
    }
  }

  async loadCurrentFollowers() {
    console.log("loadCurrentFollowers called with recordId:", this.recordId);

    if (!this.recordId) {
      console.warn("Cannot load followers: recordId is null or undefined");
      return;
    }

    try {
      this.currentFollowers = await getCurrentFollowers({
        caseId: this.recordId
      });
      console.log("currentFollowers -> ", this.currentFollowers);
    } catch (error) {
      console.error("Error in loadCurrentFollowers:", error);
      this.showToast("Error", "Failed to load current followers", "error");
    }
  }

  get filteredUsers() {
    if (!this.searchTerm) return this.availableUsers;
    return this.availableUsers.filter(
      (user) =>
        user.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  get followersCount() {
    return this.currentFollowers.length;
  }

  get hasSelectedUsers() {
    return this.selectedUsers.length > 0;
  }

  get isFollowTab() {
    return this.activeTab === "follow";
  }

  get isManageTab() {
    return this.activeTab === "manage";
  }

  openModal() {
    console.log("openModal called");
    this.showModal = true;
    this.selectedUsers = [];
    this.searchTerm = "";
    // No need to load followers again - already loaded in connectedCallback
  }

  closeModal() {
    this.showModal = false;
    this.selectedUsers = [];
    this.searchTerm = "";

    // If this is a Record Action, close the action screen
    if (this.isRecordAction) {
      this.dispatchEvent(new CloseActionScreenEvent());
    }
  }

  handleTabChange(event) {
    this.activeTab = event.target.value;
    this.selectedUsers = [];
  }

  handleSearchChange(event) {
    this.searchTerm = event.target.value;
  }

  handleUserSelection(event) {
    const userId = event.currentTarget.dataset.userId;
    const isSelected = event.currentTarget.classList.contains("selected");

    if (isSelected) {
      this.selectedUsers = this.selectedUsers.filter((id) => id !== userId);
      event.currentTarget.classList.remove("selected");
    } else {
      this.selectedUsers = [...this.selectedUsers, userId];
      event.currentTarget.classList.add("selected");
    }
  }

  async handleFollow() {
    if (this.selectedUsers.length === 0) {
      this.showToast("Warning", "Please select at least one user", "warning");
      return;
    }

    this.isLoading = true;
    try {
      const result = await followCase({
        caseId: this.recordId,
        userIds: this.selectedUsers
      });

      this.showToast("Success", result, "success");
      this.closeModal();
      await this.loadCurrentFollowers();
    } catch (error) {
      this.showToast("Error", error.body.message, "error");
    } finally {
      this.isLoading = false;
    }
  }

  async handleUnfollow() {
    if (this.selectedUsers.length === 0) {
      this.showToast(
        "Warning",
        "Please select at least one user to unfollow",
        "warning"
      );
      return;
    }

    this.isLoading = true;
    try {
      const result = await unfollowCase({
        caseId: this.recordId,
        userIds: this.selectedUsers
      });

      this.showToast("Success", result, "success");
      this.closeModal();
      await this.loadCurrentFollowers();
    } catch (error) {
      this.showToast("Error", error.body.message, "error");
    } finally {
      this.isLoading = false;
    }
  }

  showToast(title, message, variant) {
    this.dispatchEvent(
      new ShowToastEvent({
        title,
        message,
        variant
      })
    );
  }
}
