import { LightningElement, api, wire, track } from "lwc";
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import { CloseActionScreenEvent } from "lightning/actions";
import getAvailableUsers from "@salesforce/apex/CaseFollowLWCController.getAvailableUsers";
import getCurrentFollowers from "@salesforce/apex/CaseFollowLWCController.getCurrentFollowers";
import followCase from "@salesforce/apex/CaseFollowLWCController.followCase";
import unfollowCase from "@salesforce/apex/CaseFollowLWCController.unfollowCase";

export default class CaseFollowLWC extends LightningElement {
  @api recordId; // Case ID
  @track showModal = false;
  @track availableUsers = [];
  @track currentFollowers = [];
  @track selectedUsers = [];
  @track searchTerm = "";
  @track isLoading = false;
  @track activeTab = "follow";
  @track isRecordAction = false;

  wiredUsersResult;

  async connectedCallback() {
    console.log("connectedCallback called");
    // Check if this is being used as a Record Action
    // Record Actions don't show the trigger button, they open directly
    const urlParams = new URLSearchParams(window.location.search);
    this.isRecordAction =
      urlParams.has("c__action") ||
      window.location.pathname.includes("/one/one.app");

    console.log("isRecordAction:", this.isRecordAction);

    // Always load current followers when component initializes
    await this.loadCurrentFollowers();

    if (this.isRecordAction) {
      this.showModal = true;
    }
  }

  @wire(getAvailableUsers)
  wiredUsers(result) {
    this.wiredUsersResult = result;
    if (result.data) {
      this.availableUsers = result.data;
    } else if (result.error) {
      this.showToast("Error", "Failed to load users", "error");
    }
  }

  async loadCurrentFollowers() {
    console.log("loadCurrentFollowers called with recordId:", this.recordId);
    try {
      this.currentFollowers = await getCurrentFollowers({
        caseId: this.recordId
      });
      console.log("currentFollowers -> ", this.currentFollowers);
    } catch (error) {
      console.error("Error in loadCurrentFollowers:", error);
      this.showToast("Error", "Failed to load current followers", "error");
    }
  }

  get filteredUsers() {
    if (!this.searchTerm) return this.availableUsers;
    return this.availableUsers.filter(
      (user) =>
        user.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  get followersCount() {
    return this.currentFollowers.length;
  }

  get hasSelectedUsers() {
    return this.selectedUsers.length > 0;
  }

  get isFollowTab() {
    return this.activeTab === "follow";
  }

  get isManageTab() {
    return this.activeTab === "manage";
  }

  get showTriggerButton() {
    return !this.isRecordAction;
  }

  openModal() {
    console.log("openModal called");
    this.showModal = true;
    this.selectedUsers = [];
    this.searchTerm = "";
    // No need to load followers again - already loaded in connectedCallback
  }

  closeModal() {
    this.showModal = false;
    this.selectedUsers = [];
    this.searchTerm = "";

    // If this is a Record Action, close the action screen
    if (this.isRecordAction) {
      this.dispatchEvent(new CloseActionScreenEvent());
    }
  }

  handleTabChange(event) {
    this.activeTab = event.target.value;
    this.selectedUsers = [];
  }

  handleSearchChange(event) {
    this.searchTerm = event.target.value;
  }

  handleUserSelection(event) {
    const userId = event.currentTarget.dataset.userId;
    const isSelected = event.currentTarget.classList.contains("selected");

    if (isSelected) {
      this.selectedUsers = this.selectedUsers.filter((id) => id !== userId);
      event.currentTarget.classList.remove("selected");
    } else {
      this.selectedUsers = [...this.selectedUsers, userId];
      event.currentTarget.classList.add("selected");
    }
  }

  async handleFollow() {
    if (this.selectedUsers.length === 0) {
      this.showToast("Warning", "Please select at least one user", "warning");
      return;
    }

    this.isLoading = true;
    try {
      const result = await followCase({
        caseId: this.recordId,
        userIds: this.selectedUsers
      });

      this.showToast("Success", result, "success");
      this.closeModal();
      await this.loadCurrentFollowers();
    } catch (error) {
      this.showToast("Error", error.body.message, "error");
    } finally {
      this.isLoading = false;
    }
  }

  async handleUnfollow() {
    if (this.selectedUsers.length === 0) {
      this.showToast(
        "Warning",
        "Please select at least one user to unfollow",
        "warning"
      );
      return;
    }

    this.isLoading = true;
    try {
      const result = await unfollowCase({
        caseId: this.recordId,
        userIds: this.selectedUsers
      });

      this.showToast("Success", result, "success");
      this.closeModal();
      await this.loadCurrentFollowers();
    } catch (error) {
      this.showToast("Error", error.body.message, "error");
    } finally {
      this.isLoading = false;
    }
  }

  showToast(title, message, variant) {
    this.dispatchEvent(
      new ShowToastEvent({
        title,
        message,
        variant
      })
    );
  }
}
