/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  06 June 2024

   @ Description	:   The Service Layer Pattern is a very powerful tool that can help a team create a strong bias towards reusable code. If this concept is new to your team, the Service Layer Badge on Trailhead is a great place to start.
                        The underlying pattern is old and has a long background in many programming languages (including Java).
                        The recommended approach is to create a single service class for each Salesforce object for which you have code (“AccountService.cls”, “CampaignMemberService.cls”, etc). Within that class you create static methods which implement different bits of business logic. Your main design constraint is
                        to try and make the service methods themselves as reusable as possible. Keep them generic!

   @ Developer Notes  : AccountService accountService = new AccountService();

                        1) String accountName = accountService.getAccountName(accountId);
                        2) String accountName = new AccountService().getAccountName(accountId);


   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                    https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master
                    https://bluecanvas.io/blog/how-to-structure-and-manage-your-apex-codebase-for-scale-and-agility

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 06 June 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public inherited sharing class AccountService {
  @TestVisible
  private static DmlHelper dmlHelper = new DmlHelper();
  @TestVisible
  private static AccountSelector accountSelector = new AccountSelector();

  public List<Account> getChildAccounts(Set<Id> accountParentIds) {
    //system.debug('getChildAccounts -> accountParentIds -> ' + accountParentIds);
    List<Account> accounts = accountSelector.selectAccountsByParentIds(
      accountParentIds
    );
    return accounts.isEmpty() ? null : accounts;
  }

  public void updateAccounts(List<Account> accountsToUpdate, String Source) {
    for (Account accountToUpdate : accountsToUpdate) {
      accountToUpdate.ValidationBypassDateTime__c = System.now();
    }
    dmlHelper.updateObjects(accountsToUpdate, Source);
  }

  public void createAccounts(List<Account> accountsToInsert, String Source) {
    for (Account accountToInsert : accountsToInsert) {
      accountToInsert.ValidationBypassDateTime__c = System.now();
    }
    dmlHelper.insertObjects(accountsToInsert, Source);
  }

  // Change static methods to instance methods with static wrappers
  public Id findAccountIdByName(String accountName) {
    system.debug('findAccountIdByName -> accountName -> ' + accountName);
    if (String.isBlank(accountName)) {
      return null;
    }

    List<Account> accounts = accountSelector.selectAccountsByName(
      new Set<String>{ accountName }
    );
    if (CollectionUtils.isEmpty(accounts)) {
      //system.debug('findAccountIdByName -> accounts -> ' + accounts);
      return null;
    } else {
      //system.debug('findAccountIdByName -> accounts -> ' + accounts);
      return accounts.isEmpty() ? null : accounts[0].Id;
    }
  }

  public String getAccountNameById(Id accountId) {
    if (accountId == null) {
      return null;
    }

    List<Account> accounts = accountSelector.selectAccountsByIds(
      new Set<Id>{ accountId }
    );
    return accounts.isEmpty() ? null : accounts[0].Name;
  }
}