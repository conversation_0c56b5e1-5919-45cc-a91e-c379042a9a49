<?xml version="1.0" encoding="UTF-8"?>

<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    
    <apiVersion>50.0</apiVersion>
    
    <isExposed>false</isExposed>
    
    <targets>
        
        <target>lightning__FlowScreen</target>
        
    </targets>
    
    <targetConfigs>
        
        <targetConfig targets="lightning__FlowScreen" category="Input">
            
            <property name="titleFieldName" label="Title Field Name" type="String" description="Field API Name Containing Title Value"/>
            
            <property name="subtitleFieldName" label="Subtitle Field Name" type="String" description="Field API Name Containing Subtitle Value"/>
            
            <property name="statusFieldName" label="Status Field Name" type="String" description="API Name for Status Field"/>
            
            <property name="icon" label="Icon" type="String" description="Icon name for example standard:account"/>
            
            <property name="src" label="SRC" type="String" description="URL used for custom image icon"/>
            
            <property name="variant" label="Icon Variant" type="String" description="Valid values are empty, circle, and square. This value defaults to square."/>
            
            <property name="hoverText" label="Hover Text" type="String" description="This text is displayed when hovering over the icon"/>
            
            <property name="headerStyle" label="Header Style" type="String" description="Add your own style attribute to the card headers ie: background-color:red;"/>
            
        </targetConfig>
        
    </targetConfigs>
    
</LightningComponentBundle>