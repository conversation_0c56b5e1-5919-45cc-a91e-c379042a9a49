/********************************************************************************************

   @ Func Area	:  Apex <PERSON>

   @ Author	:  <PERSON>

   @ Date	:  27 August 2024

   @ Description	:   This batch job will run daily, at midnight. The job will find all orpaned MultiSelectReporting__c records and delete them.

   @ Developer Notes  : How to schedule this job:
                        String jobName = 'Batch Job-MultiSelectReportingCleanup-Daily';
                        String cronExp = '0 0 0 * * ?';  // Run every day at midnight
                        System.schedule(jobName, cronExp, new BatchJobMultiSelectReportingCleanup());

                        Run job manually:                           
                        BatchJobMultiSelectReportingCleanup batchJob = new BatchJobMultiSelectReportingCleanup();
                        Id batchProcessId = Database.executeBatch(batchJob, 200);
                        // Log the batch job ID
                        System.debug('Batch job started with Id: ' + batchProcessId);

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 27 August 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

global class BatchJobMultiSelectReportingCleanup implements Database.Batchable<sObject>, Schedulable {
  @TestVisible
  private static MultiSelectReportingService multiSelectReportingService = new MultiSelectReportingService(); //Mock MultiSelectReportingService Class

  global Database.QueryLocator start(Database.BatchableContext BC) {
    // Query for all records that are orphaned
    return Database.getQueryLocator('SELECT Id, Name, Contact__c FROM MultiSelectReporting__c WHERE Contact__c = null AND Account__c = null');
  }

  global void execute(Database.BatchableContext BC, List<MultiSelectReporting__c> scope) {
    if (!scope.isEmpty()) {
      multiSelectReportingService.deleteMultiSelectReporting(scope, 'BatchJobMultiSelectReportingCleanup');
    }
  }

  global void finish(Database.BatchableContext BC) {
    // Optional: Add any post-processing logic here
  }

  // Schedulable interface method
  global void execute(SchedulableContext SC) {
    Database.executeBatch(new BatchJobMultiSelectReportingCleanup());
  }
}