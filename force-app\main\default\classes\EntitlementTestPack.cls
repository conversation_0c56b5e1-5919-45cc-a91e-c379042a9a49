/********************************************************************************************

@ Func Area	:  Apex development testing

@ Author	:  <PERSON>

@ Date		:  06 May 2024

@ Description	:   A test class containing test methods for apex automation on the Entitlement Object

@ SFDC Documentation :   Test Best Practices to be followed:
https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
Assertion Class
https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

@ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

@ Classes Tested	:   EntitlementService, EntitlementSelector

@ Last Modified By  : <PERSON>
@ Last Modified On  : 06 May 2024
@ Last Modified Reason  : Creation

@ Last Modified By  : <PERSON>
@ Last Modified On  : 28 November 2024
@ Last Modified Reason  : Added test_EntitlementService method.

********************************************************************************************/
@IsTest
public class EntitlementTestPack {
    
    //Test EntitlementService class
    @isTest
    static void test_EntitlementService() {
        Test.startTest();
        // Given
        Id validEntitlementId = MockerUtils.generateId(Entitlement.SObjectType);
        List<Id> entitlementIds = new List<Id>{ MockerUtils.generateId(Entitlement.SObjectType), MockerUtils.generateId(Entitlement.SObjectType) };
            
            Entitlement expectedEntitlement = (Entitlement) MockerUtils.updateObjectState(
                new Entitlement(Id = validEntitlementId, Name = 'New Entitlement'),
                new Map<String, Object>{ 'Type' => 'Customer' }
            );
        
        List<Entitlement> entitlementsToInsert = new List<Entitlement>{ expectedEntitlement };
            List<Entitlement> entitlementsToUpdate = new List<Entitlement>{ expectedEntitlement };
                
                List<String> entitlementNames = new List<String>{ 'Dummy Entitlement 1', 'Dummy Entitlement 2' };
                    
                    Mocker mocker = Mocker.startStubbing();
        
        DmlHelper dmlHelperMock = (DmlHelper) mocker.mock(DmlHelper.class);
        
        dmlHelperMock.insertObjects(entitlementsToInsert, 'EntitlementService');
        Mocker.MethodRecorder insertObjectsRec = mocker.when().withAnyValues().getMethodRecorder();
        
        dmlHelperMock.updateObjects(entitlementsToInsert, 'EntitlementServiceUpdate');
        Mocker.MethodRecorder updateObjectsRec = mocker.when().withAnyValues().getMethodRecorder();
        
        EntitlementSelector entitlementSelectorMock = (EntitlementSelector) mocker.mock(EntitlementSelector.class);
        
        mocker.when(entitlementSelectorMock.selectEntitlementsById(new Set<Id>{ validEntitlementId }))
            .thenReturn(new List<Entitlement>{ expectedEntitlement });
        
        
        // Going to the execution phase
        mocker.stopStubbing();
        
        // Replacing the real instance by the mocked one
        EntitlementService.entitlementSelector = entitlementSelectorMock;
        EntitlementService.dmlHelper = dmlHelperMock;
        
        // When 1
        //test method public String getEntitlementName(Id entitlementId) on EntitlementService
        String entitlementName = new EntitlementService().getEntitlementName(validEntitlementId);
        
        // Then 1
        System.assertEquals('New Entitlement', entitlementName);
        
        // When 2
        //test method public void createEntitlements(List<String> entitlementNames, String Source) on EntitlementService
        new EntitlementService().createEntitlements(entitlementsToInsert, 'EntitlementTestPack.test_EntitlementService()');
        
        // Then 2
        System.assertEquals(1, insertObjectsRec.getCallsCount());
        
        // When 3
        //test method public void updateEntitlements(List<String> entitlementNames, String Source) on EntitlementService
        new EntitlementService().updateEntitlements(entitlementsToUpdate, 'EntitlementTestPack.test_EntitlementService()');
        
        // Then 3
        System.assertEquals(1, updateObjectsRec.getCallsCount());
        
        // Then 4
        //System.assertEquals('New Entitlement', entitlementName);
        
        Test.stopTest();
    }
    
    //Test EntitlementSelector class
    @isTest
    static void test_EntitlementSelector() {
        EntitlementSelector entitlementSelector = new EntitlementSelector();
        Integer queryLimit = 100;
        Set<Id> entitlementIds = new Set<Id> {MockerUtils.generateId(Entitlement.SObjectType), MockerUtils.generateId(Entitlement.SObjectType)};
            Set<Id> accountIds = new Set<Id> {MockerUtils.generateId(Account.SObjectType), MockerUtils.generateId(Account.SObjectType)};
                Test.startTest();
        entitlementSelector.setQueryLimit(queryLimit);
        entitlementSelector.selectEntitlementsById(entitlementIds);
        entitlementSelector.selectEntitlementsByAccountId(accountIds);
        Test.stopTest();
    }
}