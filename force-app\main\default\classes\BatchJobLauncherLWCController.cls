/********************************************************************************************

   @ Func Area	:  LWC Batch Job Launcher Controller

   @ Author	:  <PERSON>

   @ Date		:  08 August 2024

   @ Description	:     
                        This is a controller class for the batchJobLauncherLWC Lightning Web Component.
                        It is used to launch the Batch Job BatchJobBuildMatchingKey (1/3) 
                        Subsequent batch jobs are chained together to ensure all territory assignments are processed.

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :  

                         
   @ Test Class	:   BatchJobBuildMatchingKeyTest

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 30 October 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public class BatchJobLauncherLWCController {
  @AuraEnabled(cacheable=false)
  public static String launchBatchJob() {
    try {
      //Last version of query
      /*
      String query =
        'SELECT Id, SalesChannel__c, SalesTeam__c, ' +
        'ShippingCountry, ShippingCountryCode, ShippingState, ' +
        'ShippingStateCode, ShippingPostalCode, SalesTerritories__c, ' +
        'SalesTerritoryMatchingKey__c ' +
        'FROM Account ' +
        'WHERE ChildCount__c = 0 ' +
        'AND IsExcludedFromRealign = FALSE ' +
        'AND SalesTeam__c != \'CGS\'';
        */

      //New version of query
      String query =
        'SELECT Id, Name, SalesChannel__c, SalesTeam__c, ' +
        'ShippingCountry, ShippingCountryCode, ShippingState, ' +
        'ShippingStateCode, ShippingPostalCode, SalesTerritories__c, ' +
        'SalesTerritoryMatchingKey__c, RecommendedSalesTerritory__c ' +
        'FROM Account ' +
        'WHERE IsExcludedFromRealign = FALSE ' +
        'AND (ChildCount__c = 0 ' +
        'OR ChildCount__c = null)';

      //Kick off 1st Batch Job.
      BatchJobBuildMatchingKey firstBatchJob = new BatchJobBuildMatchingKey(
        query
      );
      ID batchJobId = Database.executeBatch(firstBatchJob, 200);

      /*
      // Log the batch job ID
      System.debug(
        'Batch Job BatchJobBuildMatchingKey (1/3) started with ID: ' +
        batchJobId
      );
      */

      return 'Batch Job BatchJobBuildMatchingKey (1/3) started with ID: ' +
        batchJobId;
    } catch (Exception e) {
      return 'Error launching batch job: ' + e.getMessage();
    }
  }
}