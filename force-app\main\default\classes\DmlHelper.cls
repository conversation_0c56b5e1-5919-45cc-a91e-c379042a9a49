/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  06 June 2024

   @ Description	:   This is an DML Helper class that will perform inserts/updates/upserts of generic SObject records passed to it's methods.
                        Methods accept a List<SObject> records & a source string.  If SObject records fails to insert/update/upsert then a Exception Log is created
                        This allows DML Mocking for Unit Testing purposes

   @ Developer Notes   :

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                      https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 06 June 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public inherited sharing class DmlHelper {
    public void insertObjects(List<SObject> objects, String source) {
        try {
            Database.SaveResult[] results = Database.insert(objects, false);
            // iterate over the list of returned results
            for (Database.SaveResult result : results) {
                if (result.isSuccess()) {
                    //System.debug('Successfully inserted records : ' + result.getId());
                } else {
                    Schema.SObjectType sObjectType = objects[0].getSObjectType();
                    String objectNameString = String.valueOf(sObjectType);
                    // Operation failed, so log an exception log
                    for (Database.Error error : result.getErrors()) {
                        //Log an exception record
                        String recordId = result.getId();
                        String objectName = objectNameString;
                        String errorMessage = error.getMessage();
                        List<String> fields = error.getFields();
                        String statusCodes = String.valueOf(error.getStatusCode());
                        String apexClassName = source;
                        String flowName = '';
                        String dmlOperation = 'Insert';
                        Utilities.logException(recordId, objectName, errorMessage, fields, statusCodes, apexClassName, flowName, dmlOperation);
                    }
                }
            }
        } catch (DMLException e) {
            throw new DMLException('Unable to Perform record insert, please show this error to your System Administrator : ' + e.getMessage());
        }
    }

    public void updateObjects(List<SObject> objects, String source) {
        try {
            Database.SaveResult[] results = Database.update(objects, false);
            // iterate over the list of returned results
            for (Database.SaveResult result : results) {
                if (result.isSuccess()) {
                    //System.debug('Successfully updated records : ' + result.getId());
                } else {
                    Schema.SObjectType sObjectType = objects[0].getSObjectType();
                    String objectNameString = String.valueOf(sObjectType);
                    // Operation failed, so log an exception log
                    for (Database.Error error : result.getErrors()) {
                        //Log an exception record
                        String recordId = result.getId();
                        String objectName = objectNameString;
                        String errorMessage = error.getMessage();
                        List<String> fields = error.getFields();
                        String statusCodes = String.valueOf(error.getStatusCode());
                        String apexClassName = source;
                        String flowName = '';
                        String dmlOperation = 'Update';
                        Utilities.logException(recordId, objectName, errorMessage, fields, statusCodes, apexClassName, flowName, dmlOperation);
                    }
                }
            }
        } catch (DMLException e) {
            throw new DMLException('Unable to Perform record update, please show this error to your System Administrator : ' + e.getMessage());
        }
    }

    public void deleteObjects(List<SObject> objects, String source) {
        try {
            Database.DeleteResult[] results = Database.delete(objects, false);
            // iterate over the list of returned results
            for (Database.DeleteResult result : results) {
                if (result.isSuccess()) {
                    //System.debug('Successfully updated records : ' + result.getId());
                } else {
                    Schema.SObjectType sObjectType = objects[0].getSObjectType();
                    String objectNameString = String.valueOf(sObjectType);
                    // Operation failed, so log an exception log
                    /* for (Database.Error error : result.getErrors()) {
                         //Log an exception record
                         String recordId = result.getId();
                         String objectName = objectNameString;
                         String errorMessage = error.getMessage();
                         List<String> fields = error.getFields();
                         String statusCodes = String.valueOf(error.getStatusCode());
                         String apexClassName = source;
                         String flowName = '';
                         String dmlOperation = 'Delete';
                         Utilities.logException(recordId, objectName, errorMessage, fields, statusCodes, apexClassName, flowName, dmlOperation);
                       }*/
                }
            }
        } catch (DMLException e) {
            throw new DMLException('Unable to Perform record delete, please show this error to your System Administrator : ' + e.getMessage());
        }
    }
}