/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  27 November 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Limited to 1 as there is only currently 1 in the org

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : Daniel <PERSON>
   @ Last Modified On  : 27 November 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class BusinessHoursSelector {
  private String query;
  private String fromObject = ' FROM BusinessHours ';
  private String queryLimit = ' LIMIT 1';

  //Constructor to setup the base query
  public BusinessHoursSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your businessHours queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id ';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('BusinessHoursSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your businessHourss by a set of businessHours ids
  public List<BusinessHours> selectBusinessHourssByIds(Set<Id> businessHoursIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id IN :businessHoursIds' + this.queryLimit;
    //system.debug('selectBusinessHourssByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your businessHourss by a set of businessHours parent Ids
  public List<BusinessHours> selectAllBusinessHours() {
    buildBaseQuery();
    this.query += fromObject + this.queryLimit;
    //system.debug('selectBusinessHourssByParentIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Would continue to build queries and setters for everything you theoretically need.
}