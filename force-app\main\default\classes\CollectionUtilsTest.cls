/********************************************************************************************

   @ Func Area	:  Collection Utility Methods

   @ Author	:  <PERSON>

   @ Date	:  04 February 2025

   @ Description	:   This Utility classs is responsible for processing collections of objects and evaluating for null or empty values    

   @ Class Tested	:  CollectionUtils  

   @ Developer Notes   :        // Example usage with Id collections
                                List<Id> accountIds = new List<Id>();
                                for(Account acc : [SELECT Id FROM Account LIMIT 5]) {
                                    accountIds.add(acc.Id);
                                }

                                // Checking List<Id>
                                if (CollectionUtils.isEmpty(accountIds)) {
                                    // Handle empty Id list case
                                }

                                // With SObject Lists
                                List<Account> accounts = [SELECT Id FROM Account LIMIT 5];
                                if (CollectionUtils.isNotEmpty(accounts)) {
                                    // Process accounts
                                }

                                // With Id Sets
                                Set<Id> accountIdSet = new Set<Id>(accountIds);
                                if (CollectionUtils.isEmpty(accountIdSet)) {
                                    // Handle empty Id set case
                                }

                                // With SObject Maps
                                Map<Id, Account> accountMap = new Map<Id, Account>([SELECT Id FROM Account LIMIT 5]);
                                if (CollectionUtils.isNotEmpty(accountMap)) {
                                    // Process account map
                                }
   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                        https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 04 February 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/
@IsTest
private class CollectionUtilsTest {
  @IsTest
  static void testListObjectMethods() {
    // Test empty List<Object>
    List<Object> emptyList = new List<Object>();
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(emptyList),
      'Empty list should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(emptyList),
      'Empty list should return false for isNotEmpty'
    );

    // Test null List<Object>
    List<Object> nullList;
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(nullList),
      'Null list should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(nullList),
      'Null list should return false for isNotEmpty'
    );

    // Test populated List<Object>
    List<Object> populatedList = new List<Object>{ 'Test' };
    System.assertEquals(
      false,
      CollectionUtils.isEmpty(populatedList),
      'Populated list should return false'
    );
    System.assertEquals(
      true,
      CollectionUtils.isNotEmpty(populatedList),
      'Populated list should return true for isNotEmpty'
    );
  }

  @IsTest
  static void testListIdMethods() {
    // Create test data
    Account testAccount = new Account(Name = 'Test Account');
    insert testAccount;

    // Test empty List<Id>
    List<Id> emptyIdList = new List<Id>();
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(emptyIdList),
      'Empty Id list should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(emptyIdList),
      'Empty Id list should return false for isNotEmpty'
    );

    // Test null List<Id>
    List<Id> nullIdList;
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(nullIdList),
      'Null Id list should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(nullIdList),
      'Null Id list should return false for isNotEmpty'
    );

    // Test populated List<Id>
    List<Id> populatedIdList = new List<Id>{ testAccount.Id };
    System.assertEquals(
      false,
      CollectionUtils.isEmpty(populatedIdList),
      'Populated Id list should return false'
    );
    System.assertEquals(
      true,
      CollectionUtils.isNotEmpty(populatedIdList),
      'Populated Id list should return true for isNotEmpty'
    );
  }

  @IsTest
  static void testListSObjectMethods() {
    // Test empty List<SObject>
    List<Account> emptyAccList = new List<Account>();
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(emptyAccList),
      'Empty SObject list should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(emptyAccList),
      'Empty SObject list should return false for isNotEmpty'
    );

    // Test null List<SObject>
    List<Account> nullAccList;
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(nullAccList),
      'Null SObject list should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(nullAccList),
      'Null SObject list should return false for isNotEmpty'
    );

    // Test populated List<SObject>
    Account testAcc = new Account(Name = 'Test Account');
    List<Account> populatedAccList = new List<Account>{ testAcc };
    System.assertEquals(
      false,
      CollectionUtils.isEmpty(populatedAccList),
      'Populated SObject list should return false'
    );
    System.assertEquals(
      true,
      CollectionUtils.isNotEmpty(populatedAccList),
      'Populated SObject list should return true for isNotEmpty'
    );
  }

  @IsTest
  static void testSetMethods() {
    // Test empty Set<Object>
    Set<Object> emptySet = new Set<Object>();
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(emptySet),
      'Empty set should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(emptySet),
      'Empty set should return false for isNotEmpty'
    );

    // Test null Set<Object>
    Set<Object> nullSet;
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(nullSet),
      'Null set should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(nullSet),
      'Null set should return false for isNotEmpty'
    );

    // Test populated Set<Object>
    Set<Object> populatedSet = new Set<Object>{ 'Test' };
    System.assertEquals(
      false,
      CollectionUtils.isEmpty(populatedSet),
      'Populated set should return false'
    );
    System.assertEquals(
      true,
      CollectionUtils.isNotEmpty(populatedSet),
      'Populated set should return true for isNotEmpty'
    );
  }

  @IsTest
  static void testSetIdMethods() {
    // Create test data
    Account testAccount = new Account(Name = 'Test Account');
    insert testAccount;

    // Test empty Set<Id>
    Set<Id> emptyIdSet = new Set<Id>();
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(emptyIdSet),
      'Empty Id set should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(emptyIdSet),
      'Empty Id set should return false for isNotEmpty'
    );

    // Test null Set<Id>
    Set<Id> nullIdSet;
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(nullIdSet),
      'Null Id set should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(nullIdSet),
      'Null Id set should return false for isNotEmpty'
    );

    // Test populated Set<Id>
    Set<Id> populatedIdSet = new Set<Id>{ testAccount.Id };
    System.assertEquals(
      false,
      CollectionUtils.isEmpty(populatedIdSet),
      'Populated Id set should return false'
    );
    System.assertEquals(
      true,
      CollectionUtils.isNotEmpty(populatedIdSet),
      'Populated Id set should return true for isNotEmpty'
    );
  }

  @IsTest
  static void testMapMethods() {
    // Test empty Map<Object, Object>
    Map<Object, Object> emptyMap = new Map<Object, Object>();
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(emptyMap),
      'Empty map should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(emptyMap),
      'Empty map should return false for isNotEmpty'
    );

    // Test null Map<Object, Object>
    Map<Object, Object> nullMap;
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(nullMap),
      'Null map should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(nullMap),
      'Null map should return false for isNotEmpty'
    );

    // Test populated Map<Object, Object>
    Map<Object, Object> populatedMap = new Map<Object, Object>{
      'Test' => 'Value'
    };
    System.assertEquals(
      false,
      CollectionUtils.isEmpty(populatedMap),
      'Populated map should return false'
    );
    System.assertEquals(
      true,
      CollectionUtils.isNotEmpty(populatedMap),
      'Populated map should return true for isNotEmpty'
    );
  }

  @IsTest
  static void testMapIdSObjectMethods() {
    // Create test data
    Account testAccount = new Account(Name = 'Test Account');
    insert testAccount;

    // Test empty Map<Id, SObject>
    Map<Id, SObject> emptyIdMap = new Map<Id, SObject>();
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(emptyIdMap),
      'Empty Id-SObject map should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(emptyIdMap),
      'Empty Id-SObject map should return false for isNotEmpty'
    );

    // Test null Map<Id, SObject>
    Map<Id, SObject> nullIdMap;
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(nullIdMap),
      'Null Id-SObject map should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(nullIdMap),
      'Null Id-SObject map should return false for isNotEmpty'
    );

    // Test populated Map<Id, SObject>
    Map<Id, SObject> populatedIdMap = new Map<Id, SObject>{
      testAccount.Id => testAccount
    };
    System.assertEquals(
      false,
      CollectionUtils.isEmpty(populatedIdMap),
      'Populated Id-SObject map should return false'
    );
    System.assertEquals(
      true,
      CollectionUtils.isNotEmpty(populatedIdMap),
      'Populated Id-SObject map should return true for isNotEmpty'
    );
  }

  @IsTest
  static void testIsNullOrBlank() {
    // Test null object
    Object nullObj;
    System.assertEquals(
      true,
      CollectionUtils.isNullOrBlank(nullObj),
      'Null object should return true'
    );

    // Test empty string
    String emptyStr = '';
    System.assertEquals(
      true,
      CollectionUtils.isNullOrBlank(emptyStr),
      'Empty string should return true'
    );

    // Test blank string (spaces only)
    String blankStr = '   ';
    System.assertEquals(
      true,
      CollectionUtils.isNullOrBlank(blankStr),
      'Blank string should return true'
    );

    // Test non-empty string
    String nonEmptyStr = 'Test';
    System.assertEquals(
      false,
      CollectionUtils.isNullOrBlank(nonEmptyStr),
      'Non-empty string should return false'
    );

    // Test number
    Integer num = 123;
    System.assertEquals(
      false,
      CollectionUtils.isNullOrBlank(num),
      'Number should return false'
    );

    // Test boolean
    Boolean bool = true;
    System.assertEquals(
      false,
      CollectionUtils.isNullOrBlank(bool),
      'Boolean should return false'
    );
  }

  @IsTest
  static void testListStringMethods() {
    // Test empty List<String>
    List<String> emptyStrList = new List<String>();
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(emptyStrList),
      'Empty String list should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(emptyStrList),
      'Empty String list should return false for isNotEmpty'
    );

    // Test null List<String>
    List<String> nullStrList;
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(nullStrList),
      'Null String list should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(nullStrList),
      'Null String list should return false for isNotEmpty'
    );

    // Test populated List<String>
    List<String> populatedStrList = new List<String>{ 'Test', 'String' };
    System.assertEquals(
      false,
      CollectionUtils.isEmpty(populatedStrList),
      'Populated String list should return false'
    );
    System.assertEquals(
      true,
      CollectionUtils.isNotEmpty(populatedStrList),
      'Populated String list should return true for isNotEmpty'
    );
  }

  @IsTest
  static void testMapIdStringMethods() {
    // Create test data
    Account testAccount = new Account(Name = 'Test Account');
    insert testAccount;

    // Test empty Map<Id, String>
    Map<Id, String> emptyIdStrMap = new Map<Id, String>();
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(emptyIdStrMap),
      'Empty Id-String map should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(emptyIdStrMap),
      'Empty Id-String map should return false for isNotEmpty'
    );

    // Test null Map<Id, String>
    Map<Id, String> nullIdStrMap;
    System.assertEquals(
      true,
      CollectionUtils.isEmpty(nullIdStrMap),
      'Null Id-String map should return true'
    );
    System.assertEquals(
      false,
      CollectionUtils.isNotEmpty(nullIdStrMap),
      'Null Id-String map should return false for isNotEmpty'
    );

    // Test populated Map<Id, String>
    Map<Id, String> populatedIdStrMap = new Map<Id, String>{
      testAccount.Id => 'Test Value'
    };
    System.assertEquals(
      false,
      CollectionUtils.isEmpty(populatedIdStrMap),
      'Populated Id-String map should return false'
    );
    System.assertEquals(
      true,
      CollectionUtils.isNotEmpty(populatedIdStrMap),
      'Populated Id-String map should return true for isNotEmpty'
    );
  }
}
