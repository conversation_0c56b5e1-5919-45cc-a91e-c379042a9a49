/********************************************************************************************

   @ Func Area	:  Note Migration/Import

   @ Author	:  <PERSON>

   @ Date	:  21 October 2024

   @ Description	:   This class is responsible for inserting new notes into SFDC

   @ Developer Notes   : https://salesforce.stackexchange.com/questions/336644/migrating-notes-into-salesforce

   @ Class Tested	:   NoteCreationTrigger

   @ Github Repo	:   https://github.com/mitchspano/apex-trigger-actions-framework
                        https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 21 October 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

@IsTest
public class NoteCreationTest {
  static testMethod void coverageTest() {
    Account a = new Account(Name = 'Test Account');
    insert a;

    insert new NoteCreation__c(Content__c = 'Content', Title__c = 'Title', RelatedRecordId__c = a.Id, OwnerId = UserInfo.getUserId());
  }
}