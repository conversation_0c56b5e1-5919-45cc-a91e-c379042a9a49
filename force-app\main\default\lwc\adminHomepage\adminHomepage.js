import { LightningElement, api } from "lwc";
import { NavigationMixin } from "lightning/navigation";

export default class AdminHomepage extends NavigationMixin(LightningElement) {
  @api recordId;
  // Navigate to New Account Page
  /*  navigateToNewAccountPage() {
        this[NavigationMixin.Navigate]({
            type: 'standard__objectPage',
            attributes: {
                objectApiName: 'Account',
                actionName: 'new'
            },
        });
    } */
  // Navigate to New Account Page
  navigateToNewAccountPage() {
    window.open(window.location.origin + "/lightning/o/Account/new?count=1");

    /*this[NavigationMixin.GenerateUrl]({
            type: 'standard__objectPage',
            attributes: {
                objectApiName: 'Account',
                actionName: 'new'
            },
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }
  // Navigate to New Opportunity Page
  navigateToNewOpportunityPage() {
    window.open(
      window.location.origin + "/lightning/o/Opportunity/new?count=1"
    );
    /*this[NavigationMixin.GenerateUrl]({
            type: 'standard__objectPage',
            attributes: {
                objectApiName: 'Opportunity',
                actionName: 'new'
            },
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigate to View Account Page
  navigateToViewAccountPage() {
    window.open(window.location.origin + "/lightning/o/Account/list");
    /*this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: this.recordId,
                objectApiName: 'Account',
                actionName: 'view'
            },
        });*/
  }
  // Navigate to Edit Account Page
  navigateToEditAccountPage() {
    this[NavigationMixin.Navigate]({
      type: "standard__recordPage",
      attributes: {
        recordId: this.recordId,
        objectApiName: "Account",
        actionName: "edit"
      }
    });
  }

  // Navigation to Account List view(recent)
  navigateToAccountListView() {
    window.open(window.location.origin + "/lightning/o/Account/list");
    /*this[NavigationMixin.GenerateUrl]({
            type: 'standard__objectPage',
            attributes: {
                objectApiName: 'Account',
                actionName: 'list'
            },
            state: {
                filterName: 'Recent'
            },
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to Account List view(recent)
  navigateToOpportunityListView() {
    window.open(window.location.origin + "/lightning/o/Opportunity/list");
    /*this[NavigationMixin.GenerateUrl]({
            type: 'standard__objectPage',
            attributes: {
                objectApiName: 'Opportunity',
                actionName: 'list'
            },
            state: {
                filterName: 'Recent'
            },
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to Account List view(recent)
  navigateToCaseListView() {
    this[NavigationMixin.GenerateUrl]({
      type: "standard__objectPage",
      attributes: {
        objectApiName: "Case",
        actionName: "list"
      },
      state: {
        filterName: "Recent"
      }
    }).then((generatedUrl) => {
      window.open(generatedUrl);
    });
  }

  // Navigation to Contact related list of account
  navigateToContactRelatedList() {
    this[NavigationMixin.Navigate]({
      type: "standard__recordRelationshipPage",
      attributes: {
        recordId: this.recordId,
        objectApiName: "Account",
        relationshipApiName: "Contacts",
        actionName: "view"
      }
    });
  }

  // Navigation to Setup main menu
  navigateToHomePage() {
    window.open(window.location.origin + "/lightning/setup/SetupOneHome/home");

    /* console.log('window.location.origin==>' + window.location.origin);
        this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/lightning/setup/SetupOneHome/home"
            }
        }).then(generatedUrl => {
            window.open(window.location.origin+"/lightning/setup/SetupOneHome/home");
        });*/
  }

  // Navigation to contant object home page
  navigateToContactHome() {
    this[NavigationMixin.Navigate]({
      type: "standard__objectPage",
      attributes: {
        objectApiName: "Contact",
        actionName: "home"
      }
    });
  }
  //Navigate to chatter
  navigateToChatter() {
    this[NavigationMixin.Navigate]({
      type: "standard__namedPage",
      attributes: {
        pageName: "chatter"
      }
    });
  }
  //Navigate to Reports
  navigateToReports() {
    this[NavigationMixin.Navigate]({
      type: "standard__objectPage",
      attributes: {
        objectApiName: "Report",
        actionName: "home"
      }
    });
  }
  //Navigate to Files home
  navigateToFilesHome() {
    this[NavigationMixin.Navigate]({
      type: "standard__objectPage",
      attributes: {
        objectApiName: "ContentDocument",
        actionName: "home"
      }
    });
  }
  // Navigation to lightning component
  navigateToLightningComponent() {
    this[NavigationMixin.Navigate]({
      type: "standard__component",
      attributes: {
        //Here customLabelExampleAura is name of lightning aura component
        //This aura component should implement lightning:isUrlAddressable
        componentName: "c__customLabelExampleAura"
      }
    });
  }

  // Navigation to web page
  navigateToObjectManager() {
    window.open(window.location.origin + "/lightning/setup/ObjectManager/home");
    /*this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/lightning/setup/ObjectManager/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to web page
  navigateToInboundChangeset() {
    window.open(
      window.location.origin + "/lightning/setup/InboundChangeSet/home"
    );
    /*this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/lightning/setup/InboundChangeSet/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to web page
  navigateToOutboundChangeset() {
    window.open(
      window.location.origin + "/lightning/setup/OutboundChangeSet/home"
    );

    /*this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/lightning/setup/OutboundChangeSet/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to web page
  navigateToPermissionSets() {
    window.open(window.location.origin + "/lightning/setup/PermSets/home");
    /*this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/one/one.app#/setup/PermSets/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to web page
  navigateToProfiles() {
    window.open(
      window.location.origin + "/lightning/setup/EnhancedProfiles/home"
    );
    /*this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/one/one.app#/setup/EnhancedProfiles/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to web page
  navigateToUsers() {
    window.open(window.location.origin + "/lightning/setup/ManageUsers/home");
    /*this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/lightning/setup/ManageUsers/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to web page
  navigateToGroups() {
    window.open(window.location.origin + "/lightning/setup/PublicGroups/home");
    /*this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/lightning/setup/PublicGroups/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to web page
  navigateToQueues() {
    window.open(window.location.origin + "/lightning/setup/Queues/home");
    /*this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/lightning/setup/Queues/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to web page
  navigateToEmailServices() {
    this[NavigationMixin.GenerateUrl]({
      type: "standard__webPage",
      attributes: {
        url:
          window.location.origin + "/lightning/setup/EmailToApexFunction/home"
      }
    }).then((generatedUrl) => {
      window.open(generatedUrl);
    });
  }

  // Navigation to web page
  navigateToFlows() {
    window.open(window.location.origin + "/lightning/setup/Flows/home");
    /*this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/lightning/setup/Flows/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to web page
  navigateToProcessBuilder() {
    window.open(
      window.location.origin + "/lightning/setup/ProcessAutomation/home"
    );
    /*this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/lightning/setup/ProcessAutomation/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });*/
  }

  // Navigation to web page
  navigateToWorkflowRules() {
    this[NavigationMixin.GenerateUrl]({
      type: "standard__webPage",
      attributes: {
        url: window.location.origin + "/lightning/setup/WorkflowRules/home"
      }
    }).then((generatedUrl) => {
      window.open(generatedUrl);
    });
  }

  // Navigation to web page
  navigateToEmailAlerts() {
    this[NavigationMixin.GenerateUrl]({
      type: "standard__webPage",
      attributes: {
        url: window.location.origin + "/lightning/setup/WorkflowEmails/home"
      }
    }).then((generatedUrl) => {
      window.open(generatedUrl);
    });
  }

  // Navigation to web page
  navigateToCustomMetadata() {
    window.open(
      window.location.origin + "/lightning/setup/CustomMetadata/home"
    );
    /*
        this[NavigationMixin.GenerateUrl]({
            "type": "standard__webPage",
            "attributes": {
                "url": window.location.origin+"/lightning/setup/CustomMetadata/home"
            }
        }).then(generatedUrl => {
            window.open(generatedUrl);
        });
        */
  }

  // Navigation to web page
  navigateToDebugLogs() {
    window.open(window.location.origin + "/lightning/setup/ApexDebugLogs/home");
    /*this[NavigationMixin.GenerateUrl]({
      type: "standard__webPage",
      attributes: {
        url: window.location.origin + "/lightning/setup/ApexDebugLogs/home"
      }
    }).then((generatedUrl) => {
      window.open(generatedUrl);
    });
    */
  }

  //Navigate to visualforce page
  navigateToVFPage() {
    this[NavigationMixin.GenerateUrl]({
      type: "standard__webPage",
      attributes: {
        url: "/apex/AccountVFExample?id=" + this.recordId
      }
    }).then((generatedUrl) => {
      window.open(generatedUrl);
    });
  }
  // Navigation to Custom Tab
  navigateToTab() {
    this[NavigationMixin.Navigate]({
      type: "standard__navItemPage",
      attributes: {
        //Name of any CustomTab. Visualforce tabs, web tabs, Lightning Pages, and Lightning Component tabs
        apiName: "CustomTabName"
      }
    });
  }
}