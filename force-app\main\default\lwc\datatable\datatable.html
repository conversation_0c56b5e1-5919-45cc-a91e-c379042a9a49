<!-- 
    
Lightning Web Component for Flow Screens:       datatable

This component allows the user to configure and display a datatable in a Flow screen.

CREATED BY:         <PERSON>

VERSION:            4.x.x

RELEASE NOTES:      https://github.com/alexed1/LightningFlowComponents/tree/master/flow_screen_components/datatable/README.md

04/01/20 -  <PERSON> -    Version 1.0
Features:   The only required paramters are the SObject collection of records and a list of field API names
            The field label and field type will default to what is defined in the object
            Numeric fields will display with the correct number of decimal places as defined in the object
            Lookup fields are supported and will display the referenced record's name field as a clickable link
            All columns are sortable, including lookups (by name)
            The selection column can be multi-select (Checkboxes), single-select (Radio Buttons), or hidden
            A collection of pre-selected rows can be passed into the component
            Inline editing is supported with changed values passed back to the flow
            Unlike the original datatable component, only the edited records will be passed back to the flow
            The maximum number of rows to display can be set by the user
            Optional attribute overrides are supported and can be specified by list, column # or by field name, including:
                Alignment               
                Editable
                Header Icon
                Header Label
                Initial Column Width
                Custom Cell Attributes including those with nested values {name: {name:value}}               
                Custom Type Attributes including those with nested values {name: {name:value}}
                Other Custom Column Attributes including those with nested values {name: {name:value}}

-->

<template>
    <template if:false={showSpinner}>

        <!-- Input dialog for entering Column Icon values -->
        <template if:true={isOpenIconInput}>
            <div style="height: 400px">
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-00" aria-modal="true" aria-describedby="modal-content-id-0" class="slds-modal slds-fade-in-open slds-modal_large">
                    <div class="slds-modal__container">
                        <header class="slds-modal__header slds-modal__header_empty">
                            <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={handleCloseIconModal}>
                                <lightning-icon icon-name="utility:close" alternative-text="close" variant="inverse" size="small"></lightning-icon>
                                <span class="slds-assistive-text">Close</span>
                            </button>
                        </header>
                        <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-0" style="height: 400px">
                            <c-fsc_pick-icon
                                first-tab-height="400"
                                mode="combobox"
                                icon-categories="standard,utility,custom"
                                oniconselection={handlePickIcon}>
                            </c-fsc_pick-icon>
                        </div>
                        <footer class="slds-modal__footer slds-modal__footer_directional">
                            <button class="slds-button slds-button_neutral" onclick={handleCloseIconModal}>{label.CancelButton}</button>
                            <button class="slds-button slds-button_brand" onclick={handleCommitIconSelection}>{label.SaveButton}</button>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </template>
        
        <!-- Input dialog for entering Column Filter values -->
        <template if:true={isOpenFilterInput}>
            <div style="height: 40px;">
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container">
                        <header class="slds-modal__header slds-modal__header_empty">
                            <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={handleCloseModal}>
                                <lightning-icon icon-name="utility:close" alternative-text="close" variant="inverse" size="small"></lightning-icon>
                                <span class="slds-assistive-text">Close</span>
                            </button>
                        </header>
                        <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                            <lightning-input 
                                type={inputType}
                                formatter={inputFormat} 
                                label={inputLabel} 
                                value={columnFilterValue} 
                                onchange={handleFilterChange}
                            ></lightning-input>
                            <template lwc:if={isFilterDialog}>
                                <lightning-input
                                    type="checkbox" 
                                    label={filterBlankLabel}
                                    checked={isFilterBlankValues}
                                    field-level-help={filterBlankHelpText}
                                    onchange={handleFilterBlankChange}
                                ></lightning-input>
                            </template>
                        </div>
                        <footer class="slds-modal__footer slds-modal__footer_directional">
                            <button class="slds-button slds-button_neutral" onclick={handleCloseModal}>{label.CancelButton}</button>
                            <button class="slds-button slds-button_brand" onclick={handleCommit}>{label.SaveButton}</button>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </template>

        <!-- Special Header parameter display for Configuration Mode -->
        <template if:true={isConfigMode}>
            <div class="slds-text-longform">
                <h3 class="slds-text-heading_medium">Interact with this sample datatable to configure it the way you like.</h3>
                <ul>
                    <li>Drag the separators between the column headers to change the widths.</li>
                    <li>Select the column header dropdowns to change Text Wrap/Clip Modes, Column Alignments, Column Icons, Column Labels and Column Width Behaviors.</li>
                    <li>Use the dropdowns to select the columns that can be Edited and/or Filtered or click a Button to select all of them.</li>
                </ul>
            </div>
        </template>

        <!-- DATATABLE -->
        <div class={formElementClass}>
            <!-- New Format with Searchbar and/or Pagination -->
            <template lwc:if={isShowNewheader}>
                <abbr class="slds-required" title="required">{requiredSymbol}</abbr>
                <lightning-card variant="Narrow" icon-name={tableIcon}>
                    <div slot="title">
                        <div style="font-size: 1.2em; font-weight: bolder;">
                            {formattedTableLabel}
                        </div>
                    </div>
                    <div slot="actions">
                        <div class="slds-grid slds-grid_align-end">
                            <template lwc:if={isPagination}>
                                <legend class="slds-form-element__legend slds-form-element__label">{recordCountLabel}</legend>
                                <lightning-input
                                    type="number"
                                    min="1"
                                    value={recordCountPerPage}
                                    variant="label-hidden"
                                    onchange={handleRecordCountChange}
                                ></lightning-input>
                            </template>
                            <template lwc:if={isShowSearchBar}>
                                <div class="slds-p-left_medium">
                                    <lightning-input 
                                        type="search"
                                        placeholder={searchPlaceholder}
                                        value={searchTerm}
                                        variant="label-hidden"
                                        onchange={handleSearchChange}
                                        is-loading={isWorking}
                                    ></lightning-input>
                                </div>
                            </template>
                        </div>
                    </div>
                    <template if:true={isShowTable}>
                        <div if:true={isWorking}>
                            <lightning-spinner
                                alternative-text="Working..." variant="brand">
                            </lightning-spinner>
                        </div>
        
                        <div if:false={isWorking} style={tableHeightAttribute} class={borderClass} id="datatableCard">
        
                            <c-ers_custom-lightning-datatable
                                aria-label={tableLabel}
                                data={paginatedData}
                                columns={columns}
                                key-field={keyField}
                                sorted-by={sortedBy}
                                sorted-direction={sortDirection}
                                max-row-selection={maxRowSelection}
                                min-column-width={minColumnWidth}
                                max-column-width={maxColumnWidth}
                                selected-rows={visibleSelectedRowIds}
                                show-row-number-column={showRowNumbers}
                                hide-checkbox-column={hideCheckboxColumn}
                                suppress-bottom-bar={suppressBottomBar}
                                wrap-table-header={wrapTableHeader}
                                onsort={updateColumnSorting}
                                oncellchange={handleCellChange}
                                onsave={handleSave}
                                oncancel={cancelChanges}
                                onheaderaction={handleHeaderAction}
                                onrowselection={handleRowSelection}
                                onrowaction={handleRowAction}
                                onresize={handleResize}
                                oncombovaluechange={handleComboValueChange}
                            >
                            </c-ers_custom-lightning-datatable>
        
                            <div slot="footer">
                                <lightning-layout>
                                    <lightning-layout-item padding="right-small">
                                        <div if:true={showClearButton} class="slds-m-top_x-small slds-m-left_small">
                                            <lightning-button variant="brand-outline" label={label.ClearSelectionButton} icon-name="utility:clear" onclick={handleClearSelection}></lightning-button>
                                        </div>
                                    </lightning-layout-item>
                                    <lightning-layout-item>
                                        <div if:true={showClearFilterButton} class="slds-m-top_x-small slds-m-left_small">
                                            <lightning-button variant="brand-outline" label={label.ClearFilterButton} icon-name="utility:clear" onclick={handleClearFilterButton}></lightning-button>
                                        </div>
                                    </lightning-layout-item>
                                </lightning-layout>
                            </div>
        
                        </div>
        
                    </template>
        
                    <template if:false={isShowTable}>
                        <div if:true={haveRecords}>
                            <lightning-spinner
                                alternative-text="Working..." variant="brand">
                            </lightning-spinner>
                        </div>
                        <div if:false={haveRecords} class="slds-p-around_x-small slds-text-heading_small">
                            {emptyTableMessage}
                        </div>
                    </template>
                </lightning-card>
            </template>

            <!-- Old Format with Searchbar commented out -->
            <template if:false={isShowNewheader}>
                <template if:true={isDisplayHeader}>
                    <div class="slds-grid slds-grid_align-spread">
                        <div class="slds-col">
                            <label class="slds-form-element__label" for="datatable">
                                <div class="slds-media slds-media_center slds-media_small">
                                    <abbr class="slds-required" title="required">{requiredSymbol}</abbr>
                                    <div class="slds-media__figure">
                                        <template if:true={hasIcon}>
                                            <lightning-icon icon-name={tableIcon} alternative-text="Datatable Icon" title="Icon" size="small"></lightning-icon>
                                        </template>
                                    </div>
                                    <div class="slds-media__body">
                                        <div style="font-size: 1.2em; font-weight: bolder;">&nbsp;{formattedTableLabel}</div>
                                    </div>
                                </div>
                            </label>
                        </div>
                        <!-- <div if:true={isShowSearchBar}> 
                            <div class="slds-col slds-p-bottom_xx-small">
                                <lightning-input 
                                    type="search"
                                    placeholder="Enter search term ..."
                                    value={searchTerm}
                                    onchange={handleSearchChange}
                                    is-loading={isWorking}
                                ></lightning-input>
                            </div>
                        </div> -->
                    </div>
                </template>

                <template if:true={isShowTable}>

                    <div if:true={isWorking}>
                        <lightning-spinner
                            alternative-text="Working..." variant="brand">
                        </lightning-spinner>
                    </div>

                    <div if:false={isWorking} style={tableHeightAttribute} class={borderClass} id="datatable">
                        <c-ers_custom-lightning-datatable
                            aria-label={tableLabel}
                            data={paginatedData}
                            columns={columns}
                            key-field={keyField}
                            sorted-by={sortedBy}
                            sorted-direction={sortDirection}
                            max-row-selection={maxRowSelection}
                            min-column-width={minColumnWidth}
                            max-column-width={maxColumnWidth}
                            selected-rows={visibleSelectedRowIds}
                            show-row-number-column={showRowNumbers}
                            hide-checkbox-column={hideCheckboxColumn}
                            suppress-bottom-bar={suppressBottomBar}
                            wrap-table-header={wrapTableHeader}
                            onsort={updateColumnSorting}
                            oncellchange={handleCellChange}
                            onsave={handleSave}
                            oncancel={cancelChanges}
                            onheaderaction={handleHeaderAction}
                            onrowselection={handleRowSelection}
                            onrowaction={handleRowAction}
                            onresize={handleResize}
                            oncombovaluechange={handleComboValueChange}
                        >
                        </c-ers_custom-lightning-datatable>

                        <lightning-layout>
                            <lightning-layout-item padding="right-small">
                                <div if:true={showClearButton} class="slds-m-top_x-small slds-m-left_small">
                                    <lightning-button variant="brand-outline" label={label.ClearSelectionButton} icon-name="utility:clear" onclick={handleClearSelection}></lightning-button>
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item>
                                <div if:true={showClearFilterButton} class="slds-m-top_x-small slds-m-left_small">
                                    <lightning-button variant="brand-outline" label={label.ClearFilterButton} icon-name="utility:clear" onclick={handleClearFilterButton}></lightning-button>
                                </div>
                            </lightning-layout-item>
                        </lightning-layout>
                    </div>
                </template>

                <template if:false={isShowTable}>
                    <div if:true={haveRecords}>
                        <lightning-spinner
                            alternative-text="Working..." variant="brand">
                        </lightning-spinner>
                    </div>
                    <div if:false={haveRecords} class="slds-p-around_x-small slds-text-heading_small">
                        {emptyTableMessage}
                    </div>
                </template>
            </template>
        </div>

        <!-- Pagination Footer -->
        <template lwc:if={isPagination}>
            <div class="slds-grid slds-grid_align-center">
                <template lwc:if={isShowButtonFirstLast}>
                    <div class="slds-col slds-align-middle slds-p-top_medium slds-p-right_xx-small">
                        <lightning-button
                            label={buttonFirstLabel}
                            variant="brand-outline"
                            icon-position="left"
                            icon-name="utility:arrow_left"
                            onclick={handleButtonFirst}
                            disabled={isFirstPage}
                        ></lightning-button>
                    </div>
                </template>
                <div class="slds-col slds-align-middle slds-p-top_medium">
                    <lightning-button
                        label={buttonPrevLabel}
                        variant="brand-outline"
                        icon-position="left"
                        icon-name="utility:chevronleft"
                        onclick={handleButtonPrev}
                        disabled={isFirstPage}
                    ></lightning-button>
                </div>
                <div class="slds-col slds-text-body_regular slds-p-horizontal_medium slds-align-middle">
                    <lightning-input
                        type="number"
                        min="1"
                        max={pageTotalCount}
                        label={pageNumberLabel}
                        value={pageCurrentNumber}
                        variant="label-stacked"
                        onchange={handlePageChange}
                        disabled={isOnlyOnePage}
                    ></lightning-input>
                </div>
                <div class="slds-col slds-align-middle slds-p-top_medium">
                    <lightning-button
                        label={buttonNextLabel}
                        variant="brand-outline"
                        icon-position="right"
                        icon-name="utility:chevronright"
                        onclick={handleButtonNext}
                        disabled={isLastpage}
                    ></lightning-button>
                </div>
                <template lwc:if={isShowButtonFirstLast}>
                    <div class="slds-col slds-align-middle slds-p-top_medium slds-p-left_xx-small">
                        <lightning-button
                            label={buttonLastLabel}
                            variant="brand-outline"
                            icon-position="right"
                            icon-name="utility:arrow_right"
                            onclick={handleButtonLast}
                            disabled={isLastpage}
                        ></lightning-button>
                    </div>
                </template>
            </div>
        </template>

        <!-- Special Footer parameter display for Configuration Mode -->
        <template if:true={isConfigMode}>
            
            <div class="slds-box slds-box_xx-small slds-m-vertical_xx-small slds-m-horizontal_medium">
                <div class="slds-p-around_xx-small">
                    <span class="slds-text-heading_small">{columnWidthsLabel}
                        <lightning-button
                            label={roundValueLabel} variant="brand-outline" onclick={handleRoundWidths} class="slds-text-body_small slds-float_right">
                        </lightning-button>
                        <p class="slds-text-body_small">{columnWidthParameter}</p>
                    </span>
                </div>
            </div>

            <div class="slds-m-horizontal_medium">
                <div class="slds-box slds-box_x-small slds-m-vertical_xx-small">
                    <lightning-button-stateful class="slds-m-right_small"
                        label-when-off="Make all columns editable" 
                        label-when-on="Make no columns editable"
                        variant="neutral"
                        icon-name-when-off="utility:add"
                        icon-name-when-on="utility:undo"
                        selected={isAllEdit}
                        onclick={handleSelectAllEdit}>
                    </lightning-button-stateful>
                    <lightning-button-stateful class="slds-m-right_small"
                        label-when-off="Make all columns filterable"
                        label-when-on="Make no columns filterable"
                        variant="neutral"
                        icon-name-when-off="utility:add"
                        icon-name-when-on="utility:undo"
                        selected={isAllFilter}
                        onclick={handleSelectAllFilter}>
                    </lightning-button-stateful>
                    <lightning-button-stateful class="slds-m-right_small"
                        label-when-off="Make all column widths flexible"
                        label-when-on="Make no column widths flexible"
                        variant="neutral"
                        icon-name-when-off="utility:add"
                        icon-name-when-on="utility:undo"
                        selected={isAllFlex}
                        onclick={handleSelectAllFlex}>
                    </lightning-button-stateful>
                </div>          
            </div>

            <hr class="slds-m-vertical_small">

            <!-- TODO: Add ability to update special attributes in the Configuration Wizard -->
            <!-- <div class="slds-m-horizontal_medium">
                <div class="slds-box slds-box_x-small slds-m-vertical_xx-small">
                    <lightning-input 
                        type="text"
                        label="Cell Attributes" 
                        name="attribCell"
                        value={attribCell} 
                        onchange={handleAttributeChange}
                    ></lightning-input>
                </div>          
            </div> -->

        </template>

    </template>

    <!-- Spinner -->
    <template if:true={showSpinner}>
        <lightning-spinner alternative-text="Loading" size="medium"></lightning-spinner>
    </template> 

</template>