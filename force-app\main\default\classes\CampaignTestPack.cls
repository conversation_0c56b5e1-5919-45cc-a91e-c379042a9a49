/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  04 April 2025

   @ Description	:   A test class containing test methods for apex automation on the Campaign Object

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   CampaignSelector

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 04 April 2025
   @ Last Modified Reason  : Creation



********************************************************************************************/
@isTest
public class CampaignTestPack {
  //Test CampaignSelector class
  @isTest
  static void test_CampaignSelector() {
    CampaignSelector campaignSelector = new CampaignSelector();
    Integer queryLimit = 100;
    Set<Id> campaignIds = new Set<Id>{
      MockerUtils.generateId(Campaign.SObjectType),
      MockerUtils.generateId(Campaign.SObjectType)
    };

    Test.startTest();
    campaignSelector.setQueryLimit(queryLimit);
    campaignSelector.selectCampaignsByIds(campaignIds);

    Test.stopTest();
  }
}