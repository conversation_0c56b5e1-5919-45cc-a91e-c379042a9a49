<template>

    <!-- =============== Data Source Banner =============== -->
    <c-fsc_flow-banner 
        banner-color={colorWizardOverride}  
        banner-label={sectionEntries.dataSource.label}
        banner-info={sectionEntries.dataSource.info}
        modal-header-color={modalHeaderColorWizardOverride}
    ></c-fsc_flow-banner>

    <!-- Select SObject -->
    <div class="slds-box slds-box_x-small slds-m-top_x-small">
        <div class={inputValues.objectName.class}>
            <div class="slds-form-element__control">
                <c-fsc_pick-object-and-field-3 
                    if:true={isSObjectInput}
                    field-label={inputValues.fieldName.label}
                    object-label={inputValues.objectName.label} 
                    object-type={selectedSObject}
                    is-allow-all={isDisplayAll}
                    available-object-types={availableObjectTypes}
                    field={inputValues.fieldName.value} 
                    onfieldselected={handleDynamicTypeMapping}
                    hide-field-picklist=true>
                </c-fsc_pick-object-and-field-3>
            </div>
            <c-fsc_display-error 
                is-error={inputValues.objectName.isError}
                error-message={inputValues.objectName.errorMessage}
            ></c-fsc_display-error>
        </div>

        <!-- Display ALL Objects for Selection -->
        <c-fsc_flow-checkbox
            if:true={isSObjectInput}
            name="select_displayAll"
            label={inputValues.displayAll.label}
            checked={inputValues.cb_displayAll.value}
            field-level-help={inputValues.displayAll.helpText}
            oncheckboxchanged={handleCheckboxChange}
        ></c-fsc_flow-checkbox>
    </div>

    <!-- Data Source -->
    <div class="slds-box slds-box_x-small slds-m-top_x-small">
        <div class={inputValues.tableData.class}>
            <div class="slds-form-element__control">
                <c-fsc_flow-combobox 
                    if:true={isObjectSelected}
                    name="select_tableData" 
                    label={inputValues.tableData.label}
                    value={inputValues.tableData.value} 
                    value-type={inputValues.tableData.valueDataType}
                    field-level-help={inputValues.tableData.helpText}
                    builder-context-filter-type={selectedSObject}
                    builder-context-filter-collection-boolean={inputValues.tableData.isCollection}
                    builder-context={_builderContext}
                    allow-hard-code-reference=true
                    placeholder-text="Manual entry allowed"
                    onvaluechanged={handleFlowComboboxValueChange}
                    automatic-output-variables={automaticOutputVariables}>
                </c-fsc_flow-combobox>
            </div>
            <c-fsc_display-error 
                is-error={inputValues.tableData.isError}
                error-message={inputValues.tableData.errorMessage}
            ></c-fsc_display-error> 
        </div>

        <c-fsc_flow-combobox 
            if:false={isSObjectInput}
            name="select_tableDataString" 
            label={inputValues.tableDataString.label}
            value={inputValues.tableDataString.value} 
            value-type={inputValues.tableDataString.valueDataType}
            field-level-help={inputValues.tableDataString.helpText}
            builder-context-filter-collection-boolean={inputValues.tableDataString.isCollection}
            builder-context={_builderContext} 
            onvaluechanged={handleFlowComboboxValueChange}
            automatic-output-variables={automaticOutputVariables}>
        </c-fsc_flow-combobox>

        <!-- Pre-Selected Rows -->
        <c-fsc_flow-combobox 
            if:true={isObjectSelected}
            name="select_preSelectedRows" 
            label={inputValues.preSelectedRows.label}
            value={inputValues.preSelectedRows.value} 
            value-type={inputValues.preSelectedRows.valueDataType}
            field-level-help={inputValues.preSelectedRows.helpText}
            builder-context-filter-type={selectedSObject}
            builder-context-filter-collection-boolean={inputValues.preSelectedRows.isCollection}
            builder-context={_builderContext} 
            allow-hard-code-reference=true
            placeholder-text="Manual entry allowed"
            onvaluechanged={handleFlowComboboxValueChange}
            automatic-output-variables={automaticOutputVariables}>
        </c-fsc_flow-combobox>

        <c-fsc_flow-combobox
            if:false={isSObjectInput}
            name="select_preSelectedRowsString" 
            label={inputValues.preSelectedRowsString.label}
            value={inputValues.preSelectedRowsString.value} 
            value-type={inputValues.preSelectedRowsString.valueDataType}
            field-level-help={inputValues.preSelectedRowsString.helpText}
            builder-context-filter-collection-boolean={inputValues.preSelectedRowsString.isCollection}
            builder-context={_builderContext} 
            onvaluechanged={handleFlowComboboxValueChange}
            automatic-output-variables={automaticOutputVariables}>
        </c-fsc_flow-combobox>
    </div>

    <!-- =============== Table Formatting Banner =============== -->
    <c-fsc_flow-banner
        banner-color={defaultBannerColor}  
        banner-label={sectionEntries.tableFormatting.label}
        banner-info={sectionEntries.tableFormatting.info}
        modal-header-color={defaultModalHeaderColor}
    ></c-fsc_flow-banner>

    <!-- Table Header -->
    <div class="slds-box slds-box_x-small slds-m-top_small">
        <!-- Display Header Checkbox -->
        <c-fsc_flow-checkbox
            name="select_isDisplayHeader"
            label={inputValues.isDisplayHeader.label} 
            checked={inputValues.cb_isDisplayHeader.value} 
            field-level-help={inputValues.isDisplayHeader.helpText}
            oncheckboxchanged={handleCheckboxChange}
        ></c-fsc_flow-checkbox>
        <c-fsc_flow-combobox 
            name="select_tableLabel" 
            label={inputValues.tableLabel.label}
            value={inputValues.tableLabel.value} 
            value-type={inputValues.tableLabel.valueDataType}
            field-level-help={inputValues.tableLabel.helpText}
            disabled={disallowHeaderChange}
            builder-context-filter-collection-boolean={inputValues.tableLabel.isCollection}
            builder-context={_builderContext} 
            onvaluechanged={handleFlowComboboxValueChange}
            automatic-output-variables={automaticOutputVariables}>
        </c-fsc_flow-combobox>
        <c-fsc_flow-combobox 
            name="select_tableIcon" 
            label={inputValues.tableIcon.label}
            value={inputValues.tableIcon.value} 
            value-type={inputValues.tableIcon.valueDataType}
            field-level-help={inputValues.tableIcon.helpText}
            disabled={disallowHeaderChange}
            builder-context-filter-collection-boolean={inputValues.tableIcon.isCollection}
            builder-context={_builderContext} 
            onvaluechanged={handleFlowComboboxValueChange}
            automatic-output-variables={automaticOutputVariables}>
        </c-fsc_flow-combobox>
        <c-fsc_pick-icon
            mode="combobox"
            oniconselection={handleTableIcon}>
        </c-fsc_pick-icon>
    </div>

    <!-- Max Record Count -->  
    <c-fsc_flow-combobox 
        name="select_maxNumberOfRows" 
        label={inputValues.maxNumberOfRows.label}
        value={inputValues.maxNumberOfRows.value} 
        value-type={inputValues.maxNumberOfRows.valueDataType}
        field-level-help={inputValues.maxNumberOfRows.helpText}
        builder-context-filter-type="Number"
        builder-context-filter-collection-boolean={inputValues.maxNumberOfRows.isCollection}
        builder-context={_builderContext} 
        onvaluechanged={handleFlowComboboxValueChange}
        automatic-output-variables={automaticOutputVariables}>
    </c-fsc_flow-combobox>

    <!-- Pagination -->
    <div class={paginationClass}>
        <div class={paginationCheckboxClass}>
            <c-fsc_flow-checkbox
                name="select_showPagination"
                label={inputValues.showPagination.label} 
                checked={inputValues.cb_showPagination.value} 
                field-level-help={inputValues.showPagination.helpText}
                oncheckboxchanged={handleCheckboxChange}
            ></c-fsc_flow-checkbox>
        </div>

        <div class={showHidePaginationAttributes}>
            <c-fsc_flow-combobox 
                name="select_recordsPerPage" 
                label={inputValues.recordsPerPage.label}
                value={inputValues.recordsPerPage.value} 
                value-type={inputValues.recordsPerPage.valueDataType}
                field-level-help={inputValues.recordsPerPage.helpText}
                builder-context-filter-type="Number"
                builder-context-filter-collection-boolean={inputValues.recordsPerPage.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <div class="slds-m-top_xx-small">
                <c-fsc_flow-checkbox
                    name="select_showFirstLastButtons"
                    label={inputValues.showFirstLastButtons.label} 
                    checked={inputValues.cb_showFirstLastButtons.value} 
                    field-level-help={inputValues.showFirstLastButtons.helpText}
                    oncheckboxchanged={handleCheckboxChange}
                ></c-fsc_flow-checkbox>
            </div>
        </div>
    </div>

    <!-- Other Table Formatting -->
    <c-fsc_flow-checkbox
        name="select_showRowNumbers"
        label={inputValues.showRowNumbers.label} 
        checked={inputValues.cb_showRowNumbers.value} 
        field-level-help={inputValues.showRowNumbers.helpText}
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>

    <c-fsc_flow-checkbox
        name="select_showRecordCount"
        label={inputValues.showRecordCount.label} 
        checked={inputValues.cb_showRecordCount.value} 
        field-level-help={inputValues.showRecordCount.helpText}
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>
    
    <c-fsc_flow-checkbox
        disabled={disableSelectCountSelection}
        name="select_showSelectedCount"
        label={inputValues.showSelectedCount.label} 
        checked={inputValues.cb_showSelectedCount.value} 
        field-level-help={inputValues.showSelectedCount.helpText}
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>

    <c-fsc_flow-checkbox
        disabled={disableSearchBarSelection}
        name="select_isShowSearchBar"
        label={inputValues.isShowSearchBar.label}
        field-level-help={inputValues.isShowSearchBar.helpText}
        checked={inputValues.cb_isShowSearchBar.value}
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>

    <c-fsc_flow-checkbox
        name="select_tableBorder"
        label={inputValues.tableBorder.label} 
        checked={inputValues.cb_tableBorder.value} 
        field-level-help={inputValues.tableBorder.helpText}
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>

    <!-- =============== Column Wizard =============== -->
    <div class={inputValues.columnFields.class}>
        <div class="slds-form-element__control">
            <div if:true={isObjectSelected}> 
            
                <!-- Column Wizard -->
                <div class="slds-m-top_small">
                        <lightning-button label="Configure Columns" icon-name="utility:lightning_extension" onclick={showModal}></lightning-button>
                </div>

                <template if:true={openModal}>
                    <div class="slds-modal slds-modal_large slds-fade-in-open slds-backdrop">
                        <div class="slds-modal__container">

                            <!-- Header Section -->
                            <div class="slds-modal__header">
                                <lightning-button-icon icon-name="utility:close"
                                    alternative-text="Close this window" size="large" variant="bare-inverse"
                                    onclick={closeModal} class="slds-modal__close">
                                </lightning-button-icon>
                                <div if:false={isFlowLoaded}>
                                    <h1 class="slds-modal__title slds-hyphenate">Please Wait... Loading Field Details</h1>
                                </div>
                                <div if:true={isFlowLoaded}>
                                    <h2 class="slds-modal__title slds-hyphenate">Configure Columns</h2>
                                </div>
                            </div>

                            <!-- Body Section -->
                            <div class="slds-modal__content slds-p-around_medium">
                                <div if:false={isFlowLoaded}>
                                    <lightning-spinner alternative-text="Loading" size="large"></lightning-spinner>
                                </div>
                                <lightning-flow
                                    flow-api-name="Datatable_Configuration_Wizard5"
                                    onstatuschange={handleFlowStatusChange}
                                    flow-input-variables={wizardParams}
                                ></lightning-flow>
                            </div>

                            <!-- Footer Section -->
                            <!-- <div class="slds-modal__footer slds-modal__footer_directional">
                                <lightning-button label="Cancel" onclick={handleWizardCancel}></lightning-button>
                                <lightning-button label="Restart" onclick={handleWizardRestart}></lightning-button>
                                <lightning-button label={nextLabel} onclick={handleWizardNext} variant="brand" disabled={isNextDisabled}></lightning-button>
                                <lightning-slider value={wizardHeight} label="Wizard Height (pixels)"
                                    min="400" 
                                    max="1000" 
                                    step="10"
                                    onchange={handleHeightChange}>
                                </lightning-slider>
                            </div> -->

                        </div>
                    </div>
                </template>

            </div>
        </div>
        <c-fsc_display-error 
            is-error={inputValues.columnFields.isError}
            error-message={inputValues.columnFields.errorMessage}
        ></c-fsc_display-error>
    </div>

    <!-- =============== Table Behavior Banner =============== -->
    <c-fsc_flow-banner
        banner-label={sectionEntries.tableBehavior.label}
        banner-info={sectionEntries.tableBehavior.info}
    ></c-fsc_flow-banner>

    <!-- Table Behavior Options -->
    <c-fsc_flow-checkbox
        disabled={isCheckboxColumnHidden}           
        name="select_isRequired"
        label={inputValues.isRequired.label} 
        field-level-help={inputValues.isRequired.helpText}
        checked={inputValues.cb_isRequired.value} 
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>
    <c-fsc_flow-checkbox
        disabled={isCheckboxColumnHidden}
        name="select_singleRowSelection"
        label={inputValues.singleRowSelection.label} 
        field-level-help={inputValues.singleRowSelection.helpText}
        checked={inputValues.cb_singleRowSelection.value} 
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>   
    <c-fsc_flow-checkbox
        disabled={isShowCheckboxColumn}
        name="select_hideCheckboxColumn"
        label={inputValues.hideCheckboxColumn.label}
        field-level-help={inputValues.hideCheckboxColumn.helpText}
        checked={inputValues.cb_hideCheckboxColumn.value}
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>
    <c-fsc_flow-checkbox
        name="select_hideHeaderActions"
        label={inputValues.hideHeaderActions.label} 
        field-level-help={inputValues.hideHeaderActions.helpText}
        checked={inputValues.cb_hideHeaderActions.value} 
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>  
    <c-fsc_flow-checkbox
        disabled={isNoFilters}
        name="select_matchCaseOnFilters"
        label={inputValues.matchCaseOnFilters.label} 
        field-level-help={inputValues.matchCaseOnFilters.helpText}
        checked={inputValues.cb_matchCaseOnFilters.value} 
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>
    <c-fsc_flow-checkbox
        name="select_hideClearSelectionButton"
        label={inputValues.hideClearSelectionButton.label} 
        field-level-help={inputValues.hideClearSelectionButton.helpText}
        checked={inputValues.cb_hideClearSelectionButton.value} 
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>    
    <c-fsc_flow-checkbox
        disabled={isDisableSuppressBottomBar}
        name="select_suppressBottomBar"
        label={inputValues.suppressBottomBar.label} 
        field-level-help={inputValues.suppressBottomBar.helpText}
        checked={inputValues.cb_suppressBottomBar.value} 
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>  
    <c-fsc_flow-checkbox
        disabled={isDisableNavigateNext}
        name="select_navigateNextOnSave"
        label={inputValues.navigateNextOnSave.label} 
        field-level-help={inputValues.navigateNextOnSave.helpText}
        checked={inputValues.cb_navigateNextOnSave.value} 
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>  
    <c-fsc_flow-checkbox
        name="select_not_suppressNameFieldLink"
        label={inputValues.not_suppressNameFieldLink.label} 
        checked={inputValues.cb_not_suppressNameFieldLink.value} 
        field-level-help={inputValues.not_suppressNameFieldLink.helpText}
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox> 
    <c-fsc_flow-checkbox
        disabled={isNoLinks}
        name="select_openLinkinSameTab"
        label={inputValues.openLinkinSameTab.label} 
        checked={inputValues.cb_openLinkinSameTab.value} 
        field-level-help={inputValues.openLinkinSameTab.helpText}
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox> 

    <!-- =============== Row Actions Banner =============== -->
    <c-fsc_flow-banner
        banner-color={colorRowActionsOverride}
        banner-label={sectionEntries.rowActions.label}
        banner-info={sectionEntries.rowActions.info}
        modal-header-color={modalHeaderColorRowActionsOverride}
    ></c-fsc_flow-banner>

    <!-- Row Actions -->
    <div class={rowActionClass}>
        <div class={rowActionCheckboxClass}>
            <c-fsc_flow-checkbox
                name="select_isRemoveRowAction"
                label={inputValues.isRemoveRowAction.label} 
                checked={inputValues.cb_isRemoveRowAction.value} 
                field-level-help={inputValues.isRemoveRowAction.helpText}
                oncheckboxchanged={handleCheckboxChange}
            ></c-fsc_flow-checkbox>
        </div>

        <div class={showHideRowAction}>

            <div class={sampleActionClass}>
                <lightning-formatted-rich-text value="&lt;strong&gt;Sample Action Button&lt;/strong&gt;"></lightning-formatted-rich-text>
                <div class={inputValues.removeColor.value}>
                    <div class={showRowActionIcon}>
                        <lightning-button-icon
                            icon-name={inputValues.removeIcon.value}
                            tooltip={inputValues.removeLabel.value}
                            alternative-text={inputValues.removeLabel.value}
                            size="medium"
                            variant="border"
                        ></lightning-button-icon>
                    </div>
                    <div class={showButtonOptions}>
                        <lightning-button 
                            label={inputValues.rowActionButtonLabel.value}
                            title={inputValues.rowActionButtonLabel.value}
                            variant={inputValues.rowActionButtonVariant.value}
                            icon-name={inputValues.rowActionButtonIcon.value}
                            icon-position={inputValues.rowActionButtonIconPosition.value}
                        ></lightning-button>
                    </div>
                </div>
            </div>

            <lightning-radio-group
                name="select_rowActionType"
                label={inputValues.rowActionType.label}
                options={rowActionTypeOptions}
                value={inputValues.rowActionType.value}
                type="radio"
                onchange={handleValueChange}
            ></lightning-radio-group>

            <div class={showHideMaxNumberRows}>
                <c-fsc_flow-combobox 
                    name="select_maxRemovedRows" 
                    label={inputValues.maxRemovedRows.label}
                    value={inputValues.maxRemovedRows.value} 
                    value-type={inputValues.maxRemovedRows.valueDataType}
                    field-level-help={inputValues.maxRemovedRows.helpText}
                    builder-context-filter-type="Number"
                    builder-context-filter-collection-boolean={inputValues.maxRemovedRows.isCollection}
                    builder-context={_builderContext} 
                    onvaluechanged={handleFlowComboboxValueChange}
                    automatic-output-variables={automaticOutputVariables}>
                </c-fsc_flow-combobox>
            </div>

            <lightning-radio-group
                name="select_rowActionDisplay"
                label={inputValues.rowActionDisplay.label}
                options={rowActionDisplayOptions}
                value={inputValues.rowActionDisplay.value}
                type="radio"
                onchange={handleValueChange}
            ></lightning-radio-group>

            <!-- v4.3.5 now used for Action Label -->
            <c-fsc_flow-combobox 
                name="select_removeLabel" 
                label={rowActionInputLabel}
                value={inputValues.removeLabel.value} 
                value-type={inputValues.removeLabel.valueDataType}
                field-level-help={rowActionInputLabelHelp}
                builder-context-filter-type="String"
                builder-context-filter-collection-boolean={inputValues.removeLabel.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- v4.3.5 now used for Action Icon -->
            <c-fsc_flow-combobox 
                name="select_removeIcon" 
                label={rowActionInputIcon}
                value={inputValues.removeIcon.value} 
                value-type={inputValues.removeIcon.valueDataType}
                field-level-help={rowActionInputIconHelp}
                disabled={disallowHeaderChange}
                builder-context-filter-collection-boolean={inputValues.removeIcon.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>
            <c-fsc_pick-icon
                mode="combobox"
                oniconselection={handleActionIcon}>
            </c-fsc_pick-icon>

            <!-- v4.3.5 now used for Action Icon Color -->
            <div class={showRowActionButtonIconColor}>
                <lightning-radio-group
                    name="select_removeColor"
                    label={inputValues.removeColor.label}
                    options={actionButtonIconColorOptions}
                    value={inputValues.removeColor.value}
                    type="button"
                    onchange={handleValueChange}
                ></lightning-radio-group>
            </div>

            <div class={showButtonOptions}>
                <div class={showRowActionButtonIconPosition}>
                    <lightning-radio-group
                        name="select_rowActionButtonIconPosition"
                        label={inputValues.rowActionButtonIconPosition.label}
                        options={actionLeftOrRightOptions}
                        value={inputValues.rowActionButtonIconPosition.value}
                        type="button"
                        onchange={handleValueChange}
                    ></lightning-radio-group>
                </div>

                <lightning-combobox
                    name="select_rowActionButtonVariant"
                    label={inputValues.rowActionButtonVariant.label}
                    options={buttonVariantOptions}
                    value={inputValues.rowActionButtonVariant.value}
                    onchange={handleValueChange}
                ></lightning-combobox>
            </div>

            <lightning-radio-group
                name="select_removeRowLeftOrRight"
                label={inputValues.removeRowLeftOrRight.label}
                options={actionLeftOrRightOptions}
                value={inputValues.removeRowLeftOrRight.value}
                type="button"
                onchange={handleValueChange}
            ></lightning-radio-group>

        </div>
    </div>

    <!-- =============== Advanced Attributes Banner =============== -->
    <c-fsc_flow-banner
        banner-color={colorAdvancedOverride}
        banner-label={sectionEntries.advancedAttributes.label}
        banner-info={sectionEntries.advancedAttributes.info}
        modal-header-color={modalHeaderColorAdvancedOverride}
    ></c-fsc_flow-banner>

    <!-- Apex Defined Object? -->
    <c-fsc_flow-checkbox
        disabled={isSerializedSelected}
        name="select_isUserDefinedObject"
        label={inputValues.isUserDefinedObject.label}
        checked={inputValues.cb_isUserDefinedObject.value}
        field-level-help={inputValues.isUserDefinedObject.helpText}
        oncheckboxchanged={handleCheckboxChange}
    ></c-fsc_flow-checkbox>

    <div class="slds-m-top_xx-small">
        <c-fsc_flow-checkbox
            disabled={isApexSelected}
            name="select_isSerializedRecordData"
            label={inputValues.isSerializedRecordData.label}
            field-level-help={inputValues.isSerializedRecordData.helpText}
            checked={inputValues.cb_isSerializedRecordData.value}
            oncheckboxchanged={handleCheckboxChange}
        ></c-fsc_flow-checkbox>
    </div>
    <div class={inputValues.isSerializedRecordData.class}>
        <c-fsc_display-error 
                is-error={inputValues.isSerializedRecordData.isError}
                error-message={inputValues.isSerializedRecordData.errorMessage}
        ></c-fsc_display-error>
    </div>

    <div if:true={isSerializedRecordDataInput}>
        <c-fsc_flow-combobox 
            name="select_serializedRecordData" 
            label={inputValues.serializedRecordData.label}
            value={inputValues.serializedRecordData.value} 
            value-type={inputValues.serializedRecordData.valueDataType}
            field-level-help={inputValues.serializedRecordData.helpText}
            builder-context-filter-type="String"
            builder-context-filter-collection-boolean={inputValues.serializedRecordData.isCollection}
            builder-context={_builderContext} 
            onvaluechanged={handleFlowComboboxValueChange}
            automatic-output-variables={automaticOutputVariables}>
        </c-fsc_flow-combobox>
        <c-fsc_flow-combobox 
            name="select_objectName" 
            label={inputValues.objectName.label}
            value={inputValues.objectName.value} 
            value-type={inputValues.objectName.valueDataType}
            field-level-help={inputValues.objectName.helpText}
            builder-context-filter-type="String"
            builder-context-filter-collection-boolean={inputValues.objectName.isCollection}
            builder-context={_builderContext} 
            onvaluechanged={handleFlowComboboxValueChange}
            automatic-output-variables={automaticOutputVariables}>
        </c-fsc_flow-combobox>
    </div>

    <div if:true={isShowColumnAttributesToggle}>
        <!-- Column Attributes Toggle -->
        <lightning-input type="toggle" class="slds-m-vertical_small"
            name="toggle_showColumnAttributes"
            label="Show/Edit Column Attributes"
            message-toggle-active="" 
            message-toggle-inactive=""
            onchange={handleShowColumnAttributesToggle}
        ></lightning-input>
    </div>

    <!-- =============== Column Attributes  =============== -->
    <div class={showHideColumnAttributes}>
        <div class="slds-box slds-box_x-small slds-m-top_small">

            <!-- Column Fields -->
            <c-fsc_flow-combobox 
                name="select_columnFields" 
                label={inputValues.columnFields.label}
                value={inputValues.columnFields.value} 
                value-type={inputValues.columnFields.valueDataType}
                field-level-help={inputValues.columnFields.helpText}
                builder-context-filter-collection-boolean={inputValues.columnFields.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Column Alignments -->
            <c-fsc_flow-combobox 
                name="select_columnAlignments" 
                label={inputValues.columnAlignments.label}
                value={inputValues.columnAlignments.value} 
                value-type={inputValues.columnAlignments.valueDataType}
                field-level-help={inputValues.columnAlignments.helpText}
                builder-context-filter-collection-boolean={inputValues.columnAlignments.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Column Edits -->
            <c-fsc_flow-combobox 
                name="select_columnEdits" 
                label={inputValues.columnEdits.label}
                value={inputValues.columnEdits.value} 
                value-type={inputValues.columnEdits.valueDataType}
                field-level-help={inputValues.columnEdits.helpText}
                builder-context-filter-collection-boolean={inputValues.columnEdits.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Column Filters -->
            <c-fsc_flow-combobox 
                name="select_columnFilters" 
                label={inputValues.columnFilters.label}
                value={inputValues.columnFilters.value} 
                value-type={inputValues.columnFilters.valueDataType}
                field-level-help={inputValues.columnFilters.helpText}
                builder-context-filter-collection-boolean={inputValues.columnFilters.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Column Icons -->
            <c-fsc_flow-combobox 
                name="select_columnIcons" 
                label={inputValues.columnIcons.label}
                value={inputValues.columnIcons.value} 
                value-type={inputValues.columnIcons.valueDataType}
                field-level-help={inputValues.columnIcons.helpText}
                builder-context-filter-collection-boolean={inputValues.columnIcons.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Column Labels -->
            <c-fsc_flow-combobox 
                name="select_columnLabels" 
                label={inputValues.columnLabels.label}
                value={inputValues.columnLabels.value} 
                value-type={inputValues.columnLabels.valueDataType}
                field-level-help={inputValues.columnLabels.helpText}
                builder-context-filter-collection-boolean={inputValues.columnLabels.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Column Scales -->
            <c-fsc_flow-combobox
                if:false={isSObjectInput}
                name="select_columnScales" 
                label={inputValues.columnScales.label}
                value={inputValues.columnScales.value} 
                value-type={inputValues.columnScales.valueDataType}
                field-level-help={inputValues.columnScales.helpText}
                builder-context-filter-collection-boolean={inputValues.columnScales.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Column Types -->
            <c-fsc_flow-combobox 
                if:false={isSObjectInput}
                name="select_columnTypes" 
                label={inputValues.columnTypes.label}
                value={inputValues.columnTypes.value} 
                value-type={inputValues.columnTypes.valueDataType}
                field-level-help={inputValues.columnTypes.helpText}
                builder-context-filter-collection-boolean={inputValues.columnTypes.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>
        
            <!-- Column Widths -->
            <c-fsc_flow-combobox 
                name="select_columnWidths" 
                label={inputValues.columnWidths.label}
                value={inputValues.columnWidths.value} 
                value-type={inputValues.columnWidths.valueDataType}
                field-level-help={inputValues.columnWidths.helpText}
                builder-context-filter-collection-boolean={inputValues.columnWidths.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>
            
            <!-- Column Flexes -->
            <c-fsc_flow-combobox 
                name="select_columnFlexes" 
                label={inputValues.columnFlexes.label}
                value={inputValues.columnFlexes.value} 
                value-type={inputValues.columnFlexes.valueDataType}
                field-level-help={inputValues.columnFlexes.helpText}
                builder-context-filter-collection-boolean={inputValues.columnFlexes.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Column Wraps -->
            <c-fsc_flow-combobox 
                name="select_columnWraps" 
                label={inputValues.columnWraps.label}
                value={inputValues.columnWraps.value} 
                value-type={inputValues.columnWraps.valueDataType}
                field-level-help={inputValues.columnWraps.helpText}
                builder-context-filter-collection-boolean={inputValues.columnWraps.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Special Column CellAttribs -->
            <c-fsc_flow-combobox 
                name="select_columnCellAttribs" 
                label={inputValues.columnCellAttribs.label}
                value={inputValues.columnCellAttribs.value} 
                value-type={inputValues.columnCellAttribs.valueDataType}
                field-level-help={inputValues.columnCellAttribs.helpText}
                builder-context-filter-collection-boolean={inputValues.columnCellAttribs.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Special Column TypeAttribs -->
            <c-fsc_flow-combobox 
                name="select_columnTypeAttribs" 
                label={inputValues.columnTypeAttribs.label}
                value={inputValues.columnTypeAttribs.value} 
                value-type={inputValues.columnTypeAttribs.valueDataType}
                field-level-help={inputValues.columnTypeAttribs.helpText}
                builder-context-filter-collection-boolean={inputValues.columnTypeAttribs.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

            <!-- Special Column OtherAttribs -->
            <c-fsc_flow-combobox 
                name="select_columnOtherAttribs" 
                label={inputValues.columnOtherAttribs.label}
                value={inputValues.columnOtherAttribs.value} 
                value-type={inputValues.columnOtherAttribs.valueDataType}
                field-level-help={inputValues.columnOtherAttribs.helpText}
                builder-context-filter-collection-boolean={inputValues.columnOtherAttribs.isCollection}
                builder-context={_builderContext} 
                onvaluechanged={handleFlowComboboxValueChange}
                automatic-output-variables={automaticOutputVariables}>
            </c-fsc_flow-combobox>

        </div>
    </div>

    <!-- Record Type Id -->
    <c-fsc_flow-combobox 
        name="select_recordTypeId" 
        label={inputValues.recordTypeId.label}
        value={inputValues.recordTypeId.value} 
        value-type={inputValues.recordTypeId.valueDataType}
        field-level-help={inputValues.recordTypeId.helpText}
        builder-context-filter-collection-boolean={inputValues.recordTypeId.isCollection}
        builder-context={_builderContext} 
        onvaluechanged={handleFlowComboboxValueChange}
        automatic-output-variables={automaticOutputVariables}>
    </c-fsc_flow-combobox>

    <!-- Add --None-- to Picklists -->
    <div class="slds-m-top_xx-small">
        <c-fsc_flow-checkbox
            name="select_allowNoneToBeChosen"
            label={inputValues.allowNoneToBeChosen.label} 
            field-level-help={inputValues.allowNoneToBeChosen.helpText}
            checked={inputValues.cb_allowNoneToBeChosen.value} 
            oncheckboxchanged={handleCheckboxChange}
        ></c-fsc_flow-checkbox> 
    </div>

    <!-- Suppress Currency Conversion -->
    <div class="slds-m-top_xx-small">
        <c-fsc_flow-checkbox
            name="select_suppressCurrencyConversion"
            label={inputValues.suppressCurrencyConversion.label} 
            field-level-help={inputValues.suppressCurrencyConversion.helpText}
            checked={inputValues.cb_suppressCurrencyConversion.value} 
            oncheckboxchanged={handleCheckboxChange}
        ></c-fsc_flow-checkbox> 
    </div>

    <!-- Case Insensitive Sort -->
    <div class="slds-m-top_xx-small">
        <c-fsc_flow-checkbox
            name="select_isCaseInsensitiveSort"
            label={inputValues.isCaseInsensitiveSort.label} 
            field-level-help={inputValues.isCaseInsensitiveSort.helpText}
            checked={inputValues.cb_isCaseInsensitiveSort.value} 
            oncheckboxchanged={handleCheckboxChange}
        ></c-fsc_flow-checkbox> 
    </div>
    
    <!-- Allow Container Overflow -->
    <div class="slds-m-top_xx-small">
        <c-fsc_flow-checkbox
            name="select_allowOverflow"
            label={inputValues.allowOverflow.label} 
            field-level-help={inputValues.allowOverflow.helpText}
            checked={inputValues.cb_allowOverflow.value} 
            oncheckboxchanged={handleCheckboxChange}
        ></c-fsc_flow-checkbox> 
    </div>
    
    <!-- Set Table Height -->
    <c-fsc_flow-combobox 
        disabled={isAllowOverflow}
        name="select_tableHeight" 
        label={inputValues.tableHeight.label}
        value={inputValues.tableHeight.value} 
        value-type={inputValues.tableHeight.valueDataType}
        field-level-help={inputValues.tableHeight.helpText}
        builder-context-filter-collection-boolean={inputValues.tableHeight.isCollection}
        builder-context={_builderContext} 
        onvaluechanged={handleFlowComboboxValueChange}
        automatic-output-variables={automaticOutputVariables}>
    </c-fsc_flow-combobox>

    <!-- Key Field -->
    <c-fsc_flow-combobox 
        name="select_keyField" 
        label={inputValues.keyField.label}
        value={inputValues.keyField.value} 
        value-type={inputValues.keyField.valueDataType}
        field-level-help={inputValues.keyField.helpText}
        builder-context-filter-collection-boolean={inputValues.keyField.isCollection}
        builder-context={_builderContext} 
        onvaluechanged={handleFlowComboboxValueChange}
        automatic-output-variables={automaticOutputVariables}>
    </c-fsc_flow-combobox>

    <div class="slds-text-title slds-text-align_right slds-m-top_xx-small slds-p-bottom_small slds-text-color_success">
        Datatable Version #: {versionNumber}
    </div>

</template>