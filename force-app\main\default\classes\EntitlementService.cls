/********************************************************************************************

   @ Func Area  :  Apex development

   @ Author :  <PERSON>

   @ Date :  26 November 2024

   @ Description  :   The Service Layer Pattern is a very powerful tool that can help a team create a strong bias towards reusable code. If this concept is new to your team, the Service Layer Badge on Trailhead is a great place to start.
                        The underlying pattern is old and has a long background in many programming languages (including Java).
                        The recommended approach is to create a single service class for each Salesforce object for which you have code (“EntitlementService.cls”, “CampaignMemberService.cls”, etc). Within that class you create static methods which implement different bits of business logic. Your main design constraint is
                        to try and make the service methods themselves as reusable as possible. Keep them generic!

   @ Developer Notes  : EntitlementService entitlementService = new EntitlementService();

                        1) String entitlementName = entitlementService.getEntitlementName(entitlementId);
                        2) String entitlementName = new EntitlementService().getEntitlementName(entitlementId);


   @ Github Repo  : https://github.com/mitchspano/apex-trigger-actions-framework
                    https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master
                    https://bluecanvas.io/blog/how-to-structure-and-manage-your-apex-codebase-for-scale-and-agility

   @ Last Modified By  : Daniel Field
   @ Last Modified On  : 26 November 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public inherited sharing class EntitlementService {
  @TestVisible
  private static DmlHelper dmlHelper = new DmlHelper();
  @TestVisible
  private static EntitlementSelector entitlementSelector = new EntitlementSelector();

  public String getEntitlementName(Id entitlementId) {
    List<Entitlement> entitlements = entitlementSelector.selectEntitlementsById(
      new Set<Id>{ entitlementId }
    );
    return entitlements.isEmpty() ? null : entitlements[0].Name;
  }

  public void updateEntitlements(
    List<Entitlement> entitlementsToUpdate,
    String Source
  ) {
    dmlHelper.updateObjects(entitlementsToUpdate, Source);
  }

  public void createEntitlements(
    List<Entitlement> entitlementsToInsert,
    String Source
  ) {
    dmlHelper.insertObjects(entitlementsToInsert, Source);
  }

  public void deleteEntitlements(
    List<Entitlement> entitlementsToDelete,
    String Source
  ) {
    dmlHelper.deleteObjects(entitlementsToDelete, Source);
  }
}