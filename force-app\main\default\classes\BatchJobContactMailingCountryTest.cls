@isTest
private class BatchJobContactMailingCountryTest {
  @TestSetup
  static void setupTestData() {
    // Create test accounts with different country configurations
    List<Account> testAccounts = new List<Account>{
      new Account(
        Name = 'Test Account 1',
        BillingCountry = 'United States',
        ShippingCountry = 'Canada'
      ),
      new Account(
        Name = 'Test Account 2',
        BillingCountry = null,
        ShippingCountry = 'Mexico'
      ),
      new Account(
        Name = 'Test Account 3',
        BillingCountry = null,
        ShippingCountry = null
      )
    };
    insert testAccounts;

    // Create test contacts with different scenarios
    List<Contact> testContacts = new List<Contact>{
      // Contact that should update to BillingCountry
      new Contact(
        FirstName = 'Test1',
        LastName = 'Contact1',
        Email = '<EMAIL>',
        Phone = '**********',
        MobilePhone = '**********',
        MailingCountry = 'United Kingdom',
        AccountId = testAccounts[0].Id
      ),
      // Contact that should update to ShippingCountry
      new Contact(
        FirstName = 'Test2',
        LastName = 'Contact2',
        Email = '<EMAIL>',
        Phone = '**********',
        MobilePhone = '**********',
        MailingCountry = 'United Kingdom',
        AccountId = testAccounts[1].Id
      ),
      // Contact that should retain original MailingCountry
      new Contact(
        FirstName = 'Test3',
        LastName = 'Contact3',
        Email = '<EMAIL>',
        Phone = '**********',
        MobilePhone = '**********',
        MailingCountry = 'United Kingdom',
        AccountId = testAccounts[2].Id
      )
    };
    insert testContacts;
  }

  @isTest
  static void testBatchExecution() {
    // Start the batch job
    Test.startTest();
    BatchJobContactMailingCountry batchJob = new BatchJobContactMailingCountry();
    Database.executeBatch(batchJob);
    Test.stopTest();
  }

  @isTest
  static void testSchedulableExecution() {
    // Test the schedulable interface
    Test.startTest();
    String jobId = System.schedule(
      'Test Contact Update Job',
      '0 0 23 * * ?',
      new BatchJobContactMailingCountry()
    );
    Test.stopTest();

    // Verify the job was scheduled
    List<CronTrigger> cronTriggers = [
      SELECT Id, CronExpression
      FROM CronTrigger
      WHERE Id = :jobId
    ];
    System.assertEquals(
      1,
      cronTriggers.size(),
      'Job should have been scheduled'
    );
  }

  @isTest
  static void testEmptyScope() {
    // Delete all contacts to test empty scope
    delete [SELECT Id FROM Contact];

    // Start the batch job
    Test.startTest();
    BatchJobContactMailingCountry batchJob = new BatchJobContactMailingCountry();
    Database.executeBatch(batchJob);
    Test.stopTest();

    // Verify no errors occurred
    List<AsyncApexJob> completedJobs = [
      SELECT Id, Status, NumberOfErrors
      FROM AsyncApexJob
      WHERE
        ApexClass.Name = 'BatchJobContactMailingCountry'
        AND JobType = 'BatchApex'
    ];

    System.assertEquals(
      1,
      completedJobs.size(),
      'Batch job should have completed'
    );
    System.assertEquals(
      'Completed',
      completedJobs[0].Status,
      'Batch job should have completed successfully'
    );
    System.assertEquals(
      0,
      completedJobs[0].NumberOfErrors,
      'Batch job should have no errors'
    );
  }
}