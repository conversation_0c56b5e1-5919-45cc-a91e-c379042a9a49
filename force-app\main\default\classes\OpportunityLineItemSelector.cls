/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  27 January 2025

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> opportunityLineItemScheduleIds = new Set<Id>();
                       opportunityLineItemScheduleIds.add('001O300000FGc3tIAD');
                       opportunityLineItemScheduleIds.add('001O300000FGc3tIZZ');
                       OpportunityLineItemScheduleSelector opportunityLineItemScheduleSelector = new OpportunityLineItemScheduleSelector();
                       List<OpportunityLineItemSchedule> opportunityLineItemSchedules = opportunityLineItemScheduleSelector.selectOpportunityLineItemSchedulesByIds(opportunityLineItemScheduleIds);
                       List<OpportunityLineItemSchedule> opportunityLineItemSchedules = new OpportunityLineItemScheduleSelector().selectOpportunityLineItemSchedulesByIds(opportunityLineItemScheduleIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 27 January 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/
//TODO: Add a method to select all OpportunityLineItemSchedules
public inherited sharing class OpportunityLineItemSelector {
  private String query;
  private String fromObject = ' FROM OpportunityLineItem ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public OpportunityLineItemSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your account queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Name, CalendarYear__c, CreatedById, CYUnweightedRevenue__c, CYWeightedRevenue__c, ServiceDate, Discount, DisplaySchedulingBanner__c, IsVRBypassed__c, LastModifiedById, Description, ListPrice, NextCalendarYear__c, NextCYUnweightedRevenue__c, NextCYWeightedRevenue__c, OpportunityId, OpportunityProbability__c, ValidationBypassDateTime__c, Product2Id, Product2.Name, Product2.ProductCode, Product2.Description, Product2.Family, Product2.UOM__c, Product2.PackSize__c, ProductCode, Quantity, UnitPrice, Subtotal, TotalPrice, TotalPrice__c, SalesPricePerPack__c, HasQuantitySchedule';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('OpportunityLineItemSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your OpportunityLineItems by a set of ids
  public List<OpportunityLineItem> selectOpportunityLineItemsByIds(
    Set<Id> opportunityLineItemIds
  ) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE Id IN :opportunityLineItemIds' +
      this.queryLimit;
    //system.debug('selectOpportunityLineItemsByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your OpportunityLineItems by a set of ids
  public List<OpportunityLineItem> selectOpportunityLineItemsByOppId(
    Id opportunityId
  ) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE OpportunityId = :opportunityId' +
      this.queryLimit;
    //system.debug('selectOpportunityLineItemsByOppId() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your OpportunityLineItems by a set of ids
  public List<OpportunityLineItem> selectOpportunityLineItemsByOppIds(
    Set<Id> opportunityIds
  ) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE OpportunityId IN :opportunityIds   ' +
      this.queryLimit;
    //system.debug('selectOpportunityLineItemsByOppIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }
}