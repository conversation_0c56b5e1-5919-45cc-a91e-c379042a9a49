/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  27 January 2025

   @ Description	:   The Service Layer Pattern is a very powerful tool that can help a team create a strong bias towards reusable code. If this concept is new to your team, the Service Layer Badge on Trailhead is a great place to start.
                        The underlying pattern is old and has a long background in many programming languages (including Java).
                        The recommended approach is to create a single service class for each Salesforce object for which you have code (“AccountService.cls”, “CampaignMemberService.cls”, etc). Within that class you create static methods which implement different bits of business logic. Your main design constraint is
                        to try and make the service methods themselves as reusable as possible. Keep them generic!

   @ Developer Notes  :      opportunityLineItemService = new OpportunityLineItemService();

                        1) opportunityLineItemService.updateOpportunityLineItems(opportunityLineItemsToUpdate, 'OpportunityLineItemService');
                        2) opportunityLineItemService.createOpportunityLineItems(opportunityLineItemsToInsert, 'OpportunityLineItemService');


   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                    https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master
                    https://bluecanvas.io/blog/how-to-structure-and-manage-your-apex-codebase-for-scale-and-agility

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 27 January 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/
public inherited sharing class OpportunityLineItemService {
  @TestVisible
  private static DmlHelper dmlHelper = new DmlHelper();
  @TestVisible
  private static OpportunityLineItemSelector opportunityLineItemSelector = new OpportunityLineItemSelector();

  public void updateOpportunityLineItems(
    List<OpportunityLineItem> opportunityLineItemsToUpdate,
    String Source
  ) {
    for (
      OpportunityLineItem opportunityLineItemToUpdate : opportunityLineItemsToUpdate
    ) {
      opportunityLineItemToUpdate.ValidationBypassDateTime__c = System.now();
    }
    dmlHelper.updateObjects(opportunityLineItemsToUpdate, Source);
  }

  public void createOpportunityLineItems(
    List<OpportunityLineItem> opportunityLineItemsToInsert,
    String Source
  ) {
    for (
      OpportunityLineItem opportunityLineItemToInsert : opportunityLineItemsToInsert
    ) {
      opportunityLineItemToInsert.ValidationBypassDateTime__c = System.now();
    }
    dmlHelper.insertObjects(opportunityLineItemsToInsert, Source);
  }
}