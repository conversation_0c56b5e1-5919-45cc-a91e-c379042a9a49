<template>
    <div style={inputStyle} onmouseleave={handleInputFocus}>
        <div>
            <div class={formElementClass}>
                <div class="slds-form-element__control">
                    <div class="slds-combobox_container">
                        <div class={dropdownClass} data-id="resultBox" aria-expanded="false" aria-haspopup="listbox"
                             role="combobox">
                            <div class="slds-form-element__control slds-input-has-icon slds-input-has-icon slds-input-has-icon_right"
                                 role="none" style="height: 55px;">
                                <template if:false={displayPill}>
                                    <div onkeyup={handleSearchKeyUp}>
                                        <label class="slds-form-element__label custom-width-full" onclick={toggleMenu}>{label}
                                            <lightning-helptext if:true={fieldLevelHelp} class="slds-m-left_xxx-small" content={fieldLevelHelp}></lightning-helptext>
                                        </label>
                                        <lightning-input
                                                class="value-input"
                                                name="searchText"
                                                type="search"
                                                required={required}
                                                data-input-name={name}
                                                data-id="userinput"
                                                variant="label-hidden"
                                                value={_value}
                                                disabled={disabled}
                                                onchange={handleSearchField}
                                                onkeydown={handleKeyDown}
                                                onclick={handleOpenOptions}
                                                autocomplete={autocomplete}
                                                placeholder={placeholderText}
                                        ></lightning-input>
                                    </div>
                                </template>
                                <template if:true={displayPill}>
                                    <label class="slds-form-element__label slds-no-flex custom-width-full" onclick={toggleMenu}>{label}
                                        <lightning-helptext if:true={fieldLevelHelp} class="slds-m-left_xxx-small" content={fieldLevelHelp}></lightning-helptext>
                                    </label>
                                    <div data-input-name={name} class="slds-pill_container" onclick={handleOpenOptions}>
                                        <lightning-pill
                                                class="slds-pill_bare custom-width-full"
                                                label={_value}
                                                onremove={resetData}
                                                has-error={hasError}
                                        >
                                        </lightning-pill>
                                    </div>
                                </template>
                            </div>

                            <div id="listbox-id-1"
                                 class="slds-dropdown slds-dropdown_length-with-icon-7 slds-dropdown_fluid"
                                 role="listbox">
                                <ul class="slds-listbox slds-listbox_vertical" role="presentation">
                                    <template if:false={_options.length}>
                                        <div class="slds-p-around_small">
                                            {labels.noDataAvailable}
                                        </div>
                                    </template>
                                    <template for:each={_options} for:item="curOption">
                                        <li role="presentation" class="slds-listbox__item" key={curOption.type}>
                                            <div class="slds-p-around_xx-small custom-text-transform_uppercase">
                                                <lightning-icon 
                                                                icon-name="utility:chevronleft"
                                                                class="slds-icon slds-icon_small slds-icon-text-default"
                                                                size="x-small"
                                                                onclick={resetData}
                                                ></lightning-icon>
                                                {curOption.type}
                                            </div>
                                            <template for:each={curOption.options} for:item="curOptionElement">
                                                <div key={curOptionElement.key}
                                                     class="slds-media slds-listbox__option slds-listbox__option_entity slds-listbox__option_has-meta"
                                                     role="option">
                                                <span class="slds-media__figure">
                                                    <span class="slds-icon_container">
                                                        <lightning-icon icon-name={curOptionElement.optionIcon}
                                                                        class="slds-icon slds-icon_small slds-icon-text-default"
                                                                        size="x-small"></lightning-icon>
                                                    </span>
                                                </span>
                                                    <span data-value={curOptionElement.value}
                                                          data-flow-type={curOptionElement.flowType}
                                                          data-object-type={curOptionElement.objectType}
                                                          onclick={handleSetSelectedRecord}
                                                          class="slds-media__body">
                                                    <span class="slds-listbox__option-text slds-listbox__option-text_entity">{curOptionElement.label}</span>
                                                    <span class="slds-listbox__option-meta slds-listbox__option-meta_entity">{curOptionElement.displayType}</span>
                                                </span>
                                                    <lightning-icon if:true={curOptionElement.isObject}
                                                                    data-option-value={curOptionElement.value}
                                                                    data-object-type={curOptionElement.objectType}
                                                                    icon-name="utility:chevronright"
                                                                    class="slds-float--right"
                                                                    size="x-small"
                                                                    onclick={handleOpenObject}
                                                    ></lightning-icon>

                                                    <lightning-icon if:true={curOptionElement.storeOutputAutomatically}
                                                                    data-option-value={curOptionElement.value}
                                                                    data-object-type={curOptionElement.objectType}
                                                                    icon-name="utility:chevronright"
                                                                    class="slds-float--right"
                                                                    size="x-small"
                                                                    onclick={handleOpenScreenComponent}
                                                    ></lightning-icon>

                                                    <lightning-icon if:true={curOptionElement.globalVariable}
                                                                    data-option-value={curOptionElement.value}
                                                                    data-object-type={curOptionElement.objectType}
                                                                    icon-name="utility:chevronright"
                                                                    class="slds-float--right"
                                                                    size="x-small"
                                                                    onclick={handleOpenGlobalVariable}
                                                    ></lightning-icon>
                                                </div>
                                            </template>
                                        </li>
                                    </template>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <template if:true={hasError}>
                    <div class="slds-form-element__help slds-m-top_large">
                        {labels.invalidReferenceError}
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>