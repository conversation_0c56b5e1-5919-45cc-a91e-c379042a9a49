<template>
  <!-- <PERSON><PERSON> (only shown when used on Record Page, not as Record Action) -->
  <template if:true={showTriggerButton}>
    <div class="trigger-container">
      <lightning-button
        label={followersCount}
        icon-name="utility:following"
        variant="brand-outline"
        onclick={openModal}
        class="follow-trigger-btn"
      >
      </lightning-button>
      <span class="followers-label">Case Followers</span>
    </div>
  </template>

  <!-- Modal -->
  <template if:true={showModal}>
    <section
      role="dialog"
      tabindex="-1"
      class="slds-modal slds-fade-in-open slds-modal_large"
    >
      <div class="slds-modal__container">
        <!-- Modal Header -->
        <header class="slds-modal__header slds-theme_default">
          <button
            class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
            title="Close"
            onclick={closeModal}
          >
            <lightning-icon
              icon-name="utility:close"
              alternative-text="close"
              size="small"
            ></lightning-icon>
          </button>
          <h2 class="slds-modal__title slds-hyphenate">
            Manage Case Followers
          </h2>
          <p class="slds-m-top_x-small">
            Add or remove followers for this case
          </p>
        </header>

        <!-- Modal Body -->
        <div class="slds-modal__content slds-p-around_medium">
          <!-- Tab Navigation -->
          <lightning-tabset
            active-tab-value={activeTab}
            onactive={handleTabChange}
          >
            <lightning-tab label="Add Followers" value="follow">
              <!-- Search Bar -->
              <div class="slds-m-bottom_medium">
                <lightning-input
                  type="search"
                  label="Search Users"
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onchange={handleSearchChange}
                  class="search-input"
                >
                </lightning-input>
              </div>

              <!-- User List -->
              <div class="user-grid">
                <template for:each={filteredUsers} for:item="user">
                  <div
                    key={user.id}
                    class="user-card"
                    data-user-id={user.id}
                    onclick={handleUserSelection}
                  >
                    <div class="user-avatar">
                      <img src={user.photoUrl} alt={user.name} />
                    </div>
                    <div class="user-info">
                      <div class="user-name">{user.name}</div>
                      <div class="user-email">{user.email}</div>
                    </div>
                    <div class="selection-indicator">
                      <lightning-icon
                        icon-name="utility:check"
                        size="x-small"
                      ></lightning-icon>
                    </div>
                  </div>
                </template>
              </div>
            </lightning-tab>

            <lightning-tab label="Current Followers" value="manage">
              <div class="current-followers">
                <template if:true={currentFollowers.length}>
                  <div class="user-grid">
                    <template for:each={currentFollowers} for:item="follower">
                      <div
                        key={follower.id}
                        class="user-card follower-card"
                        data-user-id={follower.id}
                        onclick={handleUserSelection}
                      >
                        <div class="user-avatar">
                          <img src={follower.photoUrl} alt={follower.name} />
                        </div>
                        <div class="user-info">
                          <div class="user-name">{follower.name}</div>
                          <div class="user-email">{follower.email}</div>
                        </div>
                        <div class="selection-indicator">
                          <lightning-icon
                            icon-name="utility:close"
                            size="x-small"
                          ></lightning-icon>
                        </div>
                      </div>
                    </template>
                  </div>
                </template>
                <template if:false={currentFollowers.length}>
                  <div class="empty-state">
                    <lightning-icon
                      icon-name="utility:following"
                      size="large"
                    ></lightning-icon>
                    <h3>No followers yet</h3>
                    <p>Add followers using the "Add Followers" tab</p>
                  </div>
                </template>
              </div>
            </lightning-tab>
          </lightning-tabset>
        </div>

        <!-- Modal Footer -->
        <footer class="slds-modal__footer">
          <lightning-button
            label="Cancel"
            onclick={closeModal}
            class="slds-m-right_small"
          >
          </lightning-button>

          <template if:true={isFollowTab}>
            <lightning-button
              label="Add Selected Followers"
              variant="brand"
              onclick={handleFollow}
              disabled={isLoading}
              class="action-button"
            >
            </lightning-button>
          </template>

          <template if:true={isManageTab}>
            <lightning-button
              label="Remove Selected"
              variant="destructive"
              onclick={handleUnfollow}
              disabled={isLoading}
              class="action-button"
            >
            </lightning-button>
          </template>
        </footer>

        <!-- Loading Spinner -->
        <template if:true={isLoading}>
          <div class="slds-spinner_container">
            <div role="status" class="slds-spinner slds-spinner_medium">
              <span class="slds-assistive-text">Loading</span>
              <div class="slds-spinner__dot-a"></div>
              <div class="slds-spinner__dot-b"></div>
            </div>
          </div>
        </template>
      </div>
    </section>
    <div class="slds-backdrop slds-backdrop_open"></div>
  </template>
</template>
