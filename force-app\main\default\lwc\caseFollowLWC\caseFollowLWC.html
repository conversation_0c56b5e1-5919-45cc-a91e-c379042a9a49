<template>
  <!-- Initial inline display removed - component now shows modal directly -->

  <!-- Floating Popup - Only render when showModal is true -->
  <template if:true={showModal}>
    <div
      class="floating-popup-container case-follow-popup"
      data-component="caseFollowLWC"
    >
      <div class="floating-popup-card">
        <!-- Popup Header -->
        <header class="popup-header">
          <div class="header-content">
            <h2 class="popup-title">Manage Case Followers</h2>
            <p class="popup-subtitle">Add or remove followers for this case</p>
          </div>
          <button
            class="close-button"
            title="Close"
            aria-label="Close modal"
            onclick={closeModal}
          >
            <lightning-icon
              icon-name="utility:close"
              alternative-text="close"
              size="small"
            ></lightning-icon>
          </button>
        </header>

        <!-- Popup Body -->
        <div class="popup-body">
          <!-- Tab Navigation -->
          <lightning-tabset
            active-tab-value={activeTab}
            onactivetabchange={handleTabChange}
          >
            <lightning-tab label="Add Followers" value="follow">
              <!-- Search Bar -->
              <div class="slds-m-bottom_medium">
                <lightning-input
                  type="search"
                  label="Search Users"
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onchange={handleSearchChange}
                  class="search-input"
                >
                </lightning-input>
              </div>

              <!-- User List -->
              <div class="user-grid">
                <template for:each={filteredUsers} for:item="user">
                  <div
                    key={user.id}
                    class="user-card"
                    data-user-id={user.id}
                    onclick={handleUserSelection}
                  >
                    <div class="user-avatar">
                      <img src={user.photoUrl} alt={user.name} />
                    </div>
                    <div class="user-info">
                      <div class="user-name">{user.name}</div>
                      <div class="user-email">{user.email}</div>
                    </div>
                    <div class="selection-indicator">
                      <lightning-icon
                        icon-name="utility:check"
                        size="x-small"
                      ></lightning-icon>
                    </div>
                  </div>
                </template>
              </div>
            </lightning-tab>

            <lightning-tab label="Current Followers" value="manage">
              <div class="current-followers">
                <template if:true={currentFollowers.length}>
                  <div class="user-grid">
                    <template for:each={currentFollowers} for:item="follower">
                      <div
                        key={follower.id}
                        class="user-card follower-card"
                        data-user-id={follower.id}
                        onclick={handleUserSelection}
                      >
                        <div class="user-avatar">
                          <img src={follower.photoUrl} alt={follower.name} />
                        </div>
                        <div class="user-info">
                          <div class="user-name">{follower.name}</div>
                          <div class="user-email">{follower.email}</div>
                        </div>
                        <div class="selection-indicator">
                          <lightning-icon
                            icon-name="utility:close"
                            size="x-small"
                          ></lightning-icon>
                        </div>
                      </div>
                    </template>
                  </div>
                </template>
                <template if:false={currentFollowers.length}>
                  <div class="empty-state">
                    <lightning-icon
                      icon-name="utility:following"
                      size="large"
                    ></lightning-icon>
                    <h3>No followers yet</h3>
                    <p>Add followers using the "Add Followers" tab</p>
                  </div>
                </template>
              </div>
            </lightning-tab>
          </lightning-tabset>
        </div>

        <!-- Popup Footer -->
        <footer class="popup-footer">
          <lightning-button
            label="Cancel"
            onclick={closeModal}
            class="slds-m-right_small"
          >
          </lightning-button>

          <template if:true={isFollowTab}>
            <lightning-button
              label="Add Selected Followers"
              variant="brand"
              onclick={handleFollow}
              disabled={isLoading}
              class="action-button"
            >
            </lightning-button>
          </template>

          <template if:true={isManageTab}>
            <lightning-button
              label="Remove Selected"
              variant="destructive"
              onclick={handleUnfollow}
              disabled={isLoading}
              class="action-button"
            >
            </lightning-button>
          </template>
        </footer>

        <!-- Loading Spinner -->
        <template if:true={isLoading}>
          <div class="slds-spinner_container">
            <div role="status" class="slds-spinner slds-spinner_medium">
              <span class="slds-assistive-text">Loading</span>
              <div class="slds-spinner__dot-a"></div>
              <div class="slds-spinner__dot-b"></div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </template>
</template>
