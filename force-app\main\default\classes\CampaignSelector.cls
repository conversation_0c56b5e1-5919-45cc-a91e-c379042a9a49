/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  31 May 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                        there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                        to achieve with a selector layer.

   @ Developer Notes   :    Set<Id> campaignIds = new Set<Id>();
                            campaignIds.add('001O300000FGc3tIAD');
                            campaignIds.add('001O300000FGc3tIZZ');
                            CampaignSelector campaignSelector = new CampaignSelector();
                            List<Campaign> campaigns = campaignSelector.selectCampaignsByIds(campaignIds);
                            List<Campaign> campaigns = new CampaignSelector().selectCampaignsByIds(campaignIds);

   @ Github Repo	:   https://github.com/mitchspano/apex-trigger-actions-framework
                        https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ckcroft
   @ Last Modified On  : 04 April 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class CampaignSelector {
  private String query;
  private String fromObject = ' FROM Campaign ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public CampaignSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your account queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Name, StartDate, EndDate, Status, Type, IsActive, Owner.Name';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('CampaignSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your campaigns by a set of campaign ids
  public List<Campaign> selectCampaignsByIds(Set<Id> campaignIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id IN :campaignIds' + this.queryLimit;
    //system.debug('selectCampaignsByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Would continue to build queries and setters for everything you theoretically need.
}