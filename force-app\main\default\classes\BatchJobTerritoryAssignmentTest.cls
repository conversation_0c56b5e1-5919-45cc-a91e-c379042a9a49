/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  08 August 2024

   @ Description	:   A test class containing test methods for apex automation

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   BatchJobTerritoryAssignment

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 08 August 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
private class BatchJobTerritoryAssignmentTest {
  @testSetup
  static void setup() {
    // Create test accounts
    List<Account> testAccounts = new List<Account>();
    for (Integer i = 0; i < 20; i++) {
      testAccounts.add(
        new Account(
          Name = 'Test Account ' + i,
          SalesChannel__c = 'Retail',
          SalesTeam__c = 'DTC',
          ShippingCountry = 'United States',
          ShippingCountryCode = 'US',
          ShippingState = 'California',
          ShippingStateCode = 'CA',
          ShippingPostalCode = '93216'
        )
      );
    }
    insert testAccounts;
  }

  @isTest
  static void testBatchJobExecution() {
    // Query to be used in the batch job
    String query = 'SELECT Id, SalesChannel__c, SalesTeam__c, ShippingCountry, ShippingCountryCode, ShippingState, ShippingStateCode, ShippingPostalCode, SalesTerritories__c, SalesTerritoryMatchingKey__c FROM Account';

    Test.startTest();
    BatchJobTerritoryAssignment batchJob = new BatchJobTerritoryAssignment(
      query
    );
    Id batchJobId = Database.executeBatch(batchJob, 200);
    Test.stopTest();
  }

  @isTest
  static void testBatchJobLauncherLWCController() {
    // Query to be used in the batch job
    Test.startTest();
    BatchJobLauncherLWCController.launchBatchJob();
    Test.stopTest();
  }
}