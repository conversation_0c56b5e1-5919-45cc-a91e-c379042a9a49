/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  08 July 2024

   @ Description	:   This is an common Email Manager class.  Methods will retrieve a Lightning Email Template and send email

   @ Developer Notes   :   https://nadinalisbon.com/salesforce-the-better-way-to-send-emails/


   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                      https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 08 July 2024
   @ Last Modified Reason  : Creation

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 08 October 2024
   @ Last Modified Reason  : Added support for lightning email templates

********************************************************************************************/

public class EmailManager {
  public void SendEmailWithTemplate(
    String templateName,
    List<string> recipientsNonUserList,
    List<User> recipientsUsersList,
    List<Contact> contacts,
    List<Lead> leadsToProcess,
    List<Case> casesToProcess
  ) {
    EmailTemplate et = [
      SELECT Id, Subject, Body, HtmlValue
      FROM EmailTemplate
      WHERE Name = :templateName
    ];
    //Fetch org wide email to be used to send email
    OrgWideEmailAddress[] owea = [
      SELECT Id
      FROM OrgWideEmailAddress
      WHERE Address = '<EMAIL>'
    ];
    List<Messaging.SingleEmailMessage> allmsg = new List<Messaging.SingleEmailMessage>();
    List<string> toAddresses = new List<string>();

    if (CollectionUtils.isNotEmpty(recipientsNonUserList)) {
      toAddresses.addAll(recipientsNonUserList);
    }

    if (CollectionUtils.isNotEmpty(leadsToProcess)) {
      for (Lead leadToProcess : leadsToProcess) {
        /*
        if (CollectionUtils.isNotEmpty(recipientsUsersList)) {
          for (user recipientsUser : recipientsUsersList) {
            System.debug('Sending email to: ' + recipientsUser.Email);
            Messaging.SingleEmailMessage email = Messaging.renderStoredEmailTemplate(
              et.Id,
              leadToProcess.Id,
              leadToProcess.Id
            );
            String subject = email.getSubject();
            String body = email.getHtmlBody();

            email.setTargetObjectId(recipientsUser.Id);
            email.setSubject(subject);
            email.setHtmlBody(body);
            email.saveAsActivity = false;
            email.setOrgWideEmailAddressId(owea.get(0).Id);
            email.setUseSignature(false);

            allmsg.add(email);
          }
        }
        */
        if (CollectionUtils.isNotEmpty(toAddresses)) {
          //System.debug('Sending email to: ' + toAddresses);
          Messaging.SingleEmailMessage email = Messaging.renderStoredEmailTemplate(
            et.Id,
            leadToProcess.Id,
            leadToProcess.Id
          );
          String subject = email.getSubject();
          String body = email.getHtmlBody();

          email.setToAddresses(toAddresses);
          email.setTreatTargetObjectAsRecipient(false);
          email.setSubject(subject);
          email.setHtmlBody(body);
          email.saveAsActivity = false;
          email.setOrgWideEmailAddressId(owea.get(0).Id);
          email.setUseSignature(false);

          allmsg.add(email);
          //System.debug('Email added to list: ' + email);
          //System.debug('Email list size: ' + allmsg.size());
        }
      }

      try {
        Messaging.SendEmailResult[] results = Messaging.sendEmail(
          allmsg,
          false
        );
        inspectResults(results);
        return;
      } catch (Exception e) {
        System.debug(e.getMessage());
      }
    }

    if (casesToProcess != null && casesToProcess.size() > 0) {
      for (Case caseToProcess : casesToProcess) {
        for (user recipientsUser : recipientsUsersList) {
          Messaging.SingleEmailMessage email = Messaging.renderStoredEmailTemplate(
            et.Id,
            recipientsUser.Id,
            caseToProcess.Id
          );
          String subject = email.getSubject();
          String body = email.getHtmlBody();

          email.setTargetObjectId(recipientsUser.Id);
          email.setSubject(subject);
          email.setHtmlBody(body);
          email.saveAsActivity = false;
          email.setOrgWideEmailAddressId(owea.get(0).Id);
          email.setUseSignature(false);
          allmsg.add(email);
        }
      }

      try {
        Messaging.SendEmailResult[] results = Messaging.sendEmail(
          allmsg,
          false
        );
        inspectResults(results);
        return;
      } catch (Exception e) {
        System.debug(e.getMessage());
      }
    }
  }

  // Public method
  public void sendMail(String address, String subject, String body) {
    // Create an email message object
    Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
    String[] toAddresses = new List<String>{ address };
    mail.setToAddresses(toAddresses);
    mail.setSubject(subject);
    mail.setPlainTextBody(body);
    // Pass this email message to the built-in sendEmail method
    // of the Messaging class
    Messaging.SendEmailResult[] results = Messaging.sendEmail(
      new List<Messaging.SingleEmailMessage>{ mail }
    );
    // Call a helper method to inspect the returned results
    inspectResults(results);
  }

  // Helper method
  private static Boolean inspectResults(Messaging.SendEmailResult[] results) {
    Boolean sendResult = true;
    // sendEmail returns an array of result objects.
    // Iterate through the list to inspect results.
    // In this class, the methods send only one email,
    // so we should have only one result.
    for (Messaging.SendEmailResult res : results) {
      if (res.isSuccess()) {
        //System.debug('Email sent successfully');
      } else {
        sendResult = false;
        //System.debug('The following errors occurred: ' + res.getErrors());
      }
    }
    return sendResult;
  }
}
