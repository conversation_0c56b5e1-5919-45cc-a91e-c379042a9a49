<template>
    <!-- SIDEBAR -->
    <div class="shiftback">
        <lightning-accordion allow-multiple-sections-open active-section-name={openAccordionSections}
            class="slds-p-around_none slds-m-around_none">
            <!-- BUTTONS -->
            <lightning-accordion-section label="Buttons" name="buttons" class="slds-p-around_none slds-m-around_none">
                <article>
                    <div class="dragdropContainer" ondrop={handleContainerDrop}>
                        <template for:each={buttons} for:item="button" for:index="index">
                            <div class="dropzone" data-index={index} key={button.value}
                                ondragover={handleDropzoneDragOver} ondragleave={handleDragLeave}></div>
                            <div class="slds-grid buttonContainer slds-p-around_xx-small" key={button.value}
                                data-index={index} ondragover={handleRowContainerDragOver}
                                ondragleave={handleDragLeave}>
                                <div>
                                    <lightning-icon icon-name="utility:drag_and_drop" class="dragHandle"
                                        draggable="true" data-index={index} ondragstart={handleDragStart}>
                                    </lightning-icon>
                                </div>
                                <div class="slds-col">
                                    <lightning-button label={button.label} data-value={button.value} data-index={index}
                                        variant={button.variant} icon-name={button.iconName}
                                        icon-position={button.iconPosition} onclick={handleButtonOpenClick}>
                                    </lightning-button>
                                    <div class="slds-text-body_small">
                                        Value: {button.value}
                                    </div>
                                </div>
                                <div class="slds-p-around_x-small buttonRowActionsContainer">
                                    <lightning-icon icon-name="utility:edit" class="buttonAction slds-p-around_xx-small"
                                        size="xx-small" data-index={index} onclick={handleButtonOpenClick}>
                                    </lightning-icon>
                                    <lightning-icon icon-name="utility:delete"
                                        class="buttonAction slds-p-around_xx-small" size="xx-small" data-index={index}
                                        onclick={handleButtonDeleteClick}></lightning-icon>
                                </div>
                            </div>
                        </template>
                        <div class="dropzone" data-index={buttons.length} ondragover={handleDropzoneDragOver}
                            ondragleave={handleDragLeave}></div>

                        <div class="buttonBarActions">
                            <template if:false={newButtonDisabled}>
                                <span class="slds-text-link slds-text-link_reset" onclick={handleNewButtonClick}>Add new
                                    button</span>
                            </template>
                            <span><span class="slds-text-link slds-text-link_reset"
                                    onclick={handleShowPreviewClick}>Show preview</span></span>
                        </div>
                    </div>
                </article>
            </lightning-accordion-section>

            <!-- SETTINGS -->
            <lightning-accordion-section label="Button Bar Settings" name="settings"
                class="slds-p-around_none slds-m-around_none">
                <article>
                    <lightning-input label={inputValues.label.label} name="label" value={inputValues.label.value}
                        onchange={handleInputValueChange}></lightning-input>
                        <lightning-input label={inputValues.cssString.label} name="cssString" value={inputValues.cssString.value}
                        onchange={handleInputValueChange}></lightning-input>

                    <c-fsc_flow-button-bar label={inputValues.actionMode.label} name="actionMode"
                        options={actionModes.options} value={inputValues.actionMode.value}
                        onbuttonclick={handleButtonClick} required
                        help-text="In Navigation mode, clicking any button causes the user to navigate to the next Flow element, where the button's value can be input to a Decision element to determine where to route them. In Selection mode, clicking the button does not cause navigation, it simply allows the user to select one or more of the options provided.">
                    </c-fsc_flow-button-bar>

                    <!-- Navigation mode settings -->
                    <template if:false={isSelectionMode}>
                        <c-fsc_flow-button-bar label={inputValues.orientation.label} name="orientation"
                            buttons={orientations.options} value={inputValues.orientation.value}
                            onbuttonclick={handleButtonClick} required>
                        </c-fsc_flow-button-bar>

                        <template if:false={isVertical}>
                            <c-fsc_flow-button-bar label={inputValues.alignment.label} name="alignment"
                                options={alignments.options} value={inputValues.alignment.value}
                                onbuttonclick={handleButtonClick} required>
                            </c-fsc_flow-button-bar>
                        </template>
                    </template>

                    <!-- Selection mode settings -->
                    <template if:true={isSelectionMode}>
                        <lightning-combobox name="defaultValue" label={inputValues.defaultValue.label} options={defaultValueOptions} value={inputValues.defaultValue.value} onchange={handleComboboxChange}></lightning-combobox>

                        <c-fsc_flow-button-bar label={inputValues.required.label} name="required" help-text="Display a red 'required' asterisk in front of the label."
                            options={yesNo.options} value={inputValues.required.value} onbuttonclick={handleButtonClick}
                            required>
                        </c-fsc_flow-button-bar>

                        <c-fsc_flow-button-bar label={inputValues.multiselect.label} name="multiselect" help-text="If multi-select is not enabled, clicking any button unselects any selected buttons."
                            options={yesNo.options} value={inputValues.multiselect.value}
                            onbuttonclick={handleButtonClick} required>
                        </c-fsc_flow-button-bar>
                    </template>

                    <lightning-combobox name="showLines" label={inputValues.showLines.label}
                        value={inputValues.showLines.value}
                        field-level-help="You can optionally add lines below and/or above the button bar to separate it from the rest of the screen. Useful if you want to replace the standard Flow footer; use 'above' to replicate the standard UI"
                        options={showLines.options} onchange={handleComboboxChange}></lightning-combobox>
                </article>
            </lightning-accordion-section>
        </lightning-accordion>
    </div>

    <!-- BUTTON EDIT MODAL -->
    <template if:true={showModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-modal__title slds-hyphenate">Build-A-Button</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <div class="slds-text-title slds-align_absolute-center">Preview</div>
                    <div class="slds-align_absolute-center">
                        <lightning-button label={selectedButton.label} variant={selectedButton.variant}
                            icon-name={selectedButton.iconName} icon-position={selectedButton.iconPosition}>
                        </lightning-button>
                    </div>

                    <hr class="slds-m-vertical_medium">
                    <div class="slds-grid slds-wrap">

                        <lightning-input class="slds-size_1-of-2 slds-p-around_xx-small" label="Label"
                            value={selectedButton.label} oncommit={handleModalLabelChange} onblur={handleModalLabelBlur} placeholder="e.g. Cancel, Continue, Save, Next..."
                            required></lightning-input>

                        <lightning-input class="slds-size_1-of-2 slds-p-around_xx-small" label="Value" field-level-help="Value can be the same as label or you can change it."
                            value={selectedButton.value} onchange={handleModalValueChange} required></lightning-input>

                        <!-- Select style (variant) for each button -->
                        <div class="slds-size_1-of-2 slds-p-around_xx-small">
                            <div class="slds-form-element">
                                <label class="slds-form-element__label">Variant</label>
                                <div class="slds-form-element__control slds-input-has-icon slds-input-has-icon_right">
                                    <template if:false={isSelectionMode}>
                                        <lightning-icon size="x-small"
                                            class="slds-icon slds-input__icon slds-input__icon_right slds-icon-text-default"
                                            icon-name="utility:down"></lightning-icon>
                                        <input type="text" placeholder="Select button variant"
                                            value={selectedButtonVariantLabel} class="slds-input"
                                            onfocus={handleStyleFocus} onblur={handleStyleBlur} />
                                    </template>
                                    <template if:true={isSelectionMode}>
                                        <input type="text" value={selectedButtonVariantLabel} class="slds-input"
                                            disabled />
                                        <span class="slds-text-body_small slds-text-color_weak slds-nubbin_bottom-left">
                                            Colour variants are available only in Navigation mode.
                                        </span>
                                    </template>

                                    <template if:true={displayVariants}>
                                        <div class="slds-dropdown slds-dropdown_fluid" role="listbox">
                                            <ul class="slds-listbox slds-listbox_vertical" role="presentation">
                                                <template for:each={variants.options} for:item="variant">
                                                    <li role="presentation"
                                                        class="slds-listbox__item slds-align_absolute-center slds-p-around_x-small"
                                                        key={variant.label}>
                                                        <lightning-button label={variant.label} variant={variant.value}
                                                            onmousedown={handleStyleSelect}></lightning-button>
                                                        <!--
                                                        <button class={style.class} data-value={style.value} data-label={style.label} data-variant={style.variant} onmousedown={handleStyleSelect}>{style.label}</button>
                                                        -->
                                                    </li>
                                                </template>
                                            </ul>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>

                        <div class="slds-size_1-of-2 slds-p-around_xx-small">
                            <c-fsc_pick-icon oniconselection={handleSelectIcon} label="Icon Name"
                                hide-standard-icons="true" hide-custom-icons="true" hide-action-icons="true"
                                mode="combobox" icon-name={selectedButton.iconName}></c-fsc_pick-icon>
                            <template if:true={selectedButton.iconName}>
                                <c-fsc_flow-button-bar label="Icon Position" options={positionOptions}
                                    value={selectedButton.iconPosition} onbuttonclick={handleIconPositionChange}
                                    required>
                                </c-fsc_flow-button-bar>
                            </template>
                        </div>

                        <!-- <div class="slds-size_1-of-2 slds-p-around_xx-small">
                            <lightning-combobox label="Flow Navigation Type"></lightning-combobox>
                        </div> -->



                        <lightning-input class="slds-size_1-of-1 slds-p-around_xx-small" label="Description Text"
                            value={selectedButton.descriptionText} onchange={handleModalDescriptionChange}
                            disabled={isHorizontal}>
                        </lightning-input>
                        <template if:true={isHorizontal}>
                            <span class="slds-text-body_small slds-text-color_weak slds-nubbin_bottom-left">
                                Description Text is only visible on vertical orientation
                            </span>
                        </template>
                    </div>
                </div>
                <footer class="slds-modal__footer slds-is-relative">
                    <span>
                        <template if:true={showConfirmDelete}>
                            <!--
                            <section class="slds-popover slds-nubbin_bottom-left slds-popover_small confirmDialog" role="dialog">
                                <div class="slds-popover__body">
                                    <p>Confirm delete?</p>
                                </div>
                            </section>
                            -->
                            <!--<button class="slds-button slds-button_destructive confirmButton" onclick={handleButtonConfirmDeleteClick}>Confirm Delete</button>-->
                        </template>
                    </span>
                    <template if:false={modalIsNewButton}>
                        <button class="slds-button slds-button_text-destructive"
                            onclick={handleButtonDeleteClick}>Delete</button>
                    </template>
                    <button class="slds-button slds-button_neutral" onclick={handleModalCancelClick}>Cancel</button>
                    <button class="slds-button slds-button_brand" onclick={handleModalSaveClick}>Save</button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <!-- PREVIEW MODAL -->
    <template if:true={showPreviewModal}>
        <section role="dialog" tabindex="0" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-modal__title slds-hyphenate">Preview</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <div class={inputValues.alignment.value}>
                        <c-fsc_flow-button-bar options={buttons} orientation={inputValues.orientation.value}
                            action-mode={inputValues.actionMode.value} alignment={inputValues.alignment.value}
                            show-lines={inputValues.showLines.value} class="slds-p-around_medium previewButtonBar"
                            preview-mode="true">
                        </c-fsc_flow-button-bar>
                    </div>
                    <div class="slds-text-body_small">Note: Navigation, required, and multiselect are not reflected in
                        preview mode</div>
                </div>
                <footer class="slds-modal__footer slds-is-relative">
                    <button class="slds-button slds-button_neutral" onclick={handlePreviewModalClose}>Close</button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>