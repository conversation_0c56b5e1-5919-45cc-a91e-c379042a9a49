/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  14 August 2024

   @ Description	:   A test class containing test methods for apex automation on the Account Object

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   SalesTerritoriesSelector

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 14 August 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public class SalesTerritoriesTestPack {
  //Test AccountTrigger
  @isTest
  static void test_TA_SalesTerritoriesTrigger() {
    Test.startTest();
    List<SalesTerritories__c> newList = new List<SalesTerritories__c>();

    newList.add(
      new SalesTerritories__c(
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'United States',
        ShippingCountryCode__c = 'US',
        ShippingState__c = 'California',
        ShippingStateCode__c = 'CA',
        ShippingZipPostalCode__c = '90001'
      )
    );

    insert newList;
    Test.stopTest();
  }

  //Test SalesTerritoriesSelector class
  @isTest
  static void test_SalesTerritoriesSelector() {
    SalesTerritoriesSelector salesTerritoriesSelector = new SalesTerritoriesSelector();
    Integer queryLimit = 100;
    Set<Id> salesTerritoriesIds = new Set<Id>{
      MockerUtils.generateId(SalesTerritories__c.SObjectType),
      MockerUtils.generateId(SalesTerritories__c.SObjectType)
    };
    Test.startTest();
    salesTerritoriesSelector.setQueryLimit(queryLimit);
    salesTerritoriesSelector.selectSalesTerritoriesByIds(salesTerritoriesIds);
    salesTerritoriesSelector.selectAllSalesTerritories();
    Test.stopTest();
  }

  @IsTest
  private static void test_TA_SalesTerritories_FastUpdates_beforeInsert() {
    List<SalesTerritories__c> newList = new List<SalesTerritories__c>();

    newList.add(
      new SalesTerritories__c(
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'United States',
        ShippingCountryCode__c = 'US',
        ShippingState__c = 'California',
        ShippingStateCode__c = 'CA',
        ShippingZipPostalCode__c = '90001'
      )
    );

    new TA_SalesTerritories_FastUpdates().beforeInsert(newList);
  }

  @IsTest
  private static void test_TA_SalesTerritories_FastUpdates_beforeUpdate() {
    List<SalesTerritories__c> newList = new List<SalesTerritories__c>();
    List<SalesTerritories__c> oldList = new List<SalesTerritories__c>();

    Id fakeSalesTerritoriesId = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );
    Id fakeSalesTerritoriesId2 = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );
    Id fakeSalesTerritoriesId3 = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );
    Id fakeSalesTerritoriesId4 = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );

    newList.add(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'United States',
        ShippingCountryCode__c = 'US',
        ShippingState__c = 'California',
        ShippingStateCode__c = 'CA',
        ShippingZipPostalCode__c = '93215-93216'
      )
    );

    newList.add(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId2,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'United States',
        ShippingCountryCode__c = 'US',
        ShippingState__c = 'California',
        ShippingStateCode__c = 'CA',
        ShippingZipPostalCode__c = '93215-93216'
      )
    );

    newList.add(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId3,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'United States',
        ShippingCountryCode__c = 'US',
        ShippingState__c = 'California',
        ShippingStateCode__c = 'CA',
        ShippingZipPostalCode__c = '93215-93216'
      )
    );

    newList.add(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId4,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'China',
        ShippingCountryCode__c = 'CN',
        ShippingState__c = '',
        ShippingStateCode__c = '',
        ShippingZipPostalCode__c = ''
      )
    );

    oldList.add(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'China',
        ShippingCountryCode__c = 'CN',
        ShippingState__c = '',
        ShippingStateCode__c = '',
        ShippingZipPostalCode__c = ''
      )
    );

    oldList.add(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId2,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'China',
        ShippingCountryCode__c = 'CN',
        ShippingState__c = '',
        ShippingStateCode__c = '',
        ShippingZipPostalCode__c = ''
      )
    );

    oldList.add(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId3,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'China',
        ShippingCountryCode__c = 'CN',
        ShippingState__c = '',
        ShippingStateCode__c = '',
        ShippingZipPostalCode__c = ''
      )
    );
    oldList.add(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId4,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'China',
        ShippingCountryCode__c = 'CN',
        ShippingState__c = '',
        ShippingStateCode__c = '',
        ShippingZipPostalCode__c = ''
      )
    );

    new TA_SalesTerritories_FastUpdates().beforeUpdate(newList, oldList);
  }
}