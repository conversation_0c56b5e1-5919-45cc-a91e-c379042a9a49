/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  31 May 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> accountIds = new Set<Id>();
                       accountIds.add('001O300000FGc3tIAD');
                       accountIds.add('001O300000FGc3tIZZ');
                       AccountSelector accountSelector = new AccountSelector();
                       List<Account> accounts = accountSelector.selectAccountsByIds(accountIds);
                       List<Account> accounts = new AccountSelector().selectAccountsByIds(accountIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ck<PERSON>
   @ Last Modified On  : 31 May 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class ContactSelector {
  private String query;
  private String fromObject = ' FROM Contact ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public ContactSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your account queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Title, GenderIdentity, FirstName, LastName, Name, AccountId, Account.Name, Application__c, Method__c, SampleType__c, OwnerId, Owner.Name, Email, Phone, HomePhone, MobilePhone, DoNotCall, HasOptedOutOfEmail, LeadSource, LeadSourceDetail__c, CreatedById, CreatedBy.Name, ReportsToId, ReportsTo.Name';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('ContactSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your accounts by a set of account ids
  public List<Contact> selectContactsByIds(Set<Id> contactIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id IN :contactIds' + this.queryLimit;
    //system.debug('selectContactsByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select your accounts by a set of contact emails
  public List<Contact> selectContactsByEmail(Set<String> contactEmails) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE Email IN :contactEmails' +
      this.queryLimit;
    //system.debug('selectContactsByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Find potential duplicate contacts by email OR phone
  public List<Contact> selectContactsByEmailOrPhone(
    String email,
    String phone
  ) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE Email = :email OR Phone = :phone' +
      this.queryLimit;
    //system.debug('selectContactsByEmailOrPhone() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Would continue to build queries and setters for everything you theoretically need.
}