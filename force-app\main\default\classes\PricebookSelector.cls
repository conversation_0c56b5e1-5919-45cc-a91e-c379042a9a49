/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:   January 2025

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> accountIds = new Set<Id>();
                       accountIds.add('001O300000FGc3tIAD');
                       accountIds.add('001O300000FGc3tIZZ');
                       AccountSelector accountSelector = new AccountSelector();
                       List<Account> accounts = accountSelector.selectAccountsByIds(accountIds);
                       List<Account> accounts = new AccountSelector().selectAccountsByIds(accountIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ck<PERSON>
   @ Last Modified On  :  January 2025
   @ Last <PERSON>dified <PERSON>  : Creation

********************************************************************************************/

public inherited sharing class PricebookSelector {
  private String query;
  private String fromObject = ' FROM Pricebook2 ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public PricebookSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your Pricebook queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Name, IsStandard, IsActive, Description';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('OpportunityContactRoleSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select pricebooks by name
  /*public List<Pricebook2> selectPricebook2ByName(String pricebookName) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Name = :pricebookName' + this.queryLimit;
    //system.debug('selectPricebook2ByName() this.query -> ' + this.query);
    return Database.query(this.query);
  }*/

  //Select pricebooks by name
  public List<Pricebook2> selectAllPricebook2() {
    buildBaseQuery();
    this.query += fromObject + this.queryLimit;
    //system.debug('selectPricebook2ByName() this.query -> ' + this.query);
    return Database.query(this.query);
  }
}