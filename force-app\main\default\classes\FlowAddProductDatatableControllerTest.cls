/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  06 February 2025

   @ Description	:   

   @ Developer Notes  : https://ericsplayground.wordpress.com/how-to-use-an-apex-defined-object-with-my-datatable-flow-component/
                        https://github.com/ericrsmith35/Flow-PB-List-View-with-Batch-Delete/blob/master/force-app/main/default/classes/FlowDatatableDescriptor.cls
                        https://rathindradakua.medium.com/how-to-use-apex-defined-collection-variables-in-your-flows-to-retrieve-values-from-server-side-50d96553ef20


   @ Github Repo	:   https://github.com/mitchspano/apex-trigger-actions-framework
                        https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master
                        https://bluecanvas.io/blog/how-to-structure-and-manage-your-apex-codebase-for-scale-and-agility

   @ Class tested	:   FlowAddProductDatatableController

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 06 February 2025   
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public with sharing class FlowAddProductDatatableControllerTest {
  @isTest
  static void testGetProductTableData() {
    // Create test data
    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();
    Pricebook2 customPb = PricebookTestUtils.createCustomPricebook(
      'Test Pricebook'
    );

    Product2 prod1 = PricebookTestUtils.createProduct('Test Product 1');
    prod1.UOMOfPackSize__c = 'ml';
    prod1.PackSize__c = 10;
    update prod1;

    Product2 prod2 = PricebookTestUtils.createProduct('Test Product 2');
    prod2.UOMOfPackSize__c = 'ml';
    prod2.PackSize__c = 20;
    update prod2;

    // Create standard price book entries
    PricebookEntry standardPbe1 = new PricebookEntry(
      Pricebook2Id = standardPb.Id,
      Product2Id = prod1.Id,
      UnitPrice = 100.00,
      IsActive = true,
      CurrencyIsoCode = 'USD'
    );
    insert standardPbe1;

    PricebookEntry standardPbe2 = new PricebookEntry(
      Pricebook2Id = standardPb.Id,
      Product2Id = prod2.Id,
      UnitPrice = 200.00,
      IsActive = true,
      CurrencyIsoCode = 'USD'
    );
    insert standardPbe2;

    // Create custom price book entries
    PricebookEntry customPbe1 = new PricebookEntry(
      Pricebook2Id = customPb.Id,
      Product2Id = prod1.Id,
      UnitPrice = 90.00,
      IsActive = true,
      CurrencyIsoCode = 'USD'
    );
    insert customPbe1;

    PricebookEntry customPbe2 = new PricebookEntry(
      Pricebook2Id = customPb.Id,
      Product2Id = prod2.Id,
      UnitPrice = 180.00,
      IsActive = true,
      CurrencyIsoCode = 'USD'
    );
    insert customPbe2;

    // Create request
    FlowAddProductDatatableController.Requests req = new FlowAddProductDatatableController.Requests();
    req.pricebookName = 'Test Pricebook';
    req.opportunityCurrency = 'USD';

    List<FlowAddProductDatatableController.Requests> requestList = new List<FlowAddProductDatatableController.Requests>();
    requestList.add(req);

    Test.startTest();
    List<FlowAddProductDatatableController.CustomOutput> results = FlowAddProductDatatableController.getProductTableData(
      requestList
    );
    Test.stopTest();

    // Verify results
    System.assertEquals(
      1,
      results.size(),
      'Should return one CustomOutput record'
    );
    System.assertEquals(
      2,
      results[0].productDataForOutput.size(),
      'Should return two product records'
    );

    // Verify first product details
    FlowDatatableProductDescriptor prod1Result = results[0]
      .productDataForOutput[0];
    System.assertEquals('Test Product 1', prod1Result.ProductName);
    System.assertEquals(prod1.Id, prod1Result.Product2Id);
    System.assertEquals(customPbe1.Id, prod1Result.PricebookEntryId);
    System.assertEquals('ml', prod1Result.UOM);
    System.assertEquals(10, prod1Result.PackSize);
    System.assertEquals(90.00, prod1Result.ListPrice);
    System.assertEquals('USD', prod1Result.ListPriceCurrency);

    // Test with invalid pricebook name
    req.pricebookName = 'Invalid Pricebook';
    requestList = new List<FlowAddProductDatatableController.Requests>();
    requestList.add(req);

    results = FlowAddProductDatatableController.getProductTableData(
      requestList
    );
    System.assertEquals(
      1,
      results.size(),
      'Should return one CustomOutput record'
    );
    System.assertEquals(
      0,
      results[0].productDataForOutput.size(),
      'Should return empty list for invalid pricebook'
    );
  }
}