/********************************************************************************************

   @ Func Area	:  Apex <PERSON>ch Job

   @ Author	:  <PERSON>

   @ Date		:  09 January 2025

   @ Description	:   This Batch job will query for all contacts and then update the MailingCountry field with the value of the Account.BillingCountry field if it is not null.
                        If the Account.BillingCountry field is null then the MailingCountry field will be updated with the value of the Account.ShippingCountry field if it is not null.
                        If the Account.BillingCountry and Account.ShippingCountry fields are null then the MailingCountry field will be updated with the value of the Contact.MailingCountry field.

                        The same process will occur against the Custom Object SalesTerritories__c which has all the territory assingment data used to create the Account unique matching key above.

                        If a match is found then the Account will be updated with a lookup relationship to the SalesTerritories__c record

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

                            How to execute via Developer Console:

                            // Define your parameters
                            // Instantiate and execute the batch job
                            BatchJobContactMailingCountry batchJob = new BatchJobContactMailingCountry();
                            ID batchProcessId = Database.executeBatch(batchJob, 200);

                            // Log the batch job ID
                            System.debug('Batch job started with ID: ' + batchProcessId);

   @ Test Class	:   BatchJobContactMailingCountryTest

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 09 January 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/

global class BatchJobContactMailingCountry implements Database.Batchable<sObject>, Schedulable {
  // Class-level variable to store information across batches
  private List<Contact> contactsToBeProcessed = new List<Contact>();

  global Database.QueryLocator start(Database.BatchableContext BC) {
    // Query for all records that are orphaned
    return Database.getQueryLocator(
      'SELECT Id, Name, MailingCountry, Account.BillingCountry, Account.ShippingCountry FROM Contact'
    );
  }

  global void execute(Database.BatchableContext BC, List<Contact> scope) {
    if (!scope.isEmpty()) {
      for (Contact c : scope) {
        if (
          c.Account.BillingCountry != null &&
          c.MailingCountry != c.Account.BillingCountry
        ) {
          c.MailingCountry = c.Account.BillingCountry;
          c.ValidationBypassDateTime__c = DateTime.now();
          contactsToBeProcessed.add(c);
        } else if (
          c.Account.ShippingCountry != null &&
          c.MailingCountry != c.Account.ShippingCountry
        ) {
          c.MailingCountry = c.Account.ShippingCountry;
          c.ValidationBypassDateTime__c = DateTime.now();
          contactsToBeProcessed.add(c);
        }
      }

      if (contactsToBeProcessed.size() > 0) {
        update contactsToBeProcessed;
      }
    }
  }

  global void finish(Database.BatchableContext BC) {
    // Optional: Add any post-processing logic here
  }

  // Schedulable interface method
  global void execute(SchedulableContext SC) {
    Database.executeBatch(new BatchJobContactMailingCountry());
  }
}