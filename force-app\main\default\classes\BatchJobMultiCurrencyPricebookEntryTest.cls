/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  13 February 2025

   @ Description	:    This Batch job processes Product2 records and creates corresponding PricebookEntry 
                        records in multiple currencies. It accepts a pricebook name, a set of currency codes, 
                        and their corresponding conversion rates. For each product in the scope, it creates 
                        PricebookEntry records in all specified currencies using the provided conversion rates.
                        
                        The job can be run either as a batch job or scheduled for regular execution. It uses 
                        the MultiCurrencyPricebookEntryCreator class to handle the actual creation of the 
                        pricebook entries.

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   BatchJobMultiCurrencyPricebookEntry

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 13 February 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public class BatchJobMultiCurrencyPricebookEntryTest {
  @IsTest
  private static void testBatchJobMultiCurrencyPricebookEntry() {
    // Set up test data
    String pricebookName = 'Standard Price Book';

    // Create Standard Price Book if it doesn't exist
    Pricebook2 standardPB = new Pricebook2(
      Id = Test.getStandardPricebookId(),
      Name = pricebookName,
      IsActive = true
    );
    update standardPB;

    // Re-Query for the Pricebook2 record, for debugging
    standardPB = [
      SELECT IsStandard
      FROM Pricebook2
      WHERE Id = :standardPB.Id
    ];

    // Create Custom DTC Price Book if it doesn't exist
    Pricebook2 dtcPB = new Pricebook2(Name = 'DTC', IsActive = true);
    insert dtcPB;

    // Re-Query for the Pricebook2 record, for debugging
    dtcPB = [
      SELECT IsStandard
      FROM Pricebook2
      WHERE Id = :dtcPB.Id
    ];

    // Create Custom CGS Price Book if it doesn't exist
    Pricebook2 cgsPB = new Pricebook2(Name = 'CGS', IsActive = true);
    insert cgsPB;

    // Re-Query for the Pricebook2 record, for debugging
    cgsPB = [
      SELECT IsStandard
      FROM Pricebook2
      WHERE Id = :cgsPB.Id
    ];

    System.assertEquals(
      true,
      standardPB.IsStandard,
      'The Standard Pricebook should now return IsStandard = true'
    );

    // Delete any existing PricebookEntries
    List<PricebookEntry> existingEntries = [SELECT Id FROM PricebookEntry];
    //System.debug('existingEntries: ' + existingEntries.size());
    if (!existingEntries.isEmpty()) {
      delete existingEntries;
    }

    Set<String> currencies = new Set<String>{ 'USD', 'EUR', 'GBP', 'CNY' };
    Map<String, Decimal> rates = new Map<String, Decimal>{
      'USD' => 1.0,
      'EUR' => 0.970768,
      'GBP' => 0.813705,
      'CNY' => 7.332045
    };

    // Create test products
    List<Product2> testProducts = new List<Product2>();
    for (Integer i = 0; i < 5; i++) {
      testProducts.add(
        new Product2(
          Name = 'Test Product ' + i,
          MOALUSListPrice__c = 100.00,
          CGSPriceBook__c = true,
          DTCPriceBook__c = true,
          IsActive = true
        )
      );
    }
    insert testProducts;

    // Construct query for batch job
    Set<Id> productIds = new Map<Id, Product2>(testProducts).keySet();
    String productIdsString =
      '(\'' +
      String.join(new List<Id>(productIds), '\',\'') +
      '\')';
    String query =
      'SELECT Id, Name, MOALUSListPrice__c, CGSPriceBook__c, DTCPriceBook__c FROM Product2 WHERE Id IN ' +
      productIdsString;

    //System.debug('query: ' + query);

    Test.startTest();

    // Create and execute batch job
    BatchJobMultiCurrencyPricebookEntry batchJob = new BatchJobMultiCurrencyPricebookEntry(
      pricebookName,
      currencies,
      rates,
      query
    );
    Database.executeBatch(batchJob, 200);

    BatchJobMultiCurrencyPricebookEntry batchJobDTC = new BatchJobMultiCurrencyPricebookEntry(
      'DTC',
      currencies,
      rates,
      query
    );
    Database.executeBatch(batchJobDTC, 200);

    BatchJobMultiCurrencyPricebookEntry batchJobCGS = new BatchJobMultiCurrencyPricebookEntry(
      'CGS',
      currencies,
      rates,
      query
    );
    Database.executeBatch(batchJobCGS, 200);

    Test.stopTest();

    // Verify results
    List<PricebookEntry> createdEntries = [
      SELECT Id, Product2Id, Pricebook2Id, CurrencyIsoCode
      FROM PricebookEntry
      WHERE Product2Id IN :testProducts
    ];

    // Assert entries were created for each product and currency
    System.assertEquals(
      testProducts.size() * currencies.size() * 3,
      createdEntries.size(),
      'Should create pricebook entries for each product in each currency'
    );
  }

  @IsTest
  private static void testBatchJobWithEmptyScope() {
    // Set up test data with empty scope
    String pricebookName = 'Standard Price Book';
    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();
    Set<String> currencies = new Set<String>{ 'USD', 'EUR' };
    Map<String, Decimal> rates = new Map<String, Decimal>{
      'USD' => 1.0,
      'EUR' => 0.970768
    };

    String query = 'SELECT Id, Name, MOALUSListPrice__c FROM Product2 WHERE Name = \'NonExistentProduct\'';

    Test.startTest();

    BatchJobMultiCurrencyPricebookEntry batchJob = new BatchJobMultiCurrencyPricebookEntry(
      pricebookName,
      currencies,
      rates,
      query
    );
    Database.executeBatch(batchJob, 200);

    Test.stopTest();

    // Verify no entries were created
    List<PricebookEntry> createdEntries = [
      SELECT Id
      FROM PricebookEntry
      WHERE CreatedDate = TODAY
    ];
    System.assertEquals(
      0,
      createdEntries.size(),
      'No entries should be created for empty scope'
    );
  }

  @isTest
  static void testSchedulableExecution() {
    // Test the schedulable interface
    Test.startTest();
    Set<String> currencies = new Set<String>{ 'USD', 'EUR', 'GBP', 'CNY' };
    Map<String, Decimal> rates = new Map<String, Decimal>{
      'USD' => 1.0,
      'EUR' => 0.970768,
      'GBP' => 0.813705,
      'CNY' => 7.332045
    };

    String query = 'SELECT Id, Name, MOALUSListPrice__c, CGSPriceBook__c, DTCPriceBook__c FROM Product2 WHERE CreatedDate = TODAY';
    String pricebookName = 'Standard Price Book';

    BatchJobMultiCurrencyPricebookEntry batchJob = new BatchJobMultiCurrencyPricebookEntry(
      pricebookName,
      currencies,
      rates,
      query
    );
    String jobId = System.schedule(
      'Test BatchJobMultiCurrencyPricebookEntry',
      '0 0 23 * * ?',
      batchJob
    );
    Test.stopTest();

    // Verify the job was scheduled
    List<CronTrigger> cronTriggers = [
      SELECT Id, CronExpression
      FROM CronTrigger
      WHERE Id = :jobId
    ];
    System.assertEquals(
      1,
      cronTriggers.size(),
      'Job should have been scheduled'
    );
  }
}