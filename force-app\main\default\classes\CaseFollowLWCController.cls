public with sharing class CaseFollowLWCController {
  @AuraEnabled(cacheable=true)
  public static List<UserOption> getAvailableUsers() {
    try {
      List<UserOption> userOptions = new List<UserOption>();

      // Get active users, excluding system users
      List<User> users = [
        SELECT Id, Name, Email, SmallPhotoUrl, IsActive
        FROM User
        WHERE IsActive = TRUE AND UserType = 'Standard'
        ORDER BY Name
        LIMIT 200
      ];

      System.debug('getAvailableUsers -> users -> ' + users);

      for (User u : users) {
        userOptions.add(new UserOption(u.Id, u.Name, u.Email, u.SmallPhotoUrl));
      }

      return userOptions;
    } catch (Exception e) {
      throw new AuraHandledException(
        'Error retrieving users: ' + e.getMessage()
      );
    }
  }

  @AuraEnabled
  public static List<UserOption> getCurrentFollowers(Id caseId) {
    System.debug('caseId -> ' + caseId);
    try {
      List<UserOption> followers = new List<UserOption>();

      List<EntitySubscription> subscriptions = [
        SELECT
          Id,
          SubscriberId,
          Subscriber.Name,
          Subscriber.Email,
          Subscriber.SmallPhotoUrl,
          Subscriber.IsActive
        FROM EntitySubscription
        WHERE ParentId = :caseId
      ];

      for (EntitySubscription sub : subscriptions) {
        // Only include active users
        if (sub.Subscriber.IsActive) {
          followers.add(
            new UserOption(
              sub.SubscriberId,
              sub.Subscriber.Name,
              sub.Subscriber.Email,
              sub.Subscriber.SmallPhotoUrl
            )
          );
        }
      }

      System.debug('getCurrentFollowers -> followers -> ' + followers);

      return followers;
    } catch (Exception e) {
      throw new AuraHandledException(
        'Error retrieving current followers: ' + e.getMessage()
      );
    }
  }

  @AuraEnabled
  public static String followCase(Id caseId, List<Id> userIds) {
    try {
      List<EntitySubscription> subscriptionsToInsert = new List<EntitySubscription>();

      // Get existing subscriptions to avoid duplicates
      Set<Id> existingFollowers = new Set<Id>();
      for (EntitySubscription existing : [
        SELECT SubscriberId
        FROM EntitySubscription
        WHERE ParentId = :caseId
      ]) {
        existingFollowers.add(existing.SubscriberId);
      }

      // Create new subscriptions for users not already following
      for (Id userId : userIds) {
        if (!existingFollowers.contains(userId)) {
          subscriptionsToInsert.add(
            new EntitySubscription(ParentId = caseId, SubscriberId = userId)
          );
        }
      }

      if (!subscriptionsToInsert.isEmpty()) {
        insert subscriptionsToInsert;
        return 'Success: ' +
          subscriptionsToInsert.size() +
          ' users are now following this case.';
      } else {
        return 'All selected users are already following this case.';
      }
    } catch (Exception e) {
      throw new AuraHandledException('Error following case: ' + e.getMessage());
    }
  }

  @AuraEnabled
  public static String unfollowCase(Id caseId, List<Id> userIds) {
    try {
      List<EntitySubscription> subscriptionsToDelete = [
        SELECT Id
        FROM EntitySubscription
        WHERE ParentId = :caseId AND SubscriberId IN :userIds
      ];

      if (!subscriptionsToDelete.isEmpty()) {
        delete subscriptionsToDelete;
        return 'Success: ' +
          subscriptionsToDelete.size() +
          ' users have been unfollowed from this case.';
      } else {
        return 'No subscriptions found to remove.';
      }
    } catch (Exception e) {
      throw new AuraHandledException(
        'Error unfollowing case: ' + e.getMessage()
      );
    }
  }

  public class UserOption {
    @AuraEnabled
    public Id id { get; set; }
    @AuraEnabled
    public String name { get; set; }
    @AuraEnabled
    public String email { get; set; }
    @AuraEnabled
    public String photoUrl { get; set; }

    public UserOption(Id id, String name, String email, String photoUrl) {
      this.id = id;
      this.name = name;
      this.email = email;
      this.photoUrl = photoUrl;
    }
  }
}
