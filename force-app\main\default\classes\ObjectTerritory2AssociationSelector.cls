/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  01 November 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> accountIds = new Set<Id>();
                       accountIds.add('001O300000FGc3tIAD');
                       accountIds.add('001O300000FGc3tIZZ');
                       AccountSelector accountSelector = new AccountSelector();
                       List<Account> accounts = accountSelector.selectAccountsByIds(accountIds);
                       List<Account> accounts = new AccountSelector().selectAccountsByIds(accountIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ck<PERSON>
   @ Last Modified On  : 01 November 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public with sharing class ObjectTerritory2AssociationSelector {
  private String query;
  private String fromObject = ' FROM ObjectTerritory2Association ';
  private String queryLimit = ' LIMIT 5000';

  //Constructor to setup the base query
  public ObjectTerritory2AssociationSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, ObjectId, Territory2Id';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('ObjectTerritory2AssociationSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }
  /*
  public Map<Id, ObjectTerritory2Association> selectObjectTerritory2AssociationByAccountIds(
    Set<Id> accountIds
  ) {
    Map<Id, ObjectTerritory2Association> existingAssociations = new Map<Id, ObjectTerritory2Association>();

    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE ObjectId IN :accountIds' +
      this.queryLimit;
    //system.debug('ObjectTerritory2AssociationSelector() this.query -> ' + this.query);

    for (ObjectTerritory2Association ota : Database.query(this.query)) {
      existingAssociations.put(ota.ObjectId, ota);
    }
    return existingAssociations;
  }
  */

  public Map<Id, List<ObjectTerritory2Association>> selectObjectTerritory2AssociationByAccountIds(
    Set<Id> accountIds
  ) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE ObjectId IN :accountIds' +
      ' AND AssociationCause = \'Territory2Manual\'' +
      this.queryLimit;
    //system.debug('ObjectTerritory2AssociationSelector() this.query -> ' + this.query);

    // Create the map
    Map<Id, List<ObjectTerritory2Association>> objectToTerritoriesMap = new Map<Id, List<ObjectTerritory2Association>>();

    // Loop through the associations
    for (ObjectTerritory2Association ota : Database.query(this.query)) {
      // Get the object ID
      Id objectId = ota.ObjectId;

      // If the map doesn't contain this objectId yet, create a new list
      if (!objectToTerritoriesMap.containsKey(objectId)) {
        objectToTerritoriesMap.put(
          objectId,
          new List<ObjectTerritory2Association>()
        );
      }

      // Add the association to the list for this objectId
      objectToTerritoriesMap.get(objectId).add(ota);
    }

    return objectToTerritoriesMap;
  }
}