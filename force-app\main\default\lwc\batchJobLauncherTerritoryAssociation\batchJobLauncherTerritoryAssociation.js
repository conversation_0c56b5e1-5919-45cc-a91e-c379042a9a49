import { LightningElement } from "lwc";
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import executeBatchJob from "@salesforce/apex/BatchJobLauncherLWCController.launchBatchJob3";

export default class BatchJobLauncherTerritoryAssociation extends LightningElement {
  isLoading = false;

  handleClick() {
    // Show confirmation dialog
    if (confirm("Are you sure you want to start the batch job?")) {
      this.launchBatchJob();
    }
  }

  launchBatchJob() {
    this.isLoading = true;
    executeBatchJob()
      .then((result) => {
        this.showToast(
          "Success",
          "Batch Job started successfully. Job ID: " + result,
          "success"
        );
      })
      .catch((error) => {
        this.showToast(
          "Error",
          "Failed to start batch job: " + this.reduceErrors(error),
          "error"
        );
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  reduceErrors(error) {
    if (typeof error === "string") {
      return error;
    }
    // Extract error message from error object
    if (Array.isArray(error.body)) {
      return error.body.map((e) => e.message).join(", ");
    } else if (error.body && typeof error.body.message === "string") {
      return error.body.message;
    }
    return "Unknown error";
  }

  showToast(title, message, variant) {
    const event = new ShowToastEvent({
      title: title,
      message: message,
      variant: variant
    });
    this.dispatchEvent(event);
  }
}