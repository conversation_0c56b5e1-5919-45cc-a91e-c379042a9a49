/********************************************************************************************

   @ Func Area	:  Note Linking Management

   @ Author	:  <PERSON>

   @ Date	:  11 June 2025

   @ Description	:   This is an extremely simple example of how you might implement a selector class for NoteLink__c

   @ Developer Notes   :   Set<Id> recordIds = new Set<Id>();
                       recordIds.add('001O300000FGc3tIAD');
                       recordIds.add('001O300000FGc3tIZZ');
                       NoteLinkSelector noteLinkSelector = new NoteLinkSelector();
                       List<NoteLink__c> noteLinks = noteLinkSelector.selectNoteLinksToDelete('001O300000FGc3tIAD', recordIds);
                       List<NoteLink__c> noteLinks = new NoteLinkSelector().selectNoteLinksToDelete('001O300000FGc3tIAD', recordIds);

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 11 June 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class NoteLinkSelector {
  private String query;
  private String fromObject = ' FROM NoteLink__c ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public NoteLinkSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your account queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Note__c, RelatedRecordId__c, RelatedObjectType__c';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
  }

  //Select your accounts by a set of account ids
  public List<NoteLink__c> selectNoteLinksToDelete(
    Id noteId,
    Set<Id> recordIds
  ) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE Note__c = :noteId AND RelatedRecordId__c IN :recordIds' +
      ' ORDER BY CreatedDate DESC' +
      this.queryLimit;

    return Database.query(this.query);
  }

  public List<NoteLink__c> selectNoteLinksByNoteId(Id noteId) {
    System.debug(
      'NoteLinkSelector.selectNoteLinksByNoteId: enter with noteId: ' + noteId
    );
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE Note__c = :noteId' +
      ' ORDER BY CreatedDate DESC' +
      this.queryLimit;

    System.debug(
      'NoteLinkSelector.selectNoteLinksByNoteId: executing query: ' + this.query
    );
    List<NoteLink__c> results = Database.query(this.query);
    System.debug(
      'NoteLinkSelector.selectNoteLinksByNoteId: found ' +
        results.size() +
        ' records'
    );

    for (NoteLink__c link : results) {
      System.debug(
        'NoteLinkSelector.selectNoteLinksByNoteId: link record: ' + link
      );
    }

    return results;
  }

  public List<NoteLink__c> getNotesForRecord(Id recordId) {
    String recordIdString = String.valueOf(recordId);
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE RelatedRecordId__c = :recordIdString' +
      ' ORDER BY CreatedDate DESC' +
      this.queryLimit;

    return Database.query(this.query);
  }
}