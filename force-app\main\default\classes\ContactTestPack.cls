/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  06 May 2024

   @ Description	:   A test class containing test methods for apex automation on the Contact Object

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   AccountSelector

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 06 May 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public class ContactTestPack {
  //Test ContactService class
  @isTest
  static void test_ContactService() {
    Test.startTest();
    // Given
    Id validContactId1 = MockerUtils.generateId(Contact.SObjectType);
    Id validContactId2 = MockerUtils.generateId(Contact.SObjectType);
    List<Id> accountIds = new List<Id>{ MockerUtils.generateId(Account.SObjectType), MockerUtils.generateId(Account.SObjectType) };

    Contact contact1 = new Contact(
      Id = validContactId1,
      FirstName = 'Joe',
      LastName = 'Soap1',
      Application__c = 'Metagenomics',
      Method__c = 'LAMP',
      SampleType__c = 'Blood'
    );
    Contact contact2 = new Contact(
      Id = validContactId2,
      FirstName = 'Joe',
      LastName = 'Soap2',
      Application__c = 'Metagenomics',
      Method__c = 'LAMP',
      SampleType__c = 'Blood'
    );

    Contact beforeUpdate = new Contact(
      FirstName = 'Joe',
      LastName = 'Soap3',
      Application__c = 'Metagenomics',
      Method__c = 'LAMP',
      SampleType__c = 'Blood'
    );

    Contact afterUpdate = new Contact(FirstName = 'Joe', LastName = 'Soap3', Application__c = '', Method__c = '', SampleType__c = '');

    Contact expectedContact = (Contact) MockerUtils.updateObjectState(
      new Contact(Id = validContactId1, FirstName = 'Peter', LastName = 'Parker'),
      new Map<String, Object>{ 'Name' => 'Peter Parker' }
    );

    List<Contact> contactsToInsert = new List<Contact>{ contact1, contact2 };
    List<Contact> contactsToUpdate = new List<Contact>{ contact1, contact2 };

    Mocker mocker = Mocker.startStubbing();

    DmlHelper dmlHelperMock = (DmlHelper) mocker.mock(DmlHelper.class);

    dmlHelperMock.insertObjects(contactsToInsert, 'ContactServiceInsert');
    Mocker.MethodRecorder insertObjectsRec = mocker.when().withAnyValues().getMethodRecorder();

    dmlHelperMock.updateObjects(contactsToUpdate, 'ContactServiceUpdate');
    Mocker.MethodRecorder updateObjectsRec = mocker.when().withAnyValues().getMethodRecorder();

    ContactSelector contactSelectorMock = (ContactSelector) mocker.mock(ContactSelector.class);

    mocker.when(contactSelectorMock.selectContactsByIds(new Set<Id>{ validContactId1 })).thenReturn(new List<Contact>{ expectedContact });

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    ContactService.contactSelector = contactSelectorMock;
    ContactService.dmlHelper = dmlHelperMock;

    // When 1
    //test method String getContactName(Id contactId) on ContactService
    String contactName = new ContactService().getContactName(validContactId1);

    // Then 1
    System.assertEquals('Peter Parker', contactName);

    // When 2
    //test method public void createContacts(List<Contact> contactsToInsert, String Source) on ContactService
    new ContactService().createContacts(contactsToInsert, 'ContactTestPack.test_ContactService()_create');

    // Then 2
    System.assertEquals(1, insertObjectsRec.getCallsCount());

    // When 3
    //test method public void updateAccounts(List<String> accountNames, String Source) on ContactService
    new ContactService().updateContacts(contactsToUpdate, 'ContactTestPack.test_ContactService()_update');

    // Then 3
    System.assertEquals(1, updateObjectsRec.getCallsCount());

    Test.stopTest();
  }

  @isTest
  static void test_TA_Contact_MultiSelectReporting_afterInsert() {
    Test.startTest();
    // Given
    Id validContactId1 = MockerUtils.generateId(Contact.SObjectType);
    Id validContactId2 = MockerUtils.generateId(Contact.SObjectType);
    Id validMultiSelectReportingId = MockerUtils.generateId(MultiSelectReporting__c.SObjectType);

    Contact afterInsert = new Contact(
      Id = validContactId2,
      FirstName = 'Joe',
      LastName = 'Soap2',
      Application__c = 'Metagenomics',
      Method__c = 'LAMP',
      SampleType__c = 'Blood',
      SequencingPlatform__c = 'Illumina',
      CurrentKit__c = 'Illumina'
    );

    //Setup MultiSelectReporting__c Test Data
    MultiSelectReporting__c expectedMultiSelectReportingRecord = (MultiSelectReporting__c) MockerUtils.updateObjectState(
      new MultiSelectReporting__c(
        Id = validMultiSelectReportingId,
        PicklistName__c = 'Method',
        PicklistNameAPI__c = 'Method__c',
        PicklistValue__c = 'Lamp'
      ),
      new Map<String, Object>{ 'Name' => 'MSEL-9999' }
    );

    Mocker mocker = Mocker.startStubbing();

    // Going to the execution phase
    mocker.stopStubbing();

    // When 4
    new TA_Contact_MultiSelectReporting().afterInsert(new List<Contact>{ afterInsert });

    // Then 4
    //System.assertEquals(1, updateObjectsRec.getCallsCount());

    Test.stopTest();
  }

  @isTest
  static void test_TA_Contact_MultiSelectReporting_afterUpdate() {
    Test.startTest();
    // Given
    Id validContactId1 = MockerUtils.generateId(Contact.SObjectType);
    Id validContactId2 = MockerUtils.generateId(Contact.SObjectType);
    Id validContactId3 = MockerUtils.generateId(Contact.SObjectType);
    Id validMultiSelectReportingId = MockerUtils.generateId(MultiSelectReporting__c.SObjectType);

    Contact oldContact = new Contact(
      Id = validContactId3,
      FirstName = 'Joe',
      LastName = 'Soap3',
      Application__c = 'Metagenomics',
      Method__c = 'LAMP',
      SampleType__c = 'Blood',
      SequencingPlatform__c = 'Illumina',
      CurrentKit__c = 'Illumina'
    );

    Contact newContact = new Contact(
      Id = validContactId3,
      FirstName = 'Joe',
      LastName = 'Soap3',
      Application__c = '',
      Method__c = '',
      SampleType__c = '',
      SequencingPlatform__c = '',
      CurrentKit__c = ''
    );

    //Setup MultiSelectReporting__c Test Data
    MultiSelectReporting__c expectedMultiSelectReportingRecord = (MultiSelectReporting__c) MockerUtils.updateObjectState(
      new MultiSelectReporting__c(
        Id = validMultiSelectReportingId,
        Contact__c = validContactId3,
        PicklistName__c = 'Method',
        PicklistNameAPI__c = 'Method__c',
        PicklistValue__c = 'Lamp'
      ),
      new Map<String, Object>{ 'Name' => 'MSEL-9999' }
    );

    Mocker mocker = Mocker.startStubbing();

    //MultiSelectReportingSelector Class
    MultiSelectReportingSelector multiSelectReportingSelectorMock = (MultiSelectReportingSelector) mocker.mock(MultiSelectReportingSelector.class);

    mocker.when(multiSelectReportingSelectorMock.selectMultiSelectReportingByContactIdsAndFieldName(new Set<Id>{ validContactId3 }, 'Application__c'))
      .withAnyValues()
      .thenReturn(new List<MultiSelectReporting__c>{ expectedMultiSelectReportingRecord });

    mocker.when(multiSelectReportingSelectorMock.selectMultiSelectReportingByContactIdsAndFieldName(new Set<Id>{ validContactId3 }, 'Method__c'))
      .withAnyValues()
      .thenReturn(new List<MultiSelectReporting__c>{ expectedMultiSelectReportingRecord });

    mocker.when(multiSelectReportingSelectorMock.selectMultiSelectReportingByContactIdsAndFieldName(new Set<Id>{ validContactId3 }, 'SampleType__c'))
      .withAnyValues()
      .thenReturn(new List<MultiSelectReporting__c>{ expectedMultiSelectReportingRecord });

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Contact_MultiSelectReporting.multiSelectReportingSelector = multiSelectReportingSelectorMock;

    // When 1
    new TA_Contact_MultiSelectReporting().afterUpdate(new List<Contact>{ newContact }, new List<Contact>{ oldContact });

    // Then 1
    //System.assertEquals(1, updateObjectsRec.getCallsCount());

    // When 2
    new TA_Contact_MultiSelectReporting().afterUpdate(new List<Contact>{ oldContact }, new List<Contact>{ newContact });

    // Then 2
    //System.assertEquals(1, updateObjectsRec.getCallsCount());

    Test.stopTest();
  }

  @isTest
  static void test_TA_Contact_MultiSelectReporting_afterDelete() {
    Test.startTest();
    // Given
    Id validContactId3 = MockerUtils.generateId(Contact.SObjectType);
    Id validMultiSelectReportingId = MockerUtils.generateId(MultiSelectReporting__c.SObjectType);

    Contact oldContact = new Contact(
      Id = validContactId3,
      FirstName = 'Joe',
      LastName = 'Soap3',
      Application__c = 'Metagenomics',
      Method__c = 'LAMP',
      SampleType__c = 'Blood',
      SequencingPlatform__c = 'Illumina',
      CurrentKit__c = 'Illumina'
    );

    Contact newContact = new Contact(
      Id = validContactId3,
      FirstName = 'Joe',
      LastName = 'Soap3',
      Application__c = '',
      Method__c = '',
      SampleType__c = '',
      SequencingPlatform__c = '',
      CurrentKit__c = ''
    );

    //Setup MultiSelectReporting__c Test Data
    MultiSelectReporting__c expectedMultiSelectReportingRecord = (MultiSelectReporting__c) MockerUtils.updateObjectState(
      new MultiSelectReporting__c(
        Id = validMultiSelectReportingId,
        Contact__c = validContactId3,
        PicklistName__c = 'Method',
        PicklistNameAPI__c = 'Method__c',
        PicklistValue__c = 'Lamp'
      ),
      new Map<String, Object>{ 'Name' => 'MSEL-9999' }
    );

    Mocker mocker = Mocker.startStubbing();

    //MultiSelectReportingSelector Class
    MultiSelectReportingSelector multiSelectReportingSelectorMock = (MultiSelectReportingSelector) mocker.mock(MultiSelectReportingSelector.class);

    mocker.when(multiSelectReportingSelectorMock.selectMultiSelectReportingByContactIds(new Set<Id>{ validContactId3 }))
      .withAnyValues()
      .thenReturn(new List<MultiSelectReporting__c>{ expectedMultiSelectReportingRecord });

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Contact_MultiSelectReporting.multiSelectReportingSelector = multiSelectReportingSelectorMock;

    // When 1
    new TA_Contact_MultiSelectReporting().beforeDelete(new List<Contact>{ oldContact });

    Test.stopTest();
  }

  //Test ContactSelector class
  @isTest
  static void test_ContactSelector() {
    ContactSelector contactSelector = new ContactSelector();
    Integer queryLimit = 100;
    Set<Id> contactIds = new Set<Id>{ MockerUtils.generateId(Contact.SObjectType), MockerUtils.generateId(Contact.SObjectType) };
    Test.startTest();
    contactSelector.setQueryLimit(queryLimit);
    contactSelector.selectContactsByIds(contactIds);
    Test.stopTest();
  }

  //Test ContactTrigger
  @isTest
  static void test_ContactTrigger() {
    Test.startTest();
    List<Contact> newList = new List<Contact>();

    newList.add(new Contact(LastName = 'Pitt', Email = '<EMAIL>'));

    insert newList;
    Test.stopTest();
  }

  @IsTest
  private static void test_TA_Contact_FastUpdates_beforeUpdate() {
    List<Contact> newList = new List<Contact>();
    List<Contact> oldList = new List<Contact>();

    //generate fake Id
    Id fakeContactId = TestFactory.getFakeId(Contact.SObjectType);

    newList.add(new Contact(Id = fakeContactId, SequencingPlatform__c = 'Illumina', CurrentKit__c = 'Illumina'));
    oldList.add(new Contact(Id = fakeContactId, SequencingPlatform__c = 'Illumina;Other', CurrentKit__c = 'Illumina;Other'));

    new TA_Contact_FastUpdates().beforeUpdate(newList, oldList);
  }
}