/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  04 April 2025

   @ Description	:   A test class containing test methods for apex automation on the Event Object

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   EventSelector

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 04 April 2025
   @ Last Modified Reason  : Creation



********************************************************************************************/
@isTest
public class EventTestPack {
  //Test EventSelector class
  @isTest
  static void test_EventSelector() {
    EventSelector eventSelector = new EventSelector();
    Integer queryLimit = 100;
    Set<Id> eventIds = new Set<Id>{
      MockerUtils.generateId(Event.SObjectType),
      MockerUtils.generateId(Event.SObjectType)
    };

    Test.startTest();
    eventSelector.setQueryLimit(queryLimit);
    eventSelector.selectEventsByIds(eventIds);

    Test.stopTest();
  }
}