/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  19 November 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> accountIds = new Set<Id>();
                       accountIds.add('001O300000FGc3tIAD');
                       accountIds.add('001O300000FGc3tIZZ');
                       AccountSelector accountSelector = new AccountSelector();
                       List<Account> accounts = accountSelector.selectAccountsByIds(accountIds);
                       List<Account> accounts = new AccountSelector().selectAccountsByIds(accountIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ck<PERSON>
   @ Last Modified On  : 19 November 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class CaseTeamTemplateRecordSelector {
    private String query;
    private String fromObject = ' FROM CaseTeamTemplateRecord ';
    private String queryLimit = ' LIMIT 1000';

    //Constructor to setup the base query
    public CaseTeamTemplateRecordSelector() {
        buildBaseQuery();
    }

    //Put your fields you intend to almost always select with your queries here
    private void buildBaseQuery() {
        this.query = 'SELECT Id, ParentId, TeamTemplateId';
    }

    //Set the limit for your query you're building
    public void setQueryLimit(Integer passedLimit) {
        String newQueryLimit = String.valueOf(passedLimit);
        this.queryLimit = ' LIMIT ' + newQueryLimit;
        //system.debug('CaseTeamTemplateRecordSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
    }

    //Select your CaseTeamTemplateRecord
    public List<CaseTeamTemplateRecord> selectCaseTeamTemplateRecordByCaseId(Id caseId) {
        buildBaseQuery();
        this.query += fromObject + 'WHERE ParentId = :caseId' + this.queryLimit;
        //system.debug('selectCaseTeamTemplates() this.query -> ' + this.query);
        return Database.query(this.query);
    }

    //Would continue to build queries and setters for everything you theoretically need.
}