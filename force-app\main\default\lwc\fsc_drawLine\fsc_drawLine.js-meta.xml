<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <masterLabel>Draw a Line</masterLabel>
    <description>Similar to horizontalRule, use this component to add a line to a Flow Screen or Lightning Record Page - By <PERSON></description>
    <apiVersion>59.0</apiVersion>
    <isExposed>true</isExposed>
    <targets>
        <target>lightning__FlowScreen</target>
        <target>lightning__RecordPage</target>
        <target>lightning__AppPage</target>
        <target>lightning__HomePage</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__FlowScreen" configurationEditor="c-fsc_draw-line-c-p-e">  <!-- configurationEditor="c-fsc_draw-line-c-p-e" -->
            <property name="marginTop" type="String" label="Top Margin" default="none" 
                description="Size selection for the top margin (Default = none)"/>
            <property name="marginBottom" type="String" label="Bottom Margin" default="xx-small"  
                description="Size selection for the bottom margin (Default = xx-small)"/>
            <property name="thickness" type="String" label="Line Thickness" default="1px" 
                description="Number of pixels for the line thickness (Default = 1px)"/>
            <property name="color" type="String" label="Line Color" default="Gray" 
                description="Color code for the line (Default = Gray)"/>
        </targetConfig>
        <targetConfig targets="lightning__RecordPage, lightning__AppPage, lightning__HomePage">
            <property name="marginTop" type="String" label="Top Margin" default="none" 
                datasource="none,xxx-small,xx-small,x-small,small,medium,large,x-large,xx-large" 
                description="Size selection for the top margin (Default = none)"/>
            <property name="marginBottom" type="String" label="Bottom Margin" default="large"  
                datasource="none,xxx-small,xx-small,x-small,small,medium,large,x-large,xx-large" 
                description="Size selection for the bottom margin (Default = large)"/>
            <property name="thickness" type="String" label="Line Thickness" default="1px" 
                description="Number of pixels for the line thickness (Default = 1px)"/>
            <property name="color" type="String" label="Line Color" default="Gray" 
                description="Color code for the line (Default = Gray)"/>
            <property name="vCard" type="Boolean" label="Display as vCard?" default="true" 
                description="Display with a white background? (Default = true)"/>
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>