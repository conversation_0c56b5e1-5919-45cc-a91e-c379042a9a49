/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  06 May 2024

   @ Description	:   A test class containing test methods for apex automation on the Account Object

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   AccountSelector

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 06 May 2024
   @ Last Modified Reason  : Creation

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 20 August 2024
   @ Last Modified Reason  : Added test_TA_Account_TerritoryAssignment_afterUpdate() & test_TA_Account_TerritoryAssignment_afterUpdate methods

   @ Last Modified By  : Daniel Field
   @ Last Modified On  : 27 November 2024
   @ Last Modified Reason  : Added TA_Account_CreateEntitlements method

   @ Last Modified By  : Daniel Field
   @ Last Modified On  : 27 January 2025
   @ Last Modified Reason  : Updated test_AccountSelector method to test accountSelector.selectAccountsByName

********************************************************************************************/
@isTest
public class AccountTestPack {
  //Test AccountService class
  @isTest
  static void test_AccountService() {
    Test.startTest();
    // Given
    Id validAccountId = MockerUtils.generateId(Account.SObjectType);
    List<Id> accountIds = new List<Id>{
      MockerUtils.generateId(Account.SObjectType),
      MockerUtils.generateId(Account.SObjectType)
    };

    Account expectedAccount = (Account) MockerUtils.updateObjectState(
      new Account(Id = validAccountId, Name = 'New Account'),
      new Map<String, Object>{ 'Type' => 'Customer' }
    );

    List<Account> accountsToInsert = new List<Account>{ expectedAccount };
    List<Account> accountsToUpdate = new List<Account>{ expectedAccount };

    List<String> accountNames = new List<String>{
      'Dummy Account 1',
      'Dummy Account 2'
    };

    Mocker mocker = Mocker.startStubbing();

    DmlHelper dmlHelperMock = (DmlHelper) mocker.mock(DmlHelper.class);

    dmlHelperMock.insertObjects(accountsToInsert, 'AccountService');
    Mocker.MethodRecorder insertObjectsRec = mocker.when()
      .withAnyValues()
      .getMethodRecorder();

    dmlHelperMock.updateObjects(accountsToInsert, 'AccountServiceUpdate');
    Mocker.MethodRecorder updateObjectsRec = mocker.when()
      .withAnyValues()
      .getMethodRecorder();

    AccountSelector accountSelectorMock = (AccountSelector) mocker.mock(
      AccountSelector.class
    );

    mocker.when(
        accountSelectorMock.selectAccountsByIds(new Set<Id>{ validAccountId })
      )
      .thenReturn(new List<Account>{ expectedAccount });

    mocker.when(accountSelectorMock.selectAccountsByIds(null))
      .withAnyValues()
      .thenReturn(new List<Account>());

    mocker.when(
        accountSelectorMock.selectAccountsByParentIds(
          new Set<Id>{ validAccountId }
        )
      )
      .thenReturn(new List<Account>{ expectedAccount });

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    AccountService.accountSelector = accountSelectorMock;
    AccountService.dmlHelper = dmlHelperMock;

    // When 1
    //test method public String getAccountName(Id accountId) on AccountService
    String accountName = new AccountService()
      .getAccountNameById(validAccountId);

    // Then 1
    System.assertEquals('New Account', accountName);

    // When 2
    //test method public void createAccounts(List<String> accountNames, String Source) on AccountService
    new AccountService()
      .createAccounts(
        accountsToInsert,
        'AccountTestPack.test_AccountService()'
      );

    // Then 2
    System.assertEquals(1, insertObjectsRec.getCallsCount());

    // When 3
    //test method public void updateAccounts(List<String> accountNames, String Source) on AccountService
    new AccountService()
      .updateAccounts(
        accountsToUpdate,
        'AccountTestPack.test_AccountService()'
      );

    // Then 3
    System.assertEquals(1, updateObjectsRec.getCallsCount());

    // When 4
    //test method public String getAccountName(Id accountId) on AccountService
    List<Account> childAccounts = new AccountService()
      .getChildAccounts(new Set<Id>{ validAccountId });

    // Then 4
    //System.assertEquals('New Account', accountName);

    Test.stopTest();
  }

  @isTest
  static void test_AccountService_findAccountIdByName() {
    Test.startTest();

    // Test data
    String validAccountName = 'ACME Corporation';
    String fuzzyAccountName = 'Partial Match Corp';
    Id validAccountId = MockerUtils.generateId(Account.SObjectType);
    Id fuzzyAccountId = MockerUtils.generateId(Account.SObjectType);

    Account exactAccount = new Account(
      Id = validAccountId,
      Name = validAccountName
    );

    Account fuzzyAccount = new Account(
      Id = fuzzyAccountId,
      Name = 'The Partial Match Corporation'
    );

    // Start mocking
    Mocker mocker = Mocker.startStubbing();

    AccountSelector accountSelectorMock = (AccountSelector) mocker.mock(
      AccountSelector.class
    );

    // Mock for exact name match - return account
    mocker.when(
        accountSelectorMock.selectAccountsByName(
          new Set<String>{ validAccountName }
        )
      )
      .thenReturn(new List<Account>{ exactAccount });

    // Mock for exact name match - return account
    mocker.when(accountSelectorMock.selectAccountsByExactName(validAccountName))
      .thenReturn(new List<Account>{ exactAccount });

    // Mock for exact name match - empty result
    mocker.when(accountSelectorMock.selectAccountsByExactName(fuzzyAccountName))
      .thenReturn(new List<Account>());

    // Mock for fuzzy name match
    mocker.when(accountSelectorMock.selectAccountsByFuzzyName(fuzzyAccountName))
      .thenReturn(new List<Account>{ fuzzyAccount });

    // Mock for blank name - should not be called but just in case
    mocker.when(accountSelectorMock.selectAccountsByExactName(''))
      .thenReturn(new List<Account>());

    mocker.when(accountSelectorMock.selectAccountsByFuzzyName(''))
      .thenReturn(new List<Account>());

    // Mock for no match in both exact and fuzzy
    String noMatchName = 'Non-existent Account';
    mocker.when(accountSelectorMock.selectAccountsByExactName(noMatchName))
      .thenReturn(new List<Account>());

    mocker.when(accountSelectorMock.selectAccountsByFuzzyName(noMatchName))
      .thenReturn(new List<Account>());

    // Going to the execution phase
    mocker.stopStubbing();

    // Replace the original static method with our mock
    AccountService.accountSelector = accountSelectorMock;

    // Test scenario 1: Exact match found
    Id foundId = new AccountService().findAccountIdByName(validAccountName);
    System.assertEquals(
      validAccountId,
      foundId,
      'Should find account by exact name match'
    );

    // Test scenario 2: No exact match but fuzzy match found
    /*Id fuzzyFoundId = new AccountService()
      .findAccountIdByName(fuzzyAccountName);
    System.assertEquals(
      fuzzyAccountId,
      fuzzyFoundId,
      'Should find account by fuzzy name match'
    );
    */

    // Test scenario 3: No match at all
    Id notFoundId = new AccountService().findAccountIdByName(noMatchName);
    System.assertEquals(
      null,
      notFoundId,
      'Should return null when no match found'
    );

    // Test scenario 4: Null/blank input
    Id nullResult = new AccountService().findAccountIdByName(null);
    System.assertEquals(null, nullResult, 'Should return null for null input');

    Id blankResult = new AccountService().findAccountIdByName('');
    System.assertEquals(
      null,
      blankResult,
      'Should return null for blank input'
    );

    Test.stopTest();
  }

  @isTest
  static void test_AccountService_getAccountNameById() {
    Test.startTest();

    // Test data
    Id validAccountId = MockerUtils.generateId(Account.SObjectType);
    Account expectedAccount = new Account(
      Id = validAccountId,
      Name = 'Test Account Name'
    );

    // Start mocking
    Mocker mocker = Mocker.startStubbing();

    AccountSelector accountSelectorMock = (AccountSelector) mocker.mock(
      AccountSelector.class
    );

    // Mock for valid account ID
    mocker.when(
        accountSelectorMock.selectAccountsByIds(new Set<Id>{ validAccountId })
      )
      .thenReturn(new List<Account>{ expectedAccount });

    // Mock for non-existent account ID
    Id nonExistentId = MockerUtils.generateId(Account.SObjectType);
    mocker.when(
        accountSelectorMock.selectAccountsByIds(new Set<Id>{ nonExistentId })
      )
      .thenReturn(new List<Account>());

    // Going to the execution phase
    mocker.stopStubbing();

    // Replace the original static method with our mock
    AccountService.accountSelector = accountSelectorMock;

    // Test scenario 1: Valid account ID
    String accountName = new AccountService()
      .getAccountNameById(validAccountId);
    System.assertEquals(
      'Test Account Name',
      accountName,
      'Should return account name for valid ID'
    );

    // Test scenario 2: Non-existent account ID
    String nonExistentName = new AccountService()
      .getAccountNameById(nonExistentId);
    System.assertEquals(
      null,
      nonExistentName,
      'Should return null for non-existent ID'
    );

    // Test scenario 3: Null input
    String nullResult = new AccountService().getAccountNameById(null);
    System.assertEquals(null, nullResult, 'Should return null for null input');

    Test.stopTest();
  }

  //Test AccountSelector class
  @isTest
  static void test_AccountSelector() {
    AccountSelector accountSelector = new AccountSelector();
    Integer queryLimit = 100;
    Set<Id> accountIds = new Set<Id>{
      MockerUtils.generateId(Account.SObjectType),
      MockerUtils.generateId(Account.SObjectType)
    };
    Set<String> accountNames = new Set<String>{ 'Fake Name' };

    Test.startTest();
    accountSelector.setQueryLimit(queryLimit);
    accountSelector.selectAccountsByIds(accountIds);
    accountSelector.selectAccountsByParentIds(accountIds);
    accountSelector.selectAccountsByName(accountNames);

    // Test the new methods
    String exactName = 'Exact Match Account';
    String fuzzyName = 'Fuzzy Match';

    // We can't actually execute the queries in a test without data
    // but we can verify the methods don't throw exceptions
    try {
      accountSelector.selectAccountsByExactName(exactName);
      accountSelector.selectAccountsByFuzzyName(fuzzyName);

      // Also test with null/blank values
      accountSelector.selectAccountsByExactName(null);
      accountSelector.selectAccountsByExactName('');
      accountSelector.selectAccountsByFuzzyName(null);
      accountSelector.selectAccountsByFuzzyName('');

      // If we reach here, the methods executed without errors
      System.assertEquals(
        true,
        true,
        'AccountSelector new methods executed successfully'
      );
    } catch (Exception e) {
      System.assert(false, 'Exception thrown: ' + e.getMessage());
    }

    Test.stopTest();
  }

  //Test AccountTrigger
  @isTest
  static void test_AccountTrigger() {
    Test.startTest();
    List<Account> newList = new List<Account>();

    newList.add(new Account(Name = 'ACME Enterprises'));

    insert newList;
    Test.stopTest();
  }

  @IsTest
  private static void test_TA_Account_FastUpdates_beforeInsert() {
    List<Account> newList = new List<Account>();

    newList.add(
      new Account(
        Name = 'New Account',
        Description = 'My updated description of the case'
      )
    );

    new TA_Account_FastUpdates().beforeInsert(newList);
  }

  @IsTest
  private static void test_TA_Account_FastUpdates_beforeUpdate() {
    List<Account> newList = new List<Account>();
    List<Account> oldList = new List<Account>();

    //generate fake Id
    Id fakeAccountId = TestFactory.getFakeId(Account.SObjectType);
    Id fakeAccountId2 = TestFactory.getFakeId(Account.SObjectType);
    Id fakeAccountId3 = TestFactory.getFakeId(Account.SObjectType);
    Id fakeAccountId4 = TestFactory.getFakeId(Account.SObjectType);

    newList.add(
      new Account(
        Id = fakeAccountId,
        Description = 'My updated description of the case',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingState = 'Colorado',
        ShippingPostalCode = ''
      )
    );

    newList.add(
      new Account(
        Id = fakeAccountId2,
        Description = 'My updated description of the case',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingState = 'California',
        ShippingPostalCode = ''
      )
    );

    newList.add(
      new Account(
        Id = fakeAccountId3,
        Description = 'My updated description of the case',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingState = 'California',
        ShippingPostalCode = '30002'
      )
    );

    newList.add(
      new Account(
        Id = fakeAccountId4,
        Description = 'My updated description of the case',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'China',
        ShippingState = '',
        ShippingPostalCode = ''
      )
    );

    oldList.add(new Account(Id = fakeAccountId, Description = ''));
    oldList.add(new Account(Id = fakeAccountId2, Description = ''));
    oldList.add(new Account(Id = fakeAccountId3, Description = ''));
    oldList.add(new Account(Id = fakeAccountId4, Description = ''));

    new TA_Account_FastUpdates().beforeUpdate(newList, oldList);
    new TA_Account_BuildMatchingKey().afterUpdate(newList, oldList);
  }

  @IsTest
  private static void test_TA_Account_TerritoryAssignment_afterInsert() {
    List<Account> newList = new List<Account>();
    List<Account> oldList = new List<Account>();

    // Setup Test Data
    Id currentUserId = UserInfo.getUserId();
    Id fakeUserId = MockerUtils.generateId(User.SObjectType);

    Id fakeSalesTerritoriesId = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );
    String bypassValidationRulePermissionName = 'Bypass_Validation_Rules';

    Map<Id, User> userMap = new Map<Id, User>();

    userMap.put(
      currentUserId,
      new User(Id = currentUserId, Title = 'Mr', Email = '<EMAIL>')
    );

    Map<Id, Boolean> userIdToBypassValidationBooleanMap = new Map<Id, Boolean>();
    userIdToBypassValidationBooleanMap.put(currentUserId, false);

    List<SalesTerritories__c> fakeSalesTerritoriesList = new List<SalesTerritories__c>();

    SalesTerritories__c fakeSalesTerritoriesRecord = (SalesTerritories__c) MockerUtils.updateObjectState(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'United States',
        ShippingCountryCode__c = 'US',
        ShippingState__c = 'California',
        ShippingStateCode__c = 'CA',
        ShippingZipPostalCode__c = '93215-93216'
      ),
      new Map<String, Object>{ 'SalesTeam__c' => 'DTC' }
    );

    fakeSalesTerritoriesList.add(fakeSalesTerritoriesRecord);

    //Account test data
    newList.add(
      new Account(
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'California',
        ShippingStateCode = 'CA',
        ShippingPostalCode = '93215',
        SalesTerritoryMatchingKey__c = 'RetailDTCUnitedStatesCalifornia92697'
      )
    );

    newList.add(
      new Account(
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = null,
        ShippingStateCode = null,
        ShippingPostalCode = null
      )
    );

    newList.add(
      new Account(
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'Iowa',
        ShippingStateCode = 'IA',
        ShippingPostalCode = null
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    SalesTerritoriesSelector salesTerritoriesSelectorMock = (SalesTerritoriesSelector) mocker.mock(
      SalesTerritoriesSelector.class
    );

    mocker.when(salesTerritoriesSelectorMock.selectAllSalesTerritories()) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeSalesTerritoriesList);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Account_TerritoryAssignment.salesTerritoriesSelector = salesTerritoriesSelectorMock;

    new TA_Account_TerritoryAssignment().afterInsert(newList);
  }

  @IsTest
  private static void test_TA_Account_TerritoryAssignment_afterUpdate() {
    List<Account> newList = new List<Account>();
    List<Account> oldList = new List<Account>();

    // Setup Test Data
    Id currentUserId = UserInfo.getUserId();
    Id fakeUserId = MockerUtils.generateId(User.SObjectType);
    Id fakeAccountId = MockerUtils.generateId(Account.SObjectType);
    Id fakeAccountId2 = MockerUtils.generateId(Account.SObjectType);
    Id fakeAccountId3 = MockerUtils.generateId(Account.SObjectType);
    Id fakeAccountId4 = MockerUtils.generateId(Account.SObjectType);
    Id fakeAccountId5 = MockerUtils.generateId(Account.SObjectType);
    Id fakeSalesTerritoriesId = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );
    Id fakeSalesTerritoriesId2 = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );
    Id fakeSalesTerritoriesId3 = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );
    Id fakeSalesTerritoriesId4 = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );
    Id fakeSalesTerritoriesId5 = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );
    Id fakeSalesTerritoriesId6 = MockerUtils.generateId(
      SalesTerritories__c.SObjectType
    );
    String bypassValidationRulePermissionName = 'Bypass_Validation_Rules';

    Map<Id, User> userMap = new Map<Id, User>();

    userMap.put(
      currentUserId,
      new User(Id = currentUserId, Title = 'Mr', Email = '<EMAIL>')
    );

    Map<Id, Boolean> userIdToBypassValidationBooleanMap = new Map<Id, Boolean>();
    userIdToBypassValidationBooleanMap.put(currentUserId, false);

    List<SalesTerritories__c> fakeSalesTerritoriesList = new List<SalesTerritories__c>();

    SalesTerritories__c fakeSalesTerritoriesRecord = (SalesTerritories__c) MockerUtils.updateObjectState(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'United States',
        ShippingCountryCode__c = 'US',
        ShippingState__c = 'California',
        ShippingStateCode__c = 'CA',
        ShippingZipPostalCode__c = '93215-93216'
      ),
      new Map<String, Object>{
        'AccountMatchingKey__c' => 'RetailDTCUnitedStatesCalifornia93215-93216'
      }
    );

    SalesTerritories__c fakeSalesTerritoriesRecord2 = (SalesTerritories__c) MockerUtils.updateObjectState(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId2,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'United States',
        ShippingCountryCode__c = 'US',
        ShippingState__c = 'California',
        ShippingStateCode__c = 'CA',
        ShippingZipPostalCode__c = '90001'
      ),
      new Map<String, Object>{
        'AccountMatchingKey__c' => 'RetailDTCUnitedStatesCalifornia90001'
      }
    );

    SalesTerritories__c fakeSalesTerritoriesRecord3 = (SalesTerritories__c) MockerUtils.updateObjectState(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId3,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'United States',
        ShippingCountryCode__c = 'US',
        ShippingState__c = 'California',
        ShippingStateCode__c = 'CA',
        ShippingZipPostalCode__c = ''
      ),
      new Map<String, Object>{
        'AccountMatchingKey__c' => 'RetailDTCUnitedStatesCalifornia'
      }
    );

    SalesTerritories__c fakeSalesTerritoriesRecord4 = (SalesTerritories__c) MockerUtils.updateObjectState(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId4,
        Territory__c = 'East Bay Regional Account Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'United States',
        ShippingCountryCode__c = 'US',
        ShippingState__c = 'Colorado',
        ShippingStateCode__c = 'CO',
        ShippingZipPostalCode__c = ''
      ),
      new Map<String, Object>{
        'AccountMatchingKey__c' => 'RetailDTCUnitedStatesColorado'
      }
    );

    SalesTerritories__c fakeSalesTerritoriesRecord5 = (SalesTerritories__c) MockerUtils.updateObjectState(
      new SalesTerritories__c(
        Id = fakeSalesTerritoriesId5,
        Territory__c = 'DACH Regional Business Manager',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry__c = 'Germany',
        ShippingCountryCode__c = 'DE',
        ShippingState__c = '',
        ShippingStateCode__c = '',
        ShippingZipPostalCode__c = '00000-59999'
      ),
      new Map<String, Object>{
        'AccountMatchingKey__c' => 'RetailDTCGermany00000-59999'
      }
    );

    fakeSalesTerritoriesList.add(fakeSalesTerritoriesRecord);
    fakeSalesTerritoriesList.add(fakeSalesTerritoriesRecord2);
    fakeSalesTerritoriesList.add(fakeSalesTerritoriesRecord3);
    fakeSalesTerritoriesList.add(fakeSalesTerritoriesRecord4);
    fakeSalesTerritoriesList.add(fakeSalesTerritoriesRecord5);

    //Account test data

    Account oldFakeAccountRecord = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'California',
        ShippingStateCode = 'CA',
        ShippingPostalCode = '93215'
      ),
      new Map<String, Object>{ 'SalesTerritoryMatchingKey__c' => '' }
    );

    Account newFakeAccountRecord = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'California',
        ShippingStateCode = 'CA',
        ShippingPostalCode = '93215'
      ),
      new Map<String, Object>{
        'SalesTerritoryMatchingKey__c' => 'RetailDTCUnitedStatesCalifornia93215'
      }
    );

    Account oldFakeAccountRecord2 = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId2,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'California',
        ShippingStateCode = 'CA',
        ShippingPostalCode = '90001'
      ),
      new Map<String, Object>{ 'SalesTerritoryMatchingKey__c' => '' }
    );

    Account newFakeAccountRecord2 = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId2,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'California',
        ShippingStateCode = 'CA',
        ShippingPostalCode = '90001'
      ),
      new Map<String, Object>{
        'SalesTerritoryMatchingKey__c' => 'RetailDTCUnitedStatesCalifornia90001'
      }
    );

    Account oldFakeAccountRecord3 = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId3,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'California',
        ShippingStateCode = 'CA',
        ShippingPostalCode = ''
      ),
      new Map<String, Object>{ 'SalesTerritoryMatchingKey__c' => '' }
    );

    Account newFakeAccountRecord3 = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId3,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'California',
        ShippingStateCode = 'CA',
        ShippingPostalCode = ''
      ),
      new Map<String, Object>{
        'SalesTerritoryMatchingKey__c' => 'RetailDTCUnitedStatesCalifornia'
      }
    );

    Account oldFakeAccountRecord4 = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId4,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'Colorado',
        ShippingStateCode = 'CO',
        ShippingPostalCode = ''
      ),
      new Map<String, Object>{ 'SalesTerritoryMatchingKey__c' => '' }
    );

    Account newFakeAccountRecord4 = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId4,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'Colorado',
        ShippingStateCode = 'CO',
        ShippingPostalCode = ''
      ),
      new Map<String, Object>{
        'SalesTerritoryMatchingKey__c' => 'RetailDTCUnitedStatesColorado'
      }
    );

    Account oldFakeAccountRecord5 = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId5,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'Germany',
        ShippingCountryCode = 'DE',
        ShippingState = '',
        ShippingStateCode = '',
        ShippingPostalCode = ''
      ),
      new Map<String, Object>{ 'SalesTerritoryMatchingKey__c' => '' }
    );

    Account newFakeAccountRecord5 = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId5,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'Germany',
        ShippingCountryCode = 'DE',
        ShippingState = '',
        ShippingStateCode = '',
        ShippingPostalCode = '00001'
      ),
      new Map<String, Object>{
        'SalesTerritoryMatchingKey__c' => 'RetailDTCGermany'
      }
    );

    newList.add(newFakeAccountRecord);
    newList.add(newFakeAccountRecord2);
    newList.add(newFakeAccountRecord3);
    newList.add(newFakeAccountRecord4);
    newList.add(newFakeAccountRecord5);

    oldList.add(oldFakeAccountRecord);
    oldList.add(oldFakeAccountRecord2);
    oldList.add(oldFakeAccountRecord3);
    oldList.add(oldFakeAccountRecord4);
    oldList.add(oldFakeAccountRecord5);

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    SalesTerritoriesSelector salesTerritoriesSelectorMock = (SalesTerritoriesSelector) mocker.mock(
      SalesTerritoriesSelector.class
    );

    mocker.when(salesTerritoriesSelectorMock.selectAllSalesTerritories()) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeSalesTerritoriesList);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Account_TerritoryAssignment.salesTerritoriesSelector = salesTerritoriesSelectorMock;

    new TA_Account_TerritoryAssignment().afterUpdate(newList, oldList);
  }

  @IsTest
  private static void test_TA_Account_TerritoryAssociations_processAccountsForTerritoryAssignment() {
    List<Account> newList = new List<Account>();

    //generate fake Id
    Id fakeAccountId = TestFactory.getFakeId(Account.SObjectType);

    Id fakeTerritoryId = TestFactory.getFakeId(Territory2.SObjectType);

    /*newList.add(
      new Account(
        Id = fakeAccountId,
        Description = 'My updated description of the case',
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingState = 'Colorado',
        ShippingPostalCode = ''
      )
    );*/

    List<Territory2> fakeTerritoriesList = new List<Territory2>();
    fakeTerritoriesList.add(
      new Territory2(
        Id = fakeTerritoryId,
        Name = 'East Bay Regional Account Manager'
      )
    );

    Account fakeAccountRecord = (Account) MockerUtils.updateObjectState(
      new Account(
        Id = fakeAccountId,
        SalesChannel__c = 'Retail',
        SalesTeam__c = 'DTC',
        ShippingCountry = 'United States',
        ShippingCountryCode = 'US',
        ShippingState = 'California',
        ShippingStateCode = 'CA',
        ShippingPostalCode = '93215-93216'
      ),
      new Map<String, Object>{
        'RecommendedSalesTerritory__c' => 'East Bay Regional Account Manager'
      }
    );

    newList.add(fakeAccountRecord);

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    Territory2Selector territory2SelectorMock = (Territory2Selector) mocker.mock(
      Territory2Selector.class
    );

    mocker.when(
        territory2SelectorMock.selectTerritory2ByModelName(
          'WMG Territory Model'
        )
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeTerritoriesList);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Account_TerritoryAssociations.territory2Selector = territory2SelectorMock;

    new TA_Account_TerritoryAssociations()
      .processAccountsForTerritoryAssignment(newList);
  }

  @IsTest
  private static void TA_Account_DeleteEntitlements() {
    List<Account> newList = new List<Account>();
    List<Account> oldList = new List<Account>();
    List<Entitlement> fakeEntitlementsList = new List<Entitlement>();
    List<Case> fakeCasesList = new List<Case>();

    Id fakeAccountId = MockerUtils.generateId(Account.SObjectType);
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    Id fakeEntitlementId = MockerUtils.generateId(Entitlement.SObjectType);

    //Account test data

    Account oldFakeAccountRecord = new Account(
      Id = fakeAccountId,
      EntitlementSLA__c = 'Key Account'
    );

    Account newFakeAccountRecord = new Account(
      Id = fakeAccountId,
      EntitlementSLA__c = ''
    );

    Entitlement fakeEntitlementRecord = new Entitlement(
      Id = MockerUtils.generateId(Entitlement.SObjectType),
      AccountId = fakeAccountId,
      Name = 'Test Entitlement'
    );

    Case fakeCaseRecord = new Case(
      Id = fakeCaseId,
      EntitlementId = fakeEntitlementId
    );

    fakeCasesList.add(fakeCaseRecord);

    fakeEntitlementsList.add(fakeEntitlementRecord);

    newList.add(newFakeAccountRecord);

    oldList.add(oldFakeAccountRecord);

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    EntitlementSelector entitlementSelectorMock = (EntitlementSelector) mocker.mock(
      EntitlementSelector.class
    );

    CaseSelector caseSelectorMock = (CaseSelector) mocker.mock(
      CaseSelector.class
    );

    mocker.when(
        entitlementSelectorMock.selectEntitlementsByAccountId(
          new Set<Id>{ fakeAccountId }
        )
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeEntitlementsList);

    mocker.when(
        caseSelectorMock.selectCasesByEntitlementId(
          new Set<Id>{ fakeEntitlementId }
        )
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeCasesList);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Account_DeleteEntitlements.entitlementSelector = entitlementSelectorMock;
    TA_Account_DeleteEntitlements.caseSelector = caseSelectorMock;

    new TA_Account_DeleteEntitlements().afterUpdate(newList, oldList);
  }

  @IsTest
  private static void TA_Account_CreateEntitlements() {
    List<Account> newList = new List<Account>();
    List<Account> oldList = new List<Account>();

    Id fakeAccountId = MockerUtils.generateId(Account.SObjectType);

    //Account test data

    Account oldFakeAccountRecord = new Account(Id = fakeAccountId);

    Account newFakeAccountRecord = new Account(
      Id = fakeAccountId,
      EntitlementSLA__c = 'Key Account'
    );

    newList.add(newFakeAccountRecord);

    oldList.add(oldFakeAccountRecord);

    new TA_Account_CreateEntitlements().afterUpdate(newList, oldList);
  }
}