/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  15 August 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                    there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                    to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> caseIds = new Set<Id>();
                        caseIds.add('500O3000008HgXhIAK');
                        caseIds.add('500O3000008Hh3xIAC');
                        CaseSelector caseSelector = new CaseSelector();
                        List<Case> cases = caseSelector.selectCasesById(caseIds);
                        List<Case> cases = new CaseSelector().selectCasesById(caseIds);

                        https://help.salesforce.com/s/articleView?id=sf.entitlements_milestones_trigger.htm&type=5

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 15 August 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class CaseMilestoneSelector {
    private String query;
    private String fromObject = ' FROM CaseMilestone ';
    private String queryLimit = ' LIMIT 1000';

    //Constructor to setup the base query
    public CaseMilestoneSelector() {
        buildBaseQuery();
    }

    //Put your fields you intend to almost always select with your case queries here
    private void buildBaseQuery() {
        this.query = 'SELECT Id, BusinessHoursId, CaseId, CompletionDate, ElapsedTimeInDays, ElapsedTimeInHrs, ElapsedTimeInMins, IsCompleted, IsViolated, MilestoneTypeId, StartDate, TargetDate, TargetResponseInDays, TargetResponseInHrs, TargetResponseInMins, TimeRemainingInDays, TimeRemainingInHrs, TimeRemainingInMins, TimeSinceTargetInDays, TimeSinceTargetInHrs, TimeSinceTargetInMins';
    }

    //Set the limit for your query you're building
    public void setQueryLimit(Integer passedLimit) {
        String newQueryLimit = String.valueOf(passedLimit);
        this.queryLimit = ' LIMIT ' + newQueryLimit;
        //system.debug('CaseMilestoneSelector setQueryLimit() new queryLimit -> ' + this.queryLimit);
    }

    //Select your cases by a set of case ids
    public List<CaseMilestone> selectCaseMilestonesByCaseIds(Set<Id> caseIds) {
        buildBaseQuery();
        this.query += fromObject + 'WHERE CaseId IN :caseIds' + this.queryLimit;
        //system.debug('CaseMilestoneSelector selectCasesById() this.query -> ' + this.query);
        return Database.query(this.query);
    }

    //Select your cases by a set of case ids
    public List<CaseMilestone> selectCaseMilestonesByCaseIdsAndMilestoneName(Set<Id> caseIds, String mileStoneName) {
        buildBaseQuery();
        this.query += fromObject + 'WHERE CaseId IN :caseIds AND MilestoneType.Name = :mileStoneName AND completionDate = null' + this.queryLimit;
        system.debug('CaseMilestoneSelector selectCaseMilestonesByCaseIdsAndMilestoneName(Set<Id> caseIds, String mileStoneName) this.query -> ' + this.query);
        return Database.query(this.query);
    }

    //Would continue to build queries and setters for everything you theoretically need.
}