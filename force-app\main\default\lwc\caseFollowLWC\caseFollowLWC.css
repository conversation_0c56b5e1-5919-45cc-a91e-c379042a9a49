/* Header Styles */
.followers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #d8dde6;
  background: #f3f3f3;
  border-radius: 0.25rem 0.25rem 0 0;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #181818;
}

/* Inline Followers Display */
.followers-content {
  padding: 1rem;
  border: 1px solid #d8dde6;
  border-top: none;
  border-radius: 0 0 0.25rem 0.25rem;
  background: white;
}

.followers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.follower-card-inline {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #d8dde6;
  border-radius: 0.25rem;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.follower-avatar {
  margin-right: 0.75rem;
}

.follower-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.follower-info {
  flex: 1;
}

.follower-name {
  font-weight: 600;
  color: #181818;
  margin-bottom: 0.25rem;
}

.follower-email {
  font-size: 0.875rem;
  color: #706e6b;
}

.empty-state-inline {
  text-align: center;
  padding: 3rem 2rem;
  color: #706e6b;
}

.empty-state-inline h3 {
  margin: 1rem 0 0.5rem 0;
  font-size: 1.125rem;
  color: #181818;
}

.empty-state-inline p {
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
}

.trigger-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.follow-trigger-btn {
  min-width: 60px;
}

.followers-label {
  font-size: 0.875rem;
  color: #706e6b;
}

.user-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 0.75rem;
  max-height: 400px;
  overflow-y: auto;
}

.user-card {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #d8dde6;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.user-card:hover {
  border-color: #1589ee;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-card.selected {
  border-color: #1589ee;
  background-color: #f3f9ff;
}

.user-card.selected .selection-indicator {
  opacity: 1;
  color: #1589ee;
}

.user-avatar {
  margin-right: 0.75rem;
}

.user-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #181818;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.875rem;
  color: #706e6b;
}

.selection-indicator {
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: 0.5rem;
}

.follower-card.selected .selection-indicator {
  color: #c23934;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #706e6b;
}

.empty-state h3 {
  margin: 1rem 0 0.5rem 0;
  font-size: 1.125rem;
  color: #181818;
}

.empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

.search-input {
  margin-bottom: 1rem;
}

.action-button {
  min-width: 150px;
}

.slds-spinner_container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9999;
}
