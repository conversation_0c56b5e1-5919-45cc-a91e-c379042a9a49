/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date		:  1 May 2024

   @ Developer Notes    :   @isTest
                            private class YourTestClass {
                                @isTest
                                static void testYourMethod() {
                                    // Create the standard pricebook
                                    Pricebook2 standardPb = PricebookTestUtils.createStandardPricebook();
                                    
                                    // Create a custom pricebook
                                    Pricebook2 customPb = PricebookTestUtils.createCustomPricebook('Test Pricebook');
                                    
                                    // Create a product
                                    Product2 prod = PricebookTestUtils.createProduct('Test Product');
                                    
                                    // Create standard price for the product
                                    PricebookEntry standardPbe = PricebookTestUtils.createStandardPricebookEntry(prod, 100.00);
                                    
                                    // Create custom price for the product
                                    PricebookEntry customPbe = PricebookTestUtils.createCustomPricebookEntry(customPb, prod, 90.00);
                                    
                                    // Now you can use these records in your test
                                    // ... rest of your test code
                                }
                            }

   @ Description	:  A generic class containing reuseable methods for generation of Pricebook related Test Data
   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 1 May 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public class PricebookTestUtils {
  /**
   * Creates a standard Pricebook2 record for testing
   * @return Pricebook2 The standard pricebook
   */
  public static Pricebook2 createStandardPricebook() {
    // Query for existing standard pricebook first
    List<Pricebook2> standardPbs = [
      SELECT Id
      FROM Pricebook2
      WHERE IsStandard = TRUE
      LIMIT 1
    ];
    if (!standardPbs.isEmpty()) {
      return standardPbs[0];
    }

    // Create new standard pricebook if it doesn't exist
    Pricebook2 standardPb = new Pricebook2(
      Id = Test.getStandardPricebookId(),
      Name = 'Standard Price Book',
      IsActive = true
    );
    update standardPb;
    return standardPb;
  }

  /**
   * Creates a custom Pricebook2 record for testing
   * @param name The name of the custom pricebook
   * @return Pricebook2 The custom pricebook
   */
  public static Pricebook2 createCustomPricebook(String name) {
    Pricebook2 customPb = new Pricebook2(Name = name, IsActive = true);
    insert customPb;
    return customPb;
  }

  /**
   * Creates a Product2 record for testing
   * @param name The name of the product
   * @return Product2 The created product
   */
  public static Product2 createProduct(String name) {
    Product2 prod = new Product2(
      Name = name,
      IsActive = true,
      CanUseQuantitySchedule = true,
      ProductCode = name + '_Code'
    );
    insert prod;
    return prod;
  }

  /**
   * Creates a standard PricebookEntry for testing
   * @param product The Product2 record
   * @param unitPrice The unit price for the entry
   * @return PricebookEntry The standard pricebook entry
   */
  public static PricebookEntry createStandardPricebookEntry(
    Product2 product,
    Decimal unitPrice
  ) {
    PricebookEntry standardPbe = new PricebookEntry(
      Pricebook2Id = Test.getStandardPricebookId(),
      Product2Id = product.Id,
      UnitPrice = unitPrice,
      IsActive = true
    );
    insert standardPbe;
    return standardPbe;
  }

  /**
   * Creates a custom PricebookEntry for testing
   * @param pricebook The custom Pricebook2 record
   * @param product The Product2 record
   * @param unitPrice The unit price for the entry
   * @return PricebookEntry The custom pricebook entry
   */
  public static PricebookEntry createCustomPricebookEntry(
    Pricebook2 pricebook,
    Product2 product,
    Decimal unitPrice
  ) {
    PricebookEntry customPbe = new PricebookEntry(
      Pricebook2Id = pricebook.Id,
      Product2Id = product.Id,
      UnitPrice = unitPrice,
      IsActive = true
    );
    insert customPbe;
    return customPbe;
  }
}