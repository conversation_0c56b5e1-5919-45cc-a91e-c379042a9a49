/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  31 May 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                        there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                        to achieve with a selector layer.

   @ Developer Notes   :    Set<Id> eventIds = new Set<Id>();
                            eventIds.add('001O300000FGc3tIAD');
                            eventIds.add('001O300000FGc3tIZZ');
                            EventSelector eventSelector = new EventSelector();
                            List<Event> events = eventSelector.selectEventsByIds(eventIds);
                            List<Event> events = new EventSelector().selectEventsByIds(eventIds);

   @ Github Repo	:   https://github.com/mitchspano/apex-trigger-actions-framework
                        https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 04 April 2025
   @ Last Modified Reason  : Creation

********************************************************************************************/

public inherited sharing class EventSelector {
  private String query;
  private String fromObject = ' FROM Event ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public EventSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your account queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, WhoId, WhatId, Description, Subject';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('EventSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your events by a set of event ids
  public List<Event> selectEventsByIds(Set<Id> eventIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id IN :eventIds' + this.queryLimit;
    //system.debug('selectEventsByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Would continue to build queries and setters for everything you theoretically need.
}