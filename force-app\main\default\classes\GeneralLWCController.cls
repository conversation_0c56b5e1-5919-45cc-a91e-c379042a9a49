/********************************************************************************************

   @ Func Area	:  LWC Controller support

   @ Author	:  <PERSON>

   @ Date	:  30 September 2024

   @ Description	:   This class is responsible for supporting various LWCs 

   @ Developer Notes   :

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                        https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 30 September 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public without sharing class GeneralLWCController {
  //Dependent Class variables
  @TestVisible
  private static AccountService accountService = new AccountService(); //Mock AccountService Class

  @AuraEnabled(cacheable=true)
  public static List<Account> getChildAccounts(Id parentId) {
    List<Account> childAccounts = new List<Account>();
    childAccounts = accountService.getChildAccounts(new Set<Id>{ parentId });

    return childAccounts.isEmpty() ? null : childAccounts;
  }
}