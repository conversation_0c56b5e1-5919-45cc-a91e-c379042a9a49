<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <isExposed>true</isExposed>
    <masterLabel>Flow Flex Cards</masterLabel>
    <targets>
        <target>lightning__FlowScreen</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__FlowScreen" category="Input"
            configurationEditor="c-fsc_flexcard-c-p-e">
            <property name="value" label="Value" type="String"
                description="The selected cards recordId if Allow Multi-Select is set to false" />
            <property name="selectedLabel" label="Selected Label" type="String" role="outputOnly"
                description="The selected Label (V1.3+)" />
            <property name="icon" label="Icon" type="String"
                description="Icon name for example standard:account" />
            <propertyType name="T" extends="SObject" label="Flexcard Object API Name"
                description="Specify the API Name of the SObject to use in the Flexcard" />
            <property name="records" label="Card Data Record Collection" type="{T[]}"
                role="inputOnly"
                description="Record Collection variable containing the records to display in the flexcard." />
            <property name="visibleFieldNames" label="Visible Field Names" type="String"
                default="Id" required="true" description="Show which fields?" />
            <property name="visibleFlowNames" label="Visible Flow Names" type="String"
                description="Show which flow?" />
            <property name="cardSize" role="inputOnly" label="Box Size" type="Integer" default="300"
                description="The size of the box in pixels. The box is a square." />
            <property name="cardSizeString" role="inputOnly" label="Box Size" type="String"
                default="300" description="The size of the box in pixels. The box is a square." />
            <property name="isClickable" role="inputOnly" label="isClickable" type="Boolean"
                default="false"
                description="Set as true if you wish to select individual cards for action further downstream in flow default is false" />
            <property name="cb_isClickable" type="String" role="inputOnly" />
            <property name="headerStyle" label="Header Style" type="String"
                description="Add your own style attribute to the card headers ie. background-color:red;" />
            <property name="subheadCSS" label="Subhead CSS" default="" type="String" />
            <property name="Cardcss" label="Card CSS" default="card" type="String" />
            <property name="allowMultiSelect" label="Allow Multi-Select" type="Boolean"
                default="false"
                description="Allow for multiselect of cards when enabled checkboxes appear on cards and adds selected cards to collection" />
            <property name="cb_allowMultiSelect" type="String" role="inputOnly" />
            <property name="selectedRecordIds" label="Selected Record Ids" type="String[]"
                role="outputOnly" description="String Collection of selected Record ID's" />
            <property name="objectAPIName" label="Object API Name" type="String" role="inputOnly"
                default="Account" required="true"
                description="The SObject API Name used to query fields and values must be the same object selected in Flexcard Object API Name" />
            <property name="label" label="Label" type="String" role="inputOnly"
                description="Enter a label for you component" />
            <property name="transitionOnClick" label="Transition on Click" type="Boolean"
                role="inputOnly" default="false"
                description="If marked as true will transition flow to next screen on card click" />
            <property name="cb_transitionOnClick" type="String" role="inputOnly" />
            <property name="fields" label="Visible Field Names" type="String" default="Id"
                required="true" description="Show which fields?" />
            <property name="flows" label="Visible Flow Names" type="String"
                description="Show which fields?" />
            <property name="actionDisplayType" label="Action List Display Type" type="String"
                description="Display actions as hyperlinks in a list or a Menu List" />
            <property name="buttonLabel" label="Action Menu Label" type="String"
                description="Action Menu Button Label" />
            <property name="allowAllObjects" label="Show All Fields" type="String"
                description="When checked will display all sObject Types" role="inputOnly" />
            <property name="contentStyle" label="Content Style" type="String" role="inputOnly"
                description="Add your own styel attribute to the card body content ie. font-weight:bold;" />
            <property name="cardHeight" type="String" role="inputOnly" />
            <property name="cardWidth" type="String" role="inputOnly" />
            <property name="fieldVariant" type="String" role="inputOnly" />
            <property name="fieldClass" type="String" role="inputOnly" />
            <property name="headerField" type="String" role="inputOnly" />
            <property name="headerFieldClass" type="String" role="inputOnly" />
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>