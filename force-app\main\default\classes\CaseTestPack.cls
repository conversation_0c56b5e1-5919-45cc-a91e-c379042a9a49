/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  06 May 2024

   @ Description	:   A test class containing test methods for apex automation on the Case Object

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   TA_Case_Queries
                        TA_Case_FastUpdates

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 06 May 2024
   @ Last Modified Reason  : Creation

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 21 January 2025
   @ Last Modified Reason  : Added TA_Case_SetWebEmail method

********************************************************************************************/
@isTest
public class CaseTestPack {
  //Test CaseService class
  @isTest
  static void test_CaseService() {
    Test.startTest();
    // Given
    Id validCaseId = MockerUtils.generateId(Case.SObjectType);
    List<Id> accountIds = new List<Id>{
      MockerUtils.generateId(Account.SObjectType),
      MockerUtils.generateId(Account.SObjectType)
    };

    Case expectedCase = (Case) MockerUtils.updateObjectState(
      new Case(Id = validCaseId, Status = 'New'),
      new Map<String, Object>{ 'CaseNumber' => '********' }
    );

    List<Case> casesToInsert = new List<Case>{ expectedCase };

    Mocker mocker = Mocker.startStubbing();

    DmlHelper dmlHelperMock = (DmlHelper) mocker.mock(DmlHelper.class);

    dmlHelperMock.insertObjects(casesToInsert, 'Case Service');
    Mocker.MethodRecorder insertObjectsRec = mocker.when()
      .withAnyValues()
      .getMethodRecorder();

    CaseSelector caseSelectorMock = (CaseSelector) mocker.mock(
      CaseSelector.class
    );

    mocker.when(caseSelectorMock.selectCasesById(new Set<Id>{ validCaseId }))
      .thenReturn(new List<Case>{ expectedCase });

    mocker.when(caseSelectorMock.selectCasesById(null))
      .withAnyValues()
      .thenReturn(new List<Case>());

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    CaseService.caseSelector = caseSelectorMock;
    CaseService.dmlHelper = dmlHelperMock;

    // When 1
    //test method public String getCaseNumber(Id caseId) on CaseService
    String caseNumber = new CaseService().getCaseNumber(validCaseId);

    // Then 1
    System.assertEquals('********', caseNumber);

    // When 2
    //test method public void createCases(List<Id> accountIds) on CaseService
    new CaseService()
      .createCases(casesToInsert, 'CaseTestPack.test_caseService');

    // Then 2
    System.assertEquals(1, insertObjectsRec.getCallsCount());

    Test.stopTest();
  }

  //Test CaseSelector class
  @isTest
  static void test_CaseSelector() {
    CaseSelector caseSelector = new CaseSelector();
    Integer queryLimit = 100;
    Set<Id> caseIds = new Set<Id>{
      MockerUtils.generateId(Case.SObjectType),
      MockerUtils.generateId(Case.SObjectType)
    };
    Set<Id> entitlementIds = new Set<Id>{
      MockerUtils.generateId(Entitlement.SObjectType),
      MockerUtils.generateId(Entitlement.SObjectType)
    };
    Test.startTest();
    caseSelector.setQueryLimit(queryLimit);
    caseSelector.selectCasesById(caseIds);
    caseSelector.selectCasesByEntitlementId(entitlementIds);
    Test.stopTest();
  }

  //Test CaseMilestoneSelector class
  @isTest
  static void test_CaseMilestoneSelector() {
    CaseMilestoneSelector caseMilestoneSelector = new CaseMilestoneSelector();
    Integer queryLimit = 100;
    Set<Id> caseIds = new Set<Id>{
      MockerUtils.generateId(Case.SObjectType),
      MockerUtils.generateId(Case.SObjectType)
    };
    Test.startTest();
    caseMilestoneSelector.setQueryLimit(queryLimit);
    caseMilestoneSelector.selectCaseMilestonesByCaseIds(caseIds);
    caseMilestoneSelector.selectCaseMilestonesByCaseIdsAndMilestoneName(
      caseIds,
      'First Response to Customer'
    );
    Test.stopTest();
  }

  //Test CaseTrigger
  @isTest
  static void test_CaseTrigger() {
    Test.startTest();
    List<Case> newList = new List<Case>();

    newList.add(new Case(Description = 'My updated description of the case'));

    insert newList;
    Test.stopTest();
  }

  @IsTest
  private static void test_TA_Case_Queries_beforeInsert() {
    List<Case> newList = new List<Case>();

    newList.add(new Case(Description = 'My updated description of the case'));

    new TA_Case_Queries.Service().beforeInsert(newList);
  }

  @IsTest
  private static void test_TA_Case_Queries_beforeUpdate() {
    List<Case> newList = new List<Case>();
    List<Case> oldList = new List<Case>();

    //generate fake Id
    Id fakeCaseId = TestFactory.getFakeId(Case.SObjectType);

    newList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My updated description of the case'
      )
    );
    oldList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My initial description of the case'
      )
    );

    new TA_Case_Queries.Service().beforeUpdate(newList, oldList);
  }

  @IsTest
  private static void test_TA_Case_Queries_afterInsert() {
    List<Case> newList = new List<Case>();

    newList.add(
      new Case(
        Id = TestFactory.getFakeId(Case.SObjectType),
        Description = 'My updated description of the case'
      )
    );

    new TA_Case_Queries.Service().afterInsert(newList);
  }

  @IsTest
  private static void test_TA_Case_Queries_afterUpdate() {
    List<Case> newList = new List<Case>();
    List<Case> oldList = new List<Case>();

    //generate fake Id
    Id fakeCaseId = TestFactory.getFakeId(Case.SObjectType);

    newList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My updated description of the case'
      )
    );
    oldList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My initial description of the case'
      )
    );

    new TA_Case_Queries.Service().afterUpdate(newList, oldList);
  }

  @IsTest
  private static void test_TA_Case_Queries_beforeDelete() {
    List<Case> oldList = new List<Case>();

    //generate fake Id
    Id fakeCaseId = TestFactory.getFakeId(Case.SObjectType);

    oldList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My initial description of the case'
      )
    );

    new TA_Case_Queries.Service().beforeDelete(oldList);
  }

  @IsTest
  private static void test_TA_Case_Queries_afterDelete() {
    List<Case> oldList = new List<Case>();

    //generate fake Id
    Id fakeCaseId = TestFactory.getFakeId(Case.SObjectType);

    oldList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My initial description of the case'
      )
    );

    new TA_Case_Queries.Service().afterDelete(oldList);
  }

  @IsTest
  private static void test_TA_Case_FastUpdates_beforeInsert() {
    List<Case> newList = new List<Case>();

    // Setup Test Data
    Id currentUserId = UserInfo.getUserId();
    Id fakeUserId = MockerUtils.generateId(User.SObjectType);
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    String bypassValidationRulePermissionName = 'Bypass_Validation_Rules';

    Map<Id, User> userMap = new Map<Id, User>();

    userMap.put(
      currentUserId,
      new User(Id = currentUserId, Title = 'Mr', Email = '<EMAIL>')
    );

    Map<Id, Boolean> userIdToBypassValidationBooleanMap = new Map<Id, Boolean>();
    userIdToBypassValidationBooleanMap.put(currentUserId, false);

    newList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My new description of the case',
        Type = 'Complaint'
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();
    UserSelector userSelectorMock = (UserSelector) mocker.mock(
      UserSelector.class
    );
    UserService userServiceMock = (UserService) mocker.mock(UserService.class);

    // Mocking getUserDetails return values
    mocker.when(userSelectorMock.getUserDetails(currentUserId))
      .thenReturn(userMap);

    // Mocking getUserHasPermissionSet return values
    mocker.when(
        userServiceMock.buildUserIdToPermissionSetMap(
          currentUserId,
          bypassValidationRulePermissionName
        )
      )
      .thenReturn(userIdToBypassValidationBooleanMap);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Case_Queries.userSelector = userSelectorMock;

    new TA_Case_Queries.Service().beforeInsert(newList);
    new TA_Case_FastUpdates().beforeInsert(newList);

    //Assertions
    System.assertEquals('Level 3', newList[0].Level__c);
  }

  @IsTest
  private static void test_TA_Case_FastUpdates_beforeUpdate() {
    List<Case> newList = new List<Case>();
    List<Case> oldList = new List<Case>();

    // Setup Test Data
    Id currentUserId = UserInfo.getUserId();
    Id fakeUserId = MockerUtils.generateId(User.SObjectType);
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    String bypassValidationRulePermissionName = 'Bypass_Validation_Rules';

    Map<Id, User> userMap = new Map<Id, User>();

    userMap.put(
      currentUserId,
      new User(Id = currentUserId, Title = 'Mr', Email = '<EMAIL>')
    );

    Map<Id, Boolean> userIdToBypassValidationBooleanMap = new Map<Id, Boolean>();
    userIdToBypassValidationBooleanMap.put(currentUserId, false);

    newList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My new description of the case',
        Type = 'Complaint'
      )
    );

    oldList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My old description of the case',
        Type = 'Inquiry'
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();
    UserSelector userSelectorMock = (UserSelector) mocker.mock(
      UserSelector.class
    );
    UserService userServiceMock = (UserService) mocker.mock(UserService.class);

    // Mocking getUserDetails return values
    mocker.when(userSelectorMock.getUserDetails(currentUserId))
      .thenReturn(userMap);

    // Mocking getUserHasPermissionSet return values
    mocker.when(
        userServiceMock.buildUserIdToPermissionSetMap(
          currentUserId,
          bypassValidationRulePermissionName
        )
      )
      .thenReturn(userIdToBypassValidationBooleanMap);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Case_Queries.userSelector = userSelectorMock;

    new TA_Case_Queries.Service().beforeUpdate(newList, oldList);
    new TA_Case_FastUpdates().beforeUpdate(newList, oldList);

    //Assertions
    System.assertEquals('Level 3', newList[0].Level__c);
  }

  @IsTest
  private static void test_TA_Case_FastUpdates_beforeUpdate_SetVerificationDate() {
    List<Case> newList = new List<Case>();
    List<Case> oldList = new List<Case>();

    // Setup Test Data
    Id currentUserId = UserInfo.getUserId();
    Id fakeUserId = MockerUtils.generateId(User.SObjectType);
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    String bypassValidationRulePermissionName = 'Bypass_Validation_Rules';

    Map<Id, User> userMap = new Map<Id, User>();

    userMap.put(
      currentUserId,
      new User(Id = currentUserId, Title = 'Mr', Email = '<EMAIL>')
    );

    Map<Id, Boolean> userIdToBypassValidationBooleanMap = new Map<Id, Boolean>();
    userIdToBypassValidationBooleanMap.put(currentUserId, false);

    Group supportQueueGroup = Utilities.getPublicGroup('Scientific Support');

    newList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My new description of the case',
        Type = 'Complaint',
        Verification_Status__c = 'Verified',
        OwnerId = currentUserId
      )
    );

    oldList.add(
      new Case(
        Id = fakeCaseId,
        Description = 'My old description of the case',
        Type = 'Inquiry',
        Verification_Status__c = null,
        OwnerId = supportQueueGroup.Id
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();
    UserSelector userSelectorMock = (UserSelector) mocker.mock(
      UserSelector.class
    );
    UserService userServiceMock = (UserService) mocker.mock(UserService.class);

    // Mocking getUserDetails return values
    mocker.when(userSelectorMock.getUserDetails(currentUserId))
      .thenReturn(userMap);

    // Mocking getUserHasPermissionSet return values
    mocker.when(
        userServiceMock.buildUserIdToPermissionSetMap(
          currentUserId,
          bypassValidationRulePermissionName
        )
      )
      .thenReturn(userIdToBypassValidationBooleanMap);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Case_Queries.userSelector = userSelectorMock;

    new TA_Case_Queries.Service().beforeUpdate(newList, oldList);
    new TA_Case_FastUpdates().beforeUpdate(newList, oldList);

    //Assertions
    System.assertEquals('Open', newList[0].Status);
  }

  @IsTest
  private static void test_TA_Case_SupportSlaAutomation_afterInsert() {
    List<Case> newList = new List<Case>();

    // Setup Test Data
    Id currentUserId = UserInfo.getUserId();
    Id fakeUserId = MockerUtils.generateId(User.SObjectType);
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    Id fakeAccountId1 = MockerUtils.generateId(Account.SObjectType);
    Id fakeAccountId2 = MockerUtils.generateId(Account.SObjectType);
    Id fakeEntitlementId1 = MockerUtils.generateId(Entitlement.SObjectType);
    Id fakeEntitlementId2 = MockerUtils.generateId(Entitlement.SObjectType);
    String bypassValidationRulePermissionName = 'Bypass_Validation_Rules';
    Set<Id> accountIds = new Set<Id>();

    accountIds.add(fakeAccountId1);
    accountIds.add(fakeAccountId2);

    List<Entitlement> fakeEntitlementList = new List<Entitlement>();

    Entitlement fakeEntitlementRecord1 = (Entitlement) MockerUtils.updateObjectState(
      new Entitlement(
        Id = fakeEntitlementId1,
        Name = 'Key Account',
        AccountId = fakeAccountId1,
        Type = 'Phone Support'
      ),
      new Map<String, Object>{ 'Status' => 'Active' }
    );

    Entitlement fakeEntitlementRecord2 = (Entitlement) MockerUtils.updateObjectState(
      new Entitlement(
        Id = fakeEntitlementId2,
        Name = 'Key Account',
        AccountId = fakeAccountId1,
        Type = 'Phone Support'
      ),
      new Map<String, Object>{ 'Status' => 'Active' }
    );

    fakeEntitlementList.add(fakeEntitlementRecord1);
    fakeEntitlementList.add(fakeEntitlementRecord2);

    Map<Id, User> userMap = new Map<Id, User>();

    userMap.put(
      currentUserId,
      new User(Id = currentUserId, Title = 'Mr', Email = '<EMAIL>')
    );

    Map<Id, Boolean> userIdToBypassValidationBooleanMap = new Map<Id, Boolean>();
    userIdToBypassValidationBooleanMap.put(currentUserId, false);

    newList.add(
      new Case(
        Id = fakeCaseId,
        AccountId = fakeAccountId1,
        Description = 'My new description of the case',
        Type = 'Complaint'
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    EntitlementSelector entitlementSelectorMock = (EntitlementSelector) mocker.mock(
      EntitlementSelector.class
    );

    mocker.when(
        entitlementSelectorMock.selectEntitlementsByAccountId(accountIds)
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeEntitlementList);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Case_SupportSlaAutomation.entitlementSelector = entitlementSelectorMock;

    new TA_Case_SupportSlaAutomation().afterInsert(newList);

    //Assertions
    //System.assertEquals('Level 3', newList[0].Level__c);
  }

  @IsTest
  private static void test_TA_Case_MilestoneProcessing_afterUpdate() {
    // Setup Test Data
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    Id fakeAccountId1 = MockerUtils.generateId(Account.SObjectType);
    Id fakeCaseMilestoneId = MockerUtils.generateId(CaseMilestone.SObjectType);

    Set<Id> caseIds = new Set<Id>();
    caseIds.add(fakeCaseId);

    List<CaseMilestone> fakeCaseMilestoneList = new List<CaseMilestone>();

    List<Case> newList = new List<Case>();
    List<Case> oldList = new List<Case>();

    newList.add(
      new Case(
        Id = fakeCaseId,
        Status = 'Closed',
        Description = 'My updated description of the case'
      )
    );
    oldList.add(
      new Case(
        Id = fakeCaseId,
        Status = 'Open',
        Description = 'My initial description of the case'
      )
    );

    fakeCaseMilestoneList.add(
      new CaseMilestone(Id = fakeCaseMilestoneId, CompletionDate = null)
    );

    newList.add(
      new Case(
        Id = fakeCaseId,
        AccountId = fakeAccountId1,
        Description = 'My new description of the case',
        Type = 'Complaint'
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    CaseMilestoneSelector caseMilestoneSelectorMock = (CaseMilestoneSelector) mocker.mock(
      CaseMilestoneSelector.class
    );

    mocker.when(
        caseMilestoneSelectorMock.selectCaseMilestonesByCaseIdsAndMilestoneName(
          caseIds,
          'First Response to Customer'
        )
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeCaseMilestoneList);

    DmlHelper dmlHelperMock = (DmlHelper) mocker.mock(DmlHelper.class);

    dmlHelperMock.updateObjects(fakeCaseMilestoneList, 'Case Service');
    Mocker.MethodRecorder updateObjectsRec = mocker.when()
      .withAnyValues()
      .getMethodRecorder();

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    CaseService.caseMilestoneSelector = caseMilestoneSelectorMock;
    CaseService.dmlHelper = dmlHelperMock;

    new TA_Case_MilestoneProcessing().afterUpdate(newList, oldList);
  }

  @IsTest
  private static void test_TA_Case_GenerateThreadToken() {
    // Setup Test Data
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    Id fakeAccountId1 = MockerUtils.generateId(Account.SObjectType);
    String threadToken = 'thread::GB1qLMw7LlCKkCEuSX8zzgI::';

    List<Case> newList = new List<Case>();

    newList.add(
      new Case(
        Id = fakeCaseId,
        AccountId = fakeAccountId1,
        Status = 'New',
        Description = 'My thread token test'
      )
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    DmlHelper dmlHelperMock = (DmlHelper) mocker.mock(DmlHelper.class);

    dmlHelperMock.updateObjects(newList, 'Case Service');
    Mocker.MethodRecorder updateObjectsRec = mocker.when()
      .withAnyValues()
      .getMethodRecorder();

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one

    CaseService.dmlHelper = dmlHelperMock;

    new TA_Case_GenerateThreadToken().afterInsert(newList);
  }

  @IsTest
  private static void test_TA_Case_AddCaseTeam_afterUpdate() {
    // Setup Test Data
    Id fakeCaseId1 = MockerUtils.generateId(Case.SObjectType);
    Id fakeCaseId2 = MockerUtils.generateId(Case.SObjectType);
    Id fakeCaseId3 = MockerUtils.generateId(Case.SObjectType);
    Id fakeAccountId1 = MockerUtils.generateId(Account.SObjectType);
    //Id fakeCaseTeamTemplateId = MockerUtils.generateId(CaseTeamTemplate.SObjectType);

    Set<Id> caseIds = new Set<Id>();
    caseIds.add(fakeCaseId1);
    caseIds.add(fakeCaseId2);
    caseIds.add(fakeCaseId3);

    List<CaseTeamTemplate> fakeCaseTeamTemplateList = new List<CaseTeamTemplate>();
    fakeCaseTeamTemplateList.add(
      new CaseTeamTemplate(
        //Id = fakeCaseTeamTemplateId,
        Description = 'The Logistics Team will assist on certain cases when there is a complaint around Logistics.',
        Name = 'Logistics Team'
      )
    );

    List<Case> newList = new List<Case>();
    List<Case> oldList = new List<Case>();

    newList.add(
      new Case(
        Id = fakeCaseId1,
        Status = 'Open',
        Type = 'Inquiry',
        Inquiry_Type__c = 'Logistics'
      )
    );
    oldList.add(new Case(Id = fakeCaseId1, Status = 'Open', Type = ''));

    newList.add(
      new Case(
        Id = fakeCaseId2,
        Status = 'Open',
        Type = 'Complaint',
        Complaint_Type__c = 'Logistics'
      )
    );
    oldList.add(new Case(Id = fakeCaseId2, Status = 'Open', Type = ''));

    newList.add(
      new Case(
        Id = fakeCaseId3,
        Status = 'Open',
        Type = 'Complaint',
        Complaint_Type__c = 'Product',
        Product_Related_Complaint_Type__c = 'Kitting/Packaging'
      )
    );
    oldList.add(new Case(Id = fakeCaseId3, Status = 'Open', Type = ''));

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    //CaseMilestoneSelector caseMilestoneSelectorMock = (CaseMilestoneSelector) mocker.mock(CaseMilestoneSelector.class);
    CaseTeamTemplateSelector caseTeamTemplateSelectorMock = (CaseTeamTemplateSelector) mocker.mock(
      CaseTeamTemplateSelector.class
    );

    mocker.when(
        caseTeamTemplateSelectorMock.selectCaseTeamTemplatesByName(
          'Logistics Team'
        )
      ) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeCaseTeamTemplateList);

    // Going to the execution phase
    mocker.stopStubbing();

    TA_Case_AddCaseTeam.caseTeamTemplateSelector = caseTeamTemplateSelectorMock;

    new TA_Case_AddCaseTeam().afterUpdate(newList, oldList);
  }

  //Test test_CaseTeamTemplateRecordSelector class
  @isTest
  static void test_CaseTeamTemplateRecordSelector() {
    CaseTeamTemplateRecordSelector caseTeamTemplateRecordSelector = new CaseTeamTemplateRecordSelector();
    Integer queryLimit = 100;
    Test.startTest();
    caseTeamTemplateRecordSelector.setQueryLimit(queryLimit);
    caseTeamTemplateRecordSelector.selectCaseTeamTemplateRecordByCaseId(
      MockerUtils.generateId(Case.SObjectType)
    );
    Test.stopTest();
  }

  //Test CaseTeamTemplateSelector class
  @isTest
  static void test_CaseTeamTemplateSelector() {
    CaseTeamTemplateSelector caseTeamTemplateSelector = new CaseTeamTemplateSelector();
    Integer queryLimit = 100;
    Test.startTest();
    caseTeamTemplateSelector.setQueryLimit(queryLimit);
    caseTeamTemplateSelector.selectCaseTeamTemplatesByName('Logistics Team');
    Test.stopTest();
  }

  @IsTest
  private static void TA_Case_SetWebEmail() {
    List<Case> fakeCaseList = new List<Case>();
    List<User> fakeUserList = new List<User>();

    Set<String> itUserName = new Set<String>{ 'IT Technical' };
    Set<Id> caseIds = new Set<Id>();
    Set<Id> accountIds = new Set<Id>();
    User UserRecord1;

    UserSelector userSelector = new UserSelector(); //Mock Selector Class

    List<user> itTechnicalUser = userSelector.selectUsersByName(itUserName);

    UserRecord1 = itTechnicalUser[0];

    // Setup Test Data
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    Id fakeAccountId1 = MockerUtils.generateId(Account.SObjectType);
    Id fakeUserId1 = MockerUtils.generateId(User.SObjectType);

    User fakeUserRecord1 = (User) MockerUtils.updateObjectState(
      new User(Id = UserRecord1.Id),
      new Map<String, Object>{ 'Name' => 'IT Technical' }
    );

    fakeUserList.add(fakeUserRecord1);

    Mocker mocker = Mocker.startStubbing();

    UserSelector userSelectorMock = (UserSelector) mocker.mock(
      UserSelector.class
    );

    mocker.when(userSelectorMock.selectUsersByName(itUserName)) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeUserList);

    // Going to the execution phase
    mocker.stopStubbing();

    System.runAs(UserRecord1) {
      // The following code runs as user 'u'
      System.debug('Current User: ' + UserInfo.getUserName());
      System.debug('Current Profile: ' + UserInfo.getProfileId());

      caseIds.add(fakeCaseId);
      accountIds.add(fakeAccountId1);

      List<Case> newList = new List<Case>();

      newList.add(
        new Case(
          Id = fakeCaseId,
          Status = 'New',
          subject = 'New submission from Technical Support',
          Description = 'First Name FakeName Last Name FakeName Institution FakeInstitution Phone ******** Email <EMAIL> Country United States State/Province Alabama Type <NAME_EMAIL>',
          AccountId = fakeAccountId1,
          OwnerId = UserRecord1.Id
        )
      );

      new TA_Case_SetWebEmail().beforeInsert(newList);
    }
  }

  @IsTest
  private static void TA_Case_CaseTransferred_afterUpdate() {
    List<Case> fakeCaseList = new List<Case>();
    List<User> fakeUserList = new List<User>();
    Set<Id> caseIds = new Set<Id>();
    Set<Id> userIds = new Set<Id>();

    // Setup Test Data
    Id fakeEmailId = MockerUtils.generateId(EmailMessage.SObjectType);
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    Id fakeAccountId1 = MockerUtils.generateId(Account.SObjectType);
    Id newfakeUserId = MockerUtils.generateId(User.SObjectType);
    Id oldfakeUserId = MockerUtils.generateId(User.SObjectType);

    caseIds.add(fakeCaseId);
    userIds.add(newfakeUserId);

    List<Case> newList = new List<Case>();
    List<Case> oldList = new List<Case>();

    newList.add(
      new Case(
        Id = fakeCaseId,
        Status = 'Closed',
        Description = 'My updated description of the case',
        OwnerId = newfakeUserId
      )
    );
    oldList.add(
      new Case(
        Id = fakeCaseId,
        Status = 'Open',
        Description = 'My initial description of the case',
        OwnerId = oldfakeUserId
      )
    );

    //Prep a list of records to be returned from fake selector class for processing
    fakeUserList.add(
      new User(
        Id = newfakeUserId,
        FirstName = 'Joe',
        LastName = 'Bloggs',
        email = '<EMAIL>'
      )
    );
    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    UserSelector userSelectorMock = (UserSelector) mocker.mock(
      UserSelector.class
    );

    mocker.when(userSelectorMock.selectUsersById(userIds)) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeUserList);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_Case_CaseTransferred.userSelector = userSelectorMock;

    new TA_Case_CaseTransferred().afterUpdate(newList, oldList);
  }
}