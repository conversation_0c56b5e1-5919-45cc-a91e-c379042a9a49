@isTest
global class GetFirstTest {

    @isTest
    static void GetFirstReturnsFirstResult() {


        /* Account targetMember = FixtureData.OutputRecord1();

        List<GetFirst.Requests> testRequestList = new List<GetFirst.Requests>();
        GetFirst.Requests testRequest = new GetFirst.Requests();
        testRequest.inputCollection = FixtureData.InputCollection1();
        testRequestList.add(testRequest);

        System.debug('testRequestList: ' + testRequestList);
        List<GetFirst.Results> resultList = GetFirst.execute(testRequestList);
        System.assertEquals(resultList[0].outputMember.name, targetMember.name, 'GetFirst names do not match');
 */
    }
   
}