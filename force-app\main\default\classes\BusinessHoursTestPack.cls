/********************************************************************************************

@ Func Area	:  Apex development testing

@ Author	:  <PERSON>

@ Date		:  28 November 2024

@ Description	:   A test class containing test methods for apex automation on the Entitlement Object

@ SFDC Documentation :   Test Best Practices to be followed:
https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
Assertion Class
https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

@ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

@ Class Tested	:   businessHoursSelector

@ Last Modified By  : <PERSON>
@ Last Modified On  : 28 November 2024
@ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public class BusinessHoursTestPack {
    //Test BusinessHoursSelector class
    @isTest
    static void test_BusinessHoursSelector() {
        BusinessHoursSelector businessHoursSelector = new BusinessHoursSelector();
        Integer queryLimit = 100;
        Set<Id> businessHoursIds = new Set<Id> {MockerUtils.generateId(BusinessHours.SObjectType), MockerUtils.generateId(BusinessHours.SObjectType)};

        Test.startTest();
        businessHoursSelector.setQueryLimit(queryLimit);
        businessHoursSelector.selectBusinessHourssByIds(businessHoursIds);
        businessHoursSelector.selectAllBusinessHours();
        Test.stopTest();
    }
}