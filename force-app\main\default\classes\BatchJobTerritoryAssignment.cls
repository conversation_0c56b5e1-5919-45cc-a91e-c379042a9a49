/********************************************************************************************

   @ Func Area	:  Apex <PERSON>ch Job

   @ Author	:  <PERSON>

   @ Date		:  08 August 2024

   @ Description	:     Batch Job (2/3)
                        This Batch job will query for all accounts and then create a unique matching ket by concatenating the following fields
                        SalesChannel__c, SalesTeam__c, ShippingCountry, ShippingCountryCode, ShippingState, ShippingStateCode, ShippingPostalCode,

                        The same process will occur against the Custom Object SalesTerritories__c which has all the territory assingment data used to create the Account unique matching key above.

                        If a match is found then the Account will be updated with a lookup relationship to the SalesTerritories__c record

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

                            How to execute via Developer Console:

                            // Define your parameters
                            String query = 'SELECT Id, SalesChannel__c, SalesTeam__c, ShippingCountry, ShippingCountryCode, ShippingState, ShippingStateCode, ShippingPostalCode, SalesTerritories__c FROM Account';

                            // Instantiate and execute the batch job
                            BatchJobTerritoryAssignment batchJob = new BatchJobTerritoryAssignment(query);
                            ID batchProcessId = Database.executeBatch(batchJob, 200);

                            // Log the batch job ID
                            System.debug('Batch job started with ID: ' + batchProcessId);

   @ Test Class	:   BatchJobTerritoryAssignmentTest

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 08 August 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/

global class BatchJobTerritoryAssignment implements Database.Batchable<sObject>, Database.Stateful {
  private String query;
  private String objectName;
  // Class-level variable to store information across batches
  private List<Account> accsToBeProcessed = new List<Account>();
  private Integer totalRecordsProcessed = 0;

  global BatchJobTerritoryAssignment(String query) {
    this.query = query;
  }

  global Database.QueryLocator start(Database.BatchableContext BC) {
    return Database.getQueryLocator(query);
  }

  global void execute(Database.BatchableContext BC, List<sObject> scope) {
    List<SObject> sObjectRecsToBeUpdated = new List<SObject>();
    Schema.SObjectType targetType;

    for (sObject record : scope) {
      Account acc = (Account) record;
      accsToBeProcessed.add(acc);
    }

    if (accsToBeProcessed.size() > 0) {
      totalRecordsProcessed = accsToBeProcessed.size();
      TA_Account_TerritoryAssignment accountTerritoryAssignmentInstance = new TA_Account_TerritoryAssignment();
      accountTerritoryAssignmentInstance.getSalesTerritories(accsToBeProcessed);
    }
  }

  global void finish(Database.BatchableContext BC) {
    // Prepare email content
    String emailBody = 'Batch Job: BatchJobTerritoryAssignment has completed.\n\n';
    emailBody += 'Total records processed: ' + totalRecordsProcessed + '\n';

    // Send email with attachment
    Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();

    System_Configuration__mdt specificRecord = [
      SELECT BatchJobNotifications__c
      FROM System_Configuration__mdt
      WHERE DeveloperName = 'GlobalSystemConfig'
      LIMIT 1
    ];

    List<String> toAddresses = specificRecord.BatchJobNotifications__c.split(
      ','
    );
    mail.setToAddresses(toAddresses);
    mail.setSubject('Batch Job BatchJobTerritoryAssignment Complete (2/3)');
    mail.setPlainTextBody(emailBody);

    Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{ mail });

    //Now Chain the third Batch Job

    //Last version of query
    /*
    String query =
      'SELECT Id, SalesChannel__c, SalesTeam__c, ' +
      'ShippingCountry, ShippingCountryCode, ShippingState, ' +
      'ShippingStateCode, ShippingPostalCode, SalesTerritories__c, ' +
      'SalesTerritoryMatchingKey__c, RecommendedSalesTerritory__c ' +
      'FROM Account ' +
      'WHERE ChildCount__c = 0 ' +
      'AND IsExcludedFromRealign = FALSE ' +
      'AND SalesTeam__c != \'CGS\'';
      */

    //New version of query
    String query =
      'SELECT Id, Name, SalesChannel__c, SalesTeam__c, ' +
      'ShippingCountry, ShippingCountryCode, ShippingState, ' +
      'ShippingStateCode, ShippingPostalCode, SalesTerritories__c, ' +
      'SalesTerritoryMatchingKey__c, RecommendedSalesTerritory__c ' +
      'FROM Account ' +
      'WHERE IsExcludedFromRealign = FALSE ' +
      'AND (ChildCount__c = 0 ' +
      'OR ChildCount__c = null)';

    //Kick off 3rd and final  Batch Job.
    BatchJobTerritoryAssociation thirdBatch = new BatchJobTerritoryAssociation(
      query
    );
    ID batchJobId = Database.executeBatch(thirdBatch, 200);

    /*
    // Log the batch job ID
    System.debug(
      'Batch Job BatchJobTerritoryAssociation (3/3) started with ID: ' +
      batchJobId
    );
    */
  }
}