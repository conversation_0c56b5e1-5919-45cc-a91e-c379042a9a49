/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  14 August 2024

   @ Description	:   This is an extremely simple example of how you might implement a selector class
                   there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
                   to achieve with a selector layer.

   @ Developer Notes   :   Set<Id> accountIds = new Set<Id>();
                       accountIds.add('001O300000FGc3tIAD');
                       accountIds.add('001O300000FGc3tIZZ');
                       AccountSelector accountSelector = new AccountSelector();
                       List<Account> accounts = accountSelector.selectAccountsByIds(accountIds);
                       List<Account> accounts = new AccountSelector().selectAccountsByIds(accountIds);

   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                 https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master

   @ Last Modified By  : <PERSON>ck<PERSON>
   @ Last Modified On  : 14 August 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public inherited sharing class SalesTerritoriesSelector {
  private String query;
  private String fromObject = ' FROM SalesTerritories__c ';
  private String queryLimit = ' LIMIT 3000';

  //Constructor to setup the base query
  public SalesTerritoriesSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your account queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Name, Territory__c, SalesPerson__c, SalesPerson__r.Name, SalesChannel__c, SalesTeam__c, ShippingCountry__c, ShippingCountryCode__c, ShippingState__c, ShippingStateCode__c, ShippingZipPostalCode__c, AccountKey__c';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('SalesTerritoriesSelector.setQueryLimit() new queryLimit -> ' + this.queryLimit);
  }

  //Select your SalesTerritories by a set of ids
  public List<SalesTerritories__c> selectSalesTerritoriesByIds(Set<Id> salesTerritoriesIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id IN :salesTerritoriesIds' + this.queryLimit;
    //system.debug('selectSalesTerritoriesByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select all SalesTerritories
  public List<SalesTerritories__c> selectAllSalesTerritories() {
    buildBaseQuery();
    this.query += fromObject + this.queryLimit;
    //system.debug('selectSalesTerritoriesByIds() this.query -> ' + this.query);
    return Database.query(this.query);
  }
}