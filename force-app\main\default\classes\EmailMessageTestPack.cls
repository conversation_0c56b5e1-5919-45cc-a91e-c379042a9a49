/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  06 May 2024

   @ Description	:   A test class containing test methods for apex automation on the EmailMessage Object

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   TA_EmailMessage_UnreadEmail
                        EmailManager

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 06 May 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public class EmailMessageTestPack {
  @isTest
  static void test_emailMessageTrigger() {
    Test.startTest();
    //Mock a case and avoid a DML insert
    Case mockedCase = new Case(UnreadEmail__c = false, LastEmailReceived__c = null);

    insert mockedCase;

    List<EmailMessage> newList = new List<EmailMessage>();

    newList.add(new EmailMessage(Incoming = true, ParentId = mockedCase.Id));

    insert newList;
    Test.stopTest();
  }

  @IsTest
  static void test_TA_EmailMessage_UnreadEmail_afterInsert() {
    List<EmailMessage> newListEmailMessage = new List<EmailMessage>();
    List<Case> fakeCaseList = new List<Case>();
    Set<Id> caseIds = new Set<Id>();

    // Setup Test Data
    Id fakeEmailId = MockerUtils.generateId(EmailMessage.SObjectType);
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    Id fakeAccountId1 = MockerUtils.generateId(Account.SObjectType);

    caseIds.add(fakeCaseId);

    //Prep a list of records to be passed to the Trigger Action for processing
    newListEmailMessage.add(new EmailMessage(Id = fakeEmailId, Incoming = true, ParentId = fakeCaseId));

    //Prep a list of records to be returned from fake selector class for processing
    fakeCaseList.add(new Case(Id = fakeCaseId, AccountId = fakeAccountId1, Description = 'My new description of the case', Type = 'Complaint'));

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    CaseSelector caseSelectorMock = (CaseSelector) mocker.mock(CaseSelector.class);
    EntitlementSelector entitlementSelectorMock = (EntitlementSelector) mocker.mock(EntitlementSelector.class);

    mocker.when(caseSelectorMock.selectCasesById(caseIds)) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeCaseList);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_EmailMessage_UnreadEmail.caseSelector = caseSelectorMock;

    new TA_EmailMessage_UnreadEmail().afterInsert(newListEmailMessage);

    //Assertions
    //System.assertEquals('Level 3', newList[0].Level__c);
  }

  @IsTest
  static void test_EmailManager_sendMail() {
    EmailManager em = new EmailManager();
    em.sendMail('<EMAIL>', 'Test Email', 'Test Body');
  }

  @IsTest
  static void test_TA_EmailMessage_NotifyCaseOwnersNewEmail_beforeInsert() {
    List<EmailMessage> newListEmailMessage = new List<EmailMessage>();

    // Setup Test Data
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);

    //Prep a list of records to be passed to the Trigger Action for processing
    newListEmailMessage.add(new EmailMessage(Incoming = true, ParentId = fakeCaseId, TextBody = 'This is an test email'));

    new TA_EmailMessage_NotifyCaseOwnersNewEmail().beforeInsert(newListEmailMessage);
  }

  @IsTest
  static void test_TA_EmailMessage_NotifyCaseOwnersNewEmail_afterInsert() {
    List<EmailMessage> newListEmailMessage = new List<EmailMessage>();
    List<Case> fakeCaseList = new List<Case>();
    List<User> fakeUserList = new List<User>();
    Set<Id> caseIds = new Set<Id>();
    Set<Id> userIds = new Set<Id>();

    // Setup Test Data
    Id fakeEmailId = MockerUtils.generateId(EmailMessage.SObjectType);
    Id fakeCaseId = MockerUtils.generateId(Case.SObjectType);
    Id fakeAccountId1 = MockerUtils.generateId(Account.SObjectType);
    Id fakeUserId = MockerUtils.generateId(User.SObjectType);

    caseIds.add(fakeCaseId);
    userIds.add(fakeUserId);

    //Prep a list of records to be passed to the Trigger Action for processing
    newListEmailMessage.add(new EmailMessage(Id = fakeEmailId, Incoming = true, ParentId = fakeCaseId));

    //Prep a list of records to be returned from fake selector class for processing
    fakeCaseList.add(
      new Case(Id = fakeCaseId, OwnerId = fakeUserId, AccountId = fakeAccountId1, Description = 'My new description of the case', Type = 'Complaint')
    );
    //Prep a list of records to be returned from fake selector class for processing
    fakeUserList.add(new User(Id = fakeUserId, FirstName = 'Joe', LastName = 'Bloggs'));
    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    CaseSelector caseSelectorMock = (CaseSelector) mocker.mock(CaseSelector.class);
    EntitlementSelector entitlementSelectorMock = (EntitlementSelector) mocker.mock(EntitlementSelector.class);
    UserSelector userSelectorMock = (UserSelector) mocker.mock(UserSelector.class);

    mocker.when(caseSelectorMock.selectCasesById(caseIds)) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeCaseList);

    mocker.when(userSelectorMock.selectUsersById(userIds)) // It's not necessary to give an actial Set<Id>
      .withAnyValues() // Because the parameter value will be ignored
      .thenReturn(fakeUserList);

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    TA_EmailMessage_NotifyCaseOwnersNewEmail.userSelector = userSelectorMock;

    new TA_EmailMessage_NotifyCaseOwnersNewEmail().afterInsert(newListEmailMessage);

    //Assertions
    //System.assertEquals('Level 3', newList[0].Level__c);
  }
}