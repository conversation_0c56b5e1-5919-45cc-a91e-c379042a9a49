/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  06 February 2025

   @ Description	:   

   @ Developer Notes  : https://ericsplayground.wordpress.com/how-to-use-an-apex-defined-object-with-my-datatable-flow-component/
                        https://github.com/ericrsmith35/Flow-PB-List-View-with-Batch-Delete/blob/master/force-app/main/default/classes/FlowDatatableDescriptor.cls
                        https://rathindradakua.medium.com/how-to-use-apex-defined-collection-variables-in-your-flows-to-retrieve-values-from-server-side-50d96553ef20


   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                    https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master
                    https://bluecanvas.io/blog/how-to-structure-and-manage-your-apex-codebase-for-scale-and-agility

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 06 February 2025   
   @ Last <PERSON>dified Reason  : Creation

********************************************************************************************/
// Apex-Defined Variable Sample Descriptor Class
public with sharing class FlowDatatableProductDescriptor {
  // @AuraEnabled annotation exposes the methods to Lightning Components and Flows
  @AuraEnabled
  public String ProductName;

  @AuraEnabled
  public Id Product2Id;

  @AuraEnabled
  public String ProductCode;

  @AuraEnabled
  public Id PricebookEntryId;

  @AuraEnabled
  public String UOM;

  @AuraEnabled
  public Decimal PackSize;

  @AuraEnabled
  public Decimal ListPrice;

  @AuraEnabled
  public String ListPriceCurrency;

  // Define the structure of the Apex-Defined Variable
  public FlowDatatableProductDescriptor(
    String ProductName,
    Id Product2Id,
    String ProductCode,
    Id PricebookEntryId,
    String UOM,
    Decimal PackSize,
    Decimal ListPrice,
    String ListPriceCurrency
  ) {
    this.ProductName = ProductName;
    this.Product2Id = Product2Id;
    this.ProductCode = ProductCode;
    this.PricebookEntryId = PricebookEntryId;
    this.UOM = UOM;
    this.PackSize = PackSize;
    this.ListPrice = ListPrice;
    this.ListPriceCurrency = ListPriceCurrency;
  }

  // Required no-argument constructor
  public FlowDatatableProductDescriptor() {
  }
}