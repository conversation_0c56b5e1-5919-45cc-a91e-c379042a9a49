<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <masterLabel>Datatable</masterLabel>
    <description>This component allows the user to configure and display a datatable in a Flow screen.</description>
    <apiVersion>62.0</apiVersion>
    <isExposed>true</isExposed>
    <targets>
        <target>lightning__FlowScreen</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__FlowScreen" configurationEditor="c-ers_datatable-C-P-E"> 
            <!-- ================================ New in Summer '20' - Support for Dynamic SObject Selection ================================= -->      

            <propertyType name="T" extends="SObject" label="Datatable Object API Name" description="Select the API Name of the SObject to use in the Datatable"/>
            <property name="tableData" label="_ Datatable Record Collection" type="{T[]}" role="inputOnly" description="Record Collection variable containing the records to display in the datatable."/>
            <property name="preSelectedRows" label="Pre-Selected Rows" type="{T[]}" role="inputOnly" description="Record Collection variable containing the records to show as pre-selected in the datatable."/>
            <property name="outputEditedRows" label="Output Edited Rows" type="{T[]}" role="outputOnly" description="Record Collection variable to contain only the records that were edited in the datatable. 
                - NOTE: To write these edits back to the Object, you will need to do a Record Update in the Flow."/>
            <property name="outputEditedSerializedRows" label="Output Edited (Serialized) Rows" type="String" role="outputOnly" description="Record Collection variable to contain only the records that were edited in the datatable. 
                - NOTE: To write these edits back to the Object, you will need to do a Record Update in the Flow."/>
            <property name="outputSelectedRows" label="Output Selected Rows (Collection)" type="{T[]}" role="outputOnly" description="Record Collection variable to contain only the records that were selected in the datatable. 
                - NOTE: These records may not contain all of the edited values."/>
            <property name="outputSelectedRow" label="Output Selected Row (Object)" type="{T}" role="outputOnly" description="Record Object variable that contains the single record that was selected in the datatable. 
                - NOTE: This is only provided when just a single record is selected."/>
            <property name="outputRemovedRows" label="Output Removed Rows (Collection)" type="{T[]}" role="outputOnly" description="Record Collection variable to contain only the records that were removed from the datatable. 
                - NOTE: These records will contain all saved edited values."/>   
            <property name="outputRemainingRows" label="Output Remaining Rows (Collection)" type="{T[]}" role="outputOnly" description="Record Collection variable to contain all the original records that were not removed from the datatable. 
                - NOTE: These records will contain all saved edited values."/>                   
            <property name="outputActionedRow" label="Output Actioned Row (Object)" type="{T}" role="outputOnly" description="Record Object variable that contains the single record that was processed by the most recent datatable row action. 
                - NOTE: This record will contain all saved edited values."/>   

            <!-- ============== These parameters are used when providing a User Defined object rather than a Salesforce SObject ============== -->  
            <!-- =========== Inputs =========== -->
            <property name="isUserDefinedObject" label="_ (User Defined)  Display User Defined Object?" type="Boolean" default="false" role="inputOnly" description="Set to True if you are providing a User Defined object rather than a Salesforce SObject."/>
            <property name="cb_isUserDefinedObject" type="String" role="inputOnly"/>
            <property name="tableDataString" label="_ Datatable Record String (User Defined)" type="String" role="inputOnly" description="Object Collection string variable containing the records to display in the datatable."/>
            <property name="preSelectedRowsString" label="Pre-Selected Rows (User Defined)" type="String" role="inputOnly" description="Object Collection string variable containing the records to show as pre-selected in the datatable."/>
            
            <property name="serializedRecordData" label="_ (User Defined)  Display Serialized Record?" type="String" default="false" role="inputOnly" description="String variable containing the records. Serialized record data."/>
            <property name="isSerializedRecordData" type="Boolean" role="inputOnly"/>
            <property name="cb_isSerializedRecordData" type="String" role="inputOnly"/>
            <!-- =========== Outputs =========== -->
            <property name="outputSelectedRowsString" label="Output Selected Rows (User Defined)" type="String" role="outputOnly" description="Object Collection string variable to contain only the records that were selected in the datatable. 
                --  NOTE: These records may not contain all of the edited values."/>
            <property name="outputEditedRowsString" label="Output Edited Rows (User Defined)" type="String" role="outputOnly" description="Object Collection string variable to contain only the records that were edited in the datatable."/>
            <property name="outputRemovedRowsString" label="Output Removed Rows (User Defined)" type="String" role="outputOnly" description="Object Collection string variable to contain only the records that were removed from the datatable."/>
            <property name="outputRemainingRowsString" label="Output Remaining Rows (User Defined)" type="String" role="outputOnly" description="Object Collection string variable to contain all the original records that were not removed from the datatable."/>
            <property name="outputActionedRowString" label="Output Actioned Row (User Defined)" type="String" role="outputOnly" description="Object string variable to contain only the record that was processed by the most recent datatable row action."/>
            <!-- ============================================================================================================================- -->  
            
            <!-- Additional Output Parameters -->
            <property name="numberOfRowsSelected" label="Output Number of Selected Records" type="Integer" role="outputOnly" description="Total count of the number of selected records"/>
            <property name="numberOfRowsEdited" label="Output Number of Edited Records" type="Integer" role="outputOnly" description="Total count of the number of edited records"/>
            <property name="numberOfRowsRemoved" label="Output Number of Removed Records" type="Integer" role="outputOnly" description="Total count of the number of removed records"/>
            <property name="selectedRowKeyValue" label="Selected Row Key Field Value" type="String" role="outputOnly" description="The value of the keyField(Id) of the selected row"/>
            <property name="sortedBy" label="sortedBy" type="String"  role="outputOnly"/>
            <property name="sortDirection" label="sortDirection" type="String" role="outputOnly"/>

            <!-- Configuration Mode Only - Inputs/Outputs -->
            <property name="objectName" label="Wizard Only - Selected SObject" type="String"/>
            <property name="wizSObject" label="Wizard Only - Selected SObject" type="String" role="outputOnly"/>
            <property name="wizColumnFields" label="Wizard Only - Column Fields" type="String" role="outputOnly"/>
            <property name="wizColumnAlignments" label="Wizard Only - Column Alignments" type="String" role="outputOnly"/>
            <property name="wizColumnEdits" label="Wizard Only - Column Edits" type="String" role="outputOnly"/>
            <property name="wizColumnFilters" label="Wizard Only - Column Filters" type="String" role="outputOnly"/>
            <property name="wizColumnIcons" label="Wizard Only - Column Icons" type="String" role="outputOnly"/>
            <property name="wizColumnLabels" label="Wizard Only - Column Labels" type="String" role="outputOnly"/>
            <property name="wizColumnWidths" label="Wizard Only - Column Widths" type="String" role="outputOnly"/>
            <property name="wizColumnWraps" label="Wizard Only - Column Wraps" type="String" role="outputOnly"/>
            <property name="wizColumnFlexes" label="Wizard Only - Column Flexes" type="String" role="outputOnly"/>
            <property name="wizColumnCellAttribs" label="Wizard Only - Special Cell Attributes" type="String" role="outputOnly"/>
            <property name="wizColumnTypeAttributes" label="Wizard Only - Special Type Attributes" type="String" role="outputOnly"/>
            <property name="wizColumnOtherAttributes" label="Wizard Only - Special Other Attributes" type="String" role="outputOnly"/>

            <!-- Forcing sort order of parameters with special characters _(1) -(2) .(3) -->  
            <property name="columnFields" label="_ Column Fields" type="String" role="inputOnly" description="REQUIRED: Comma separated list of field API Names to display in the datatable."/>
            <property name="columnAlignments" label="- Column Alignments (Col#:alignment,...)" type="String" role="inputOnly" description="Comma separated list of ColID:Alignment Value (left,center,right).  
                --  NOTE: ColIDs can be either the column number or the field API Name"/>
            <property name="columnEdits" label="- Column Edits (Col#:true,...) or All" type="String" role="inputOnly" description="'All' or a Comma separated list of ColID:true or false  
                --  NOTE: Some data types cannot be edited in a datable (lookup, picklist, location, encrypted, rich text, long text area)
                --  NOTE: ColIDs can be either the column number or the field API Name"/>
            <property name="columnFilters" label="- Column Filters (Col#:true,...) or All" type="String" role="inputOnly" description="'All' or a Comma separated list of ColID:true or false  
                --  NOTE: Some data types cannot be filtered in a datable (location, encrypted)
                --  NOTE: ColIDs can be either the column number or the field API Name"/>
            <property name="columnIcons" label="- Column Icons (Col#:icon,...)" type="String" role="inputOnly" description="Comma separated list of ColID:Icon Identifier  --  EXAMPLE: 1:standard:account (Display the first column with the Account icon)  
                --  NOTE: ColIDs can be either the column number or the field API Name"/>
            <property name="columnLabels" label="- Column Labels (Col#:label,...)" type="String" role="inputOnly" description="Comma separated list of ColID:Label (These are only needed if you want a label that is different from the field's defined label)  
                --  NOTE: ColIDs can be either the column number or the field API Name"/>
            <property name="columnScales" label="- Column Scale Values (Col#:scale,...) (User Defined)" type="String" role="inputOnly" description="Comma separated list of ColID:Scale (The number of digits to display to the right of the decimal point in currency, number and percent fields (default = 0))  
                --  NOTE: ColIDs can be either the column number or the field API Name"/>
            <property name="columnTypes" label="- Column Field Types (Col#:type,...) (User Defined)" type="String" role="inputOnly" description="Comma separated list of ColID:FieldType (boolean, currency, date, datetime, number, email, id, location, percent, phone, time, url, text(default))  
                --  NOTE: ColIDs can be either the column number or the field API Name"/>
            <property name="columnWidths" label="- Column Widths (Col#:width,...)" type="String" role="inputOnly" description="Comma separated list of ColID:Width (in pixels).  
                --  NOTE: ColIDs can be either the column number or the field API Name"/>
            <property name="columnWraps" label="- Column Wraps (Col#:true,...)" type="String" role="inputOnly" description="Comma separated list of ColID:true or false (Default:false)  
                --  NOTE: ColIDs can be either the column number or the field API Name"/>
            <property name="columnFlexes" label="- Column Flexes (Col#:true,...)" type="String" role="inputOnly" description="Comma separated list of ColID:true or false (Default:false)  
                --  NOTE: ColIDs can be either the column number or the field API Name"/>
            <property name="columnCellAttribs" label=". Special: Column CellAttributes (Col#:{name:value,...};...) Use ; as separator" type="String" role="inputOnly" description="EXAMPLE: FancyField__c:{class: 'slds-theme_shade slds-theme_alert-texture', iconName: {fieldName: IconValue__c}, iconPosition: left}"/>
            <property name="columnOtherAttribs" label=". Special: Column Other Attributes (Col#:{name:value,...};...) Use ; as separator" type="String" role="inputOnly" description="EXAMPLE: Description:{wrapText: true, wrapTextMaxLines: 5}"/>
            <property name="columnTypeAttribs" label=". Special: Column TypeAttributes (Col#:{name:value,...};...) Use ; as separator" type="String" role="inputOnly" description="EXAMPLE: DateField__c:{year:'numeric', day:'2-digit', month:'long'}; NumberField__c:{minimumFractionDigits:4}"/>
            <property name="isConfigMode" label="Configuration Mode?" type="Boolean" default="false" role="inputOnly" description="Display the Field API Names and the Column Widths in a box below the table.  This parameter is designed to be used by the setup Flow."/>
            <property name="hideCheckboxColumn" label="Hide Checkbox Column?" type="Boolean" default="false" role="inputOnly" description="Set to True to hide the row selection column.  --  NOTE: The checkbox column will always display when inline editing is enabled."/>
            <property name="cb_hideCheckboxColumn" type="String" role="inputOnly"/>
            <property name="isShowSearchBar" label="Show Search bar" type="Boolean" role="inputOnly"/>
            <property name="cb_isShowSearchBar" type="String" role="inputOnly"/>
            <property name="hideHeaderActions" label="Hide Column Header Actions?" type="Boolean" default="false" role="inputOnly" description="Set to True to hide all column header actions including Sort, Clip Text, Wrap Text + Filter."/>
            <property name="cb_hideHeaderActions" type="String" role="inputOnly"/>
            <property name="hideClearSelectionButton" label="Hide Clear Selection Button?" type="Boolean" default="false" role="inputOnly" description="Set to True to hide the Clear Selection Button that would normally appear on a radio button selection table."/>
            <property name="cb_hideClearSelectionButton" type="String" role="inputOnly"/>
            <property name="showRowNumbers" label="Show Row Numbers" type="Boolean" default="false" role="inputOnly" description="Display a row number column as the first column in the table."/>
            <property name="cb_showRowNumbers" type="String" role="inputOnly"/>
            <property name="showRecordCount" label="Show Record Count in Header" type="Boolean" default="false" role="inputOnly" description="Display the number of records in the table header.  This will match what is shown in a List View header."/>
            <property name="cb_showRecordCount" type="String" role="inputOnly"/>
            <property name="showSelectedCount" label="Show Selected Record Count in Header" type="Boolean" default="false" role="inputOnly" description="Display the number of selected records in the table header."/>
            <property name="cb_showSelectedCount" type="String" role="inputOnly"/>
            <property name="keyField" label="Key Field" type="String" default="Id" role="inputOnly" description="This is normally the Id field, but you can specify a different field if all field values are unique."/>
            <property name="matchCaseOnFilters" label="Match Case on Column Filters?" type="Boolean" default="false" role="inputOnly" description="Set to True is you want to force an exact match on case for column filter values."/>
            <property name="cb_matchCaseOnFilters" type="String" role="inputOnly"/>
            <property name="maxNumberOfRows" label="Maximum Number of Records to Display" type="Integer" role="inputOnly" description="Enter a number here if you want to restrict how many rows will be displayed in the datatable."/>
            <property name="showPagination" type="Boolean" role="inputOnly"/>
            <property name="cb_showPagination" type="String" role="inputOnly"/>
            <property name="recordsPerPage" type="Integer" role="inputOnly"/>
            <property name="showFirstLastButtons" type="Boolean" role="inputOnly"/>
            <property name="cb_showFirstLastButtons" type="String" role="inputOnly"/>
            <property name="isRequired" label="Required?" type="Boolean" default="false" role="inputOnly" description="Require at least 1 row to be selected?"/>
            <property name="cb_isRequired" type="String" role="inputOnly"/>
            <property name="singleRowSelection" label="Single Row Selection (Radio Buttons)?" type="Boolean" default="false" role="inputOnly" description="When set to True, Radio Buttons will be displayed and only a single row can be selected.  
                The default (False) will display Checkboxes and allow multiple records to be selected."/>
            <property name="cb_singleRowSelection" type="String" role="inputOnly"/>
            <property name="suppressBottomBar" label="Suppress Cancel/Save Buttons during Edit Mode?" type="Boolean" default="false" role="inputOnly" description="Cancel/Save buttons will appear by default at the very bottom of the table once a field is edited.  
                When hiding these buttons, field updates will be applied as soon as the user Tabs out or selects a different field."/>
            <property name="cb_suppressBottomBar" type="String" role="inputOnly"/>
            <property name="navigateNextOnSave" label="Navigate to Next Flow Element on Save?" type="Boolean" default="false" role="inputOnly" description="When selecting Save after inline editing, immediately navigate to the next Flow element.  
                This removes the need for the User to select the Next button after saving."/>
            <property name="cb_navigateNextOnSave" type="String" role="inputOnly"/>
            <property name="tableHeight" label="Table Height" type="String" role="inputOnly" description="CSS specification for the height of the datatable (Examples: 30rem, calc(50vh - 100px)  If you leave this blank, the datatable will expand to display all records.)"/>
            <property name="tableBorder" label="Table Border" type="Boolean" default="true" role="inputOnly" description="Display a border around the datatable."/>
            <property name="cb_tableBorder" type="String" role="inputOnly"/>
            <property name="isDisplayHeader" label="Display Table Header?" type="Boolean" default="false" role="inputOnly" description="Display a header above the datatable."/>
            <property name="cb_isDisplayHeader" type="String" role="inputOnly"/>
            <property name="tableIcon" label="Table Icon" type="String" role="inputOnly" description="(Optional) Icon to display on the Table Header. Example: standard:account"/>
            <property name="tableLabel" label="Table Label" type="String" role="inputOnly" description="(Optional) Label to display on the Table Header"/>
            <property name="not_suppressNameFieldLink" label="Show the Link on the Object's 'Name' Field" type="Boolean" default="true" role="inputOnly" description="Show the default behavior of displaying the SObject's 'Name' field as a link to the record"/>
            <property name="cb_not_suppressNameFieldLink" type="String" role="inputOnly"/>
            <property name="openLinkinSameTab" type="Boolean" role="inputOnly"/>
            <property name="cb_openLinkinSameTab" type="String" role="inputOnly"/>
            <property name="suppressNameFieldLink" label="OBSOLETE as of v3.0.10" type="Boolean" default="false" role="inputOnly" description="Suppress the default behavior of displaying the SObject's 'Name' field as a link to the record"/>
            <property name="not_tableBorder" label="OBSOLETE as of v3.0.10" type="Boolean" default="false" role="inputOnly"/>
            <property name="displayAll" type="Boolean" role="inputOnly"/>
            <property name="cb_displayAll" type="String" role="inputOnly"/>
            <property name="recordTypeId" type="String" role="inputOnly"/>
            <property name="allowNoneToBeChosen" type="Boolean" role="inputOnly"/>
            <property name="cb_allowNoneToBeChosen" type="String" role="inputOnly"/>
            <property name="allowOverflow" type="Boolean" role="inputOnly"/>
            <property name="cb_allowOverflow" type="String" role="inputOnly"/>
            <property name="suppressCurrencyConversion" type="Boolean" role="inputOnly"/>
            <property name="cb_suppressCurrencyConversion" type="String" role="inputOnly"/>
            <property name="isCaseInsensitiveSort" type="Boolean" role="inputOnly"/>
            <property name="cb_isCaseInsensitiveSort" type="String" role="inputOnly"/>
            <property name="isRemoveRowAction" type="Boolean" role="inputOnly" default="false" description="Add a Row Action"/>
            <property name="cb_isRemoveRowAction" type="String" role="inputOnly"/>            
            <property name="removeLabel" label="Remove Row Action Label" type="String" role="inputOnly" description="This value will be used as the text that appears when hovering on the Remove Row Action Button (Default: Remove Row)"/>
            <property name="removeIcon" label="Remove Row Action Icon" type="String" role="inputOnly" description="This is the icon that will be used for the Remove Row Action Button (Default: utility:close)"/>
            <property name="removeColor" label="Remove Row Action Icon Color" type="String" role="inputOnly" description="This is the color (Red, Green or Black) for the icon that will be used for the Remove Row Action Button (Default: Red)"/>
            <property name="maxRemovedRows" label="Maximum # of rows that can be removed" type="Integer" role="inputOnly" description="Enter a number here if you want to restrict how many rows can be removed from the datatable (Default: 0 - no limit)"/>
            <property name="removeRowLeftOrRight" label="Row Action Column Location" type="String" role="inputOnly" description="Specify if the Row Action column should be on the Left or the Right (Default: Right)"/>
            <property name="rowActionType" label="Row Action Type" type="String" role="inputOnly" description="Select the type of row action.  Current options are Standard and Remove Row.  A Flow row action will be added in the future."/>
            <property name="rowActionDisplay" label="Row Action Display Type" type="String" role="inputOnly" description="Select how you want the row action to appear.  It can be a clickable Icon or a Button with a Label and an optional Icon"/>            
            <property name="rowActionButtonLabel" label="Row Action Button Label" type="String" role="inputOnly" description="Select the label for the row action button."/>
            <property name="rowActionButtonIcon" label="Row Action Button Icon" type="String" role="inputOnly" description="Select the icon for the row action button (optional)."/>
            <property name="rowActionButtonIconPosition" label="Row Action Button Icon Position" type="String" role="inputOnly" description="Specify if the Row Action Button Icon should be on the Left or the Right of the label (Default: Left)"/>
            <property name="rowActionButtonVariant" label="Row Action Button Variant" type="String" role="inputOnly" description="Select the Row Action Button Variant.  This determines the visual appearance of the button."/>
            <!-- NOTE: Input property Labels and Descriptions are overridden by the values in ers_datatableCPE.js inputValues -->
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>