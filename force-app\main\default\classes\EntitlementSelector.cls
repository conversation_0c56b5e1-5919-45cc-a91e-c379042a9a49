/**
 * <AUTHOR>
 * @date 03/31/2021
 * @description This is an extremely simple example of how you might implement a selector class
 * there is still plenty to be desired here, but this, in a very very simplified manner is effectively what you are trying
 * to achieve with a selector layer.
 *
 *
 * Set<Id> caseIds = new Set<Id>();
   caseIds.add('500O3000008HgXhIAK');
   caseIds.add('500O3000008Hh3xIAC');
   CaseSelector caseSelector = new CaseSelector();
   //List<Case> cases = caseSelector.selectCasesById(caseIds);
   List<Case> cases = new CaseSelector().selectCasesById(caseIds);

   system.debug('cases -> ' + cases);
 */

public inherited sharing class EntitlementSelector {
  private String query;
  private String fromObject = ' FROM Entitlement ';
  private String queryLimit = ' LIMIT 1000';

  //Constructor to setup the base query
  public EntitlementSelector() {
    buildBaseQuery();
  }

  //Put your fields you intend to almost always select with your entitlement queries here
  private void buildBaseQuery() {
    this.query = 'SELECT Id, Name, Type, ServiceContract.Name, ServiceContractId, Status, StartDate, EndDate, SlaProcessId, SlaProcess.Name, AccountId, Account.Name, Account.EntitlementSLA__c, Account.SalesChannel__c';
  }

  //Set the limit for your query you're building
  public void setQueryLimit(Integer passedLimit) {
    String newQueryLimit = String.valueOf(passedLimit);
    this.queryLimit = ' LIMIT ' + newQueryLimit;
    //system.debug('EntitlementSelector.setQueryLimit() new queryLimit ->' + this.queryLimit);
  }

  //Select your entitlements by a set of Entitlement Ids
  public List<Entitlement> selectEntitlementsById(Set<Id> entitlementIds) {
    buildBaseQuery();
    this.query += fromObject + 'WHERE Id IN :entitlementIds' + this.queryLimit;
    //system.debug('EntitlementSelector -> selectEntitlementsById() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Select all entitlements related to a set of Account Ids
  public List<Entitlement> selectEntitlementsByAccountId(Set<Id> accountIds) {
    buildBaseQuery();
    this.query +=
      fromObject +
      'WHERE AccountId IN :accountIds' +
      this.queryLimit;
    //system.debug('EntitlementSelector -> selectEntitlementsByAccountId() this.query -> ' + this.query);
    return Database.query(this.query);
  }

  //Would continue to build queries and setters for everything you theoretically need.
}