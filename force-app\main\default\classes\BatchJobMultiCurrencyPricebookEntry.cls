/********************************************************************************************

   @ Func Area	:  Apex Batch Job

   @ Author	:  <PERSON>

   @ Date		:  09 January 2025

   @ Description	:   This Batch job processes Product2 records and creates corresponding PricebookEntry 
                    records in multiple currencies. It accepts a pricebook name, a set of currency codes, 
                    and their corresponding conversion rates. For each product in the scope, it creates 
                    PricebookEntry records in all specified currencies using the provided conversion rates.
                    
                    The job can be run either as a batch job or scheduled for regular execution. It uses 
                    the MultiCurrencyPricebookEntryCreator class to handle the actual creation of the 
                    pricebook entries.

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

                            How to execute via Developer Console:

                      // Define your parameters
                      String pricebookName = 'Standard Price Book';

                      Set<String> currencies = new Set<String>{ 'USD', 'EUR', 'GBP', 'CNY' };
                          
                          Map<String, Decimal> rates = new Map<String, Decimal>{
                              'USD' => 1.0,
                                  'EUR' => 0.970768,
                                  'GBP' => 0.813705,
                                  'CNY' => 7.332045
                                  };  
                                      
                      String query = 'SELECT Id, Name, MOALUSListPrice__c, CGSPriceBook__c, DTCPriceBook__c FROM Product2 WHERE CreatedDate = TODAY';

                      // Instantiate and execute the batch job
                      BatchJobMultiCurrencyPricebookEntry batchJob = new BatchJobMultiCurrencyPricebookEntry(pricebookName, currencies, rates, query);
                      ID batchProcessId = Database.executeBatch(batchJob, 50);

                      // Log the batch job ID
                      System.debug('Batch job started with ID: ' + batchProcessId);

   @ Test Class	:   BatchJobMultiCurrencyPricebookEntryTest

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 09 January 2025
   @ Last Modified Reason  : Creation

  @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 12 February 2025
   @ Last Modified Reason  : Added the pricebookName parameter to the constructor and start method

********************************************************************************************/

global class BatchJobMultiCurrencyPricebookEntry implements Database.Batchable<sObject>, Schedulable {
  // Class-level variable to store information across batches
  private Set<Id> productIdsToBeProcessed = new Set<Id>();
  private String pricebookName;
  private String query;
  private Set<String> currencies;
  private Map<String, Decimal> rates;

  global BatchJobMultiCurrencyPricebookEntry(
    String pricebookName,
    Set<String> currencies,
    Map<String, Decimal> rates,
    String query
  ) {
    this.pricebookName = pricebookName;
    this.currencies = currencies;
    this.rates = rates;
    this.query = query;
  }

  global Database.QueryLocator start(Database.BatchableContext BC) {
    // Query for all records that are orphaned
    return Database.getQueryLocator(query);
  }

  global void execute(Database.BatchableContext BC, List<Product2> scope) {
    if (!scope.isEmpty()) {
      for (Product2 p : scope) {
        productIdsToBeProcessed.add(p.Id);
      }

      if (productIdsToBeProcessed.size() > 0) {
        MultiCurrencyPricebookEntryCreator multiCurrencyPricebookEntryCreator = new MultiCurrencyPricebookEntryCreator(
          currencies,
          rates,
          pricebookName
        );
        multiCurrencyPricebookEntryCreator.createEntriesForMultipleProducts(
          productIdsToBeProcessed
        );
      }
    }
  }

  global void finish(Database.BatchableContext BC) {
    // Optional: Add any post-processing logic here
  }

  // Schedulable interface method
  global void execute(SchedulableContext SC) {
    Database.executeBatch(
      new BatchJobMultiCurrencyPricebookEntry(
        pricebookName,
        currencies,
        rates,
        query
      )
    );
  }
}