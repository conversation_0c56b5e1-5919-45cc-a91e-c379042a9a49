/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  06 June 2024

   @ Description	:   The Service Layer Pattern is a very powerful tool that can help a team create a strong bias towards reusable code. If this concept is new to your team, the Service Layer Badge on Trailhead is a great place to start.
                        The underlying pattern is old and has a long background in many programming languages (including Java).
                        The recommended approach is to create a single service class for each Salesforce object for which you have code (“AccountService.cls”, “CampaignMemberService.cls”, etc). Within that class you create static methods which implement different bits of business logic. Your main design constraint is
                        to try and make the service methods themselves as reusable as possible. Keep them generic!

   @ Developer Notes  : CaseService caseservice = new CaseService();

                        1) String caseNumber = caseservice.getCaseNumber(caseId);
                        2) String caseNumber = new CaseService().getCaseNumber(caseId);


   @ Github Repo	: https://github.com/mitchspano/apex-trigger-actions-framework
                    https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master
                    https://bluecanvas.io/blog/how-to-structure-and-manage-your-apex-codebase-for-scale-and-agility

   @ Last Modified By  : Kyle Cockcroft
   @ Last Modified On  : 06 June 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
public inherited sharing class CaseService {
    @TestVisible
    private static DmlHelper dmlHelper = new DmlHelper();
    @TestVisible
    private static CaseSelector caseSelector = new CaseSelector();
    @TestVisible
    private static CaseMilestoneSelector caseMilestoneSelector = new CaseMilestoneSelector();

    public String getCaseNumber(Id caseId) {
        List<Case> cases = caseSelector.selectCasesById(new Set<Id> { caseId });
        return cases.isEmpty() ? null : cases[0].CaseNumber;
    }

    public void updateCases(List<Case> casesToUpdate, String Source) {
        for (Case caseToUpdate : casesToUpdate) {
            caseToUpdate.ValidationBypassDateTime__c = System.now();
        }
        dmlHelper.updateObjects(casesToUpdate, Source);
    }

    public void createCases(List<Case> casesToInsert, String Source) {
        for (Case caseToInsert : casesToInsert) {
            caseToInsert.ValidationBypassDateTime__c = System.now();
        }
        dmlHelper.insertObjects(casesToInsert, Source);
    }

    public void completeMilestone(Set<Id> caseIds, String milestoneName, DateTime completeDate) {
        List<CaseMilestone> caseMilestonesToUpdate = CaseMilestoneSelector.selectCaseMilestonesByCaseIdsAndMilestoneName(caseIds, milestoneName);

        if (caseMilestonesToUpdate.isEmpty() == false) {
            for (CaseMilestone caseMilestoneToUpdate : caseMilestonesToUpdate) {
                caseMilestoneToUpdate.CompletionDate = completeDate;
            }
            dmlHelper.updateObjects(caseMilestonesToUpdate, 'CaseService');
        }
    }
}