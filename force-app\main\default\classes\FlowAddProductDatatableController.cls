/********************************************************************************************

   @ Func Area	:  Apex development

   @ Author	:  <PERSON>

   @ Date	:  06 February 2025

   @ Description	:   

   @ Developer Notes  : https://ericsplayground.wordpress.com/how-to-use-an-apex-defined-object-with-my-datatable-flow-component/
                        https://github.com/ericrsmith35/Flow-PB-List-View-with-Batch-Delete/blob/master/force-app/main/default/classes/FlowDatatableDescriptor.cls
                        https://rathindradakua.medium.com/how-to-use-apex-defined-collection-variables-in-your-flows-to-retrieve-values-from-server-side-50d96553ef20


   @ Github Repo	:   https://github.com/mitchspano/apex-trigger-actions-framework
                        https://github.com/gscloudsolutions/GS-Apex-Mocking-Framework/tree/master
                        https://bluecanvas.io/blog/how-to-structure-and-manage-your-apex-codebase-for-scale-and-agility

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 06 February 2025   
   @ Last <PERSON>dified Reason  : Creation

********************************************************************************************/
public without sharing class FlowAddProductDatatableController {
  // Attributes passed in from the Flow
  public class Requests {
    @InvocableVariable(label='Pricebook Name')
    public String pricebookName;

    @InvocableVariable(label='Opportunity Currency')
    public String opportunityCurrency;
  }

  @InvocableMethod(label='Get Product Data for Datatable')
  public static List<CustomOutput> getProductTableData(
    List<Requests> requestList
  ) {
    List<CustomOutput> outputList = new List<CustomOutput>();
    CustomOutput outputToReturn = new CustomOutput();
    List<FlowDatatableProductDescriptor> flowDatatableProductDescriptorList = new List<FlowDatatableProductDescriptor>();
    String inputPricebookName = '';
    String inputOpportunityCurrency = '';

    // Bulkify proccessing of multiple requests
    for (Requests req : requestList) {
      // Get Input Value(s) passed in from the Flow

      inputPricebookName = req.pricebookName;
      //System.debug('inputPricebookName: ' + inputPricebookName);

      inputOpportunityCurrency = req.opportunityCurrency;
      //System.debug('inputOpportunityCurrency: ' + inputOpportunityCurrency);
    }

    List<PricebookEntry> pricebookEntryList = [
      SELECT
        Id,
        Product2Id,
        Product2.Name,
        Product2.ProductCode,
        Product2.UOMOfPackSize__c,
        Product2.PackSize__c,
        Pricebook2.Name,
        UnitPrice,
        CurrencyIsoCode
      FROM PricebookEntry
      WHERE
        Pricebook2.Name = :inputPricebookName
        AND CurrencyIsoCode = :inputOpportunityCurrency
      ORDER BY Product2.Name ASC
    ];

    if (CollectionUtils.isNotEmpty(pricebookEntryList)) {
      // Process pricebookEntryList
      for (PricebookEntry pricebookEntry : pricebookEntryList) {
        FlowDatatableProductDescriptor flowDatatableProductDescriptor = new FlowDatatableProductDescriptor(
          pricebookEntry.Product2.Name,
          pricebookEntry.Product2Id,
          pricebookEntry.Product2.ProductCode,
          pricebookEntry.Id,
          pricebookEntry.Product2.UOMOfPackSize__c,
          pricebookEntry.Product2.PackSize__c,
          pricebookEntry.UnitPrice,
          pricebookEntry.CurrencyIsoCode
        );
        flowDatatableProductDescriptorList.add(flowDatatableProductDescriptor);
      }
    }

    if (CollectionUtils.isNotEmpty(flowDatatableProductDescriptorList)) {
      //Set the output to the list of FlowDatatableProductDescriptor
      outputToReturn.productDataForOutput = flowDatatableProductDescriptorList;
      outputList.add(outputToReturn);
    } else {
      //Set the output to an empty list
      outputToReturn.productDataForOutput = new List<FlowDatatableProductDescriptor>();
      outputList.add(outputToReturn);
    }

    return outputList;
  }

  public class CustomOutput {
    @InvocableVariable
    public List<FlowDatatableProductDescriptor> productDataForOutput; //field which holds the list of FlowDatatableProductDescriptor type
  }
}