/********************************************************************************************

   @ Func Area	:  Apex development testing

   @ Author	:  <PERSON>

   @ Date		:  30 September 2024

   @ Description	:   A test class containing test methods for all LWC Controllers

   @ SFDC Documentation :   Test Best Practices to be followed:
                            https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_testsetup_using.htm
                            Assertion Class
                            https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Assert.htm#apex_System_Assert_isFalse

   @ Developer Notes    :   https://www.mitchspano.com/blog/pure_unit_testing_in_apex

   @ Class Tested	:   GeneralLWCController

   @ Last Modified By  : <PERSON>
   @ Last Modified On  : 30 September 2024
   @ Last Modified Reason  : Creation

********************************************************************************************/
@isTest
public class GeneralLWCControllerTest {
  @isTest
  static void test_ChildAccountListLWCController() {
    // Setup Test Data
    Id fakeParentAccountId = MockerUtils.generateId(Account.SObjectType);
    Id fakeChildAccountId2 = MockerUtils.generateId(Account.SObjectType);

    Account expectedChildAccount = (Account) MockerUtils.updateObjectState(
      new Account(Id = fakeChildAccountId2, Name = 'Child Account'),
      new Map<String, Object>{ 'Type' => 'Customer' }
    );

    // Starting the stubbing phase
    Mocker mocker = Mocker.startStubbing();

    AccountSelector accountSelectorMock = (AccountSelector) mocker.mock(AccountSelector.class);
    // Replacing the real instance by the mocked one
    AccountService.accountSelector = accountSelectorMock;

    mocker.when(accountSelectorMock.selectAccountsByParentIds(new Set<Id>{ fakeParentAccountId }))
      .thenReturn(new List<Account>{ expectedChildAccount });

    // Going to the execution phase
    mocker.stopStubbing();

    // Replacing the real instance by the mocked one
    AccountService.accountSelector = accountSelectorMock;

    List<Account> childAccounts = GeneralLWCController.getChildAccounts(fakeParentAccountId);
  }
}