<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 04-29-2024
  @last modified by  : <PERSON>
-->
<template>
    <lightning-formatted-rich-text value={label}></lightning-formatted-rich-text>
    <fieldset class="slds-form-element">
        <div class="slds-form-element__control slds-align_absolute-center slds-wrap">
            <template for:each={recs} for:item="record">
                <div class="slds-visual-picker slds-card" key={record.Id}>
                    <template lwc:if={allowMultiSelect}>
                        <input type="checkbox" onclick={handleChange} name="flexCards" id={record.Id}
                            title={record.Name} value={record.Id} data-id={record.Id} />
                    </template>

                    <template lwc:if={isClickable}>
                        <input type="radio" onclick={handleClick} name="flexCards" id={record.Id} title={record.Name}
                            value={record.Id} data-id={record.Id} />
                    </template>

                    <label for={record.Id}>
                        <span class="slds-visual-picker__figure slds-visual-picker__text mostly-customized-scrollbar"
                            style={sizeWidth}>
                            <div class="slds-card__header slds-grid">
                                <header class="slds-media slds-media_center slds-has-flexi-truncate"
                                    style={headerStyle}>

                                    <template lwc:if={showIcon}>
                                        <lightning-avatar src={record.src_URL__c} fallback-icon-name={icon}
                                            class="slds-m-right_small"></lightning-avatar>
                                    </template>

                                    <lightning-record-view-form object-api-name={objectAPIName} record-id={record.Id}>

                                        <lightning-output-field field-name={headerField} variant=label-hidden
                                            field-class={headerFieldClass}>
                                        </lightning-output-field>

                                    </lightning-record-view-form>

                                </header>
                            </div>
                            <template lwc:if={isFlowsLoaded}>
                                <div
                                    class="slds-grid slds-grid_align-spread slds-card__body slds-card__body_inner slds-text-align_left slds-border_top">



                                    <span class="slds-col slds-size_1-of-2" style={subheadCSS}>
                                        <lightning-record-view-form object-api-name={objectAPIName}
                                            record-id={record.Id}>

                                            <template for:each={fieldCollection} for:item="field">
                                                <lightning-output-field field-name={field} key={field}
                                                    variant={fieldVariant} field-class={fieldClass}>
                                                </lightning-output-field>
                                            </template>

                                        </lightning-record-view-form>




                                    </span>

                                    <span class="slds-col slds-size_1-of-2 slds-text-align_right">


                                        <c-fsc_action-list-3 flow-names={flows} record-id={record.Id}
                                            choice-type={actionDisplayType} button-label={buttonLabel}>
                                        </c-fsc_action-list-3>


                                    </span>

                                </div>
                            </template>
                            <template lwc:else>
                                <div
                                    class="slds-card__body slds-card__body_inner slds-text-align_left slds-grid slds-border_top content-overflow mostly-customized-scrollbar">
                                    <div class="slds-col slds-size_1-of-1" style={subheadCSS}>
                                        <lightning-record-view-form object-api-name={objectAPIName}
                                            record-id={record.Id}>

                                            <template for:each={fieldCollection} for:item="field">
                                                <lightning-output-field field-name={field} key={field}
                                                    variant={fieldVariant} field-class={fieldClass}>
                                                </lightning-output-field>
                                            </template>

                                        </lightning-record-view-form>
                                    </div>
                                </div>
                            </template>
                        </span>
                    </label>
                    <span class="slds-icon_container slds-visual-picker__text-check">
                        <lightning-icon icon-name="utility:check" size="x-small" variant="inverse"></lightning-icon>
                    </span>
                </div>
            </template>
        </div>
    </fieldset>
</template>