<template>
  <lightning-card
    title="Territory Assignment Batch Job Launcher 2 of 2"
    icon-name="standard:apex"
  >
    <!--<p><lightning-formatted-text value="Territory Assignment Batch Job" ></lightning-formatted-text></p>
          <br>-->
    <div class="slds-m-around_medium">
      <!-- <p><lightning-formatted-text value="Territory Assignment Batch Job" ></lightning-formatted-text></p>-->
      <lightning-button
        label="Launch Batch Job"
        onclick={handleClick}
        variant="brand"
        disabled={isLoading}
      >
      </lightning-button>
      <template if:true={isLoading}>
        <div class="slds-p-left_small slds-is-relative">
          <lightning-spinner
            alternative-text="Loading"
            size="small"
          ></lightning-spinner>
        </div>
      </template>
    </div>
  </lightning-card>
</template>