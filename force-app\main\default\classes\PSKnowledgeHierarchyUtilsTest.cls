/**
 * @description       : 
 * <AUTHOR> NuAge - JP
 * @group             : 
 * @last modified on  : 09-08-2023
 * @last modified by  : NuAge - JP
**/
@isTest
public class PSKnowledgeHierarchyUtilsTest {
    @isTest static void getKnowledgeHierarchyTest(){
        Knowledge__kav kk = new Knowledge__kav(Title='title1',urlName='test-article');
        insert kk;

        Knowledge__kav kk1 = new Knowledge__kav(Title='title12',urlName='test-article1');
        insert kk1;

        Knowledge__kav kk2 = new Knowledge__kav(Title='title3',urlName='test-article2');
        insert kk2;

        Knowledge__kav kk3 = new Knowledge__kav(Title='title4',urlName='test-article3');
        insert kk3;

        test.startTest();
            PSKnowledgeHierarchyUtils.getKnowledgeHierarchy();
        test.stopTest();
    }

    @isTest static void getKnowledgeArticlesTest(){
        Knowledge__kav kk = new Knowledge__kav(Title='title1',urlName='test-article');
        insert kk;

        Knowledge__kav kk1 = new Knowledge__kav(Title='title12',urlName='test-article1');
        insert kk1;

        Knowledge__kav kk2 = new Knowledge__kav(Title='title3',urlName='test-article2');
        insert kk2;

        Knowledge__kav kk3 = new Knowledge__kav(Title='title4',urlName='test-article3');
        insert kk3;

        // Knowledge__DataCategorySelection dataCategory = new Knowledge__DataCategorySelection();
        // // dataCategory.ParentId = kk.Id;
        // dataCategory.DataCategoryGroupName = 'Help_Center_Categories';
        // dataCategory.DataCategoryName = 'Known_Error';
        // insert dataCategory;

        Knowledge__DataCategorySelection dcs2 = new Knowledge__DataCategorySelection(ParentId=kk.Id,DataCategoryGroupName='Applications',DataCategoryName='Automation');
        insert dcs2;

        test.startTest();
            // PSKnowledgeHierarchyUtils.getKnowledgeHierarchy();
            List<Knowledge__DataCategorySelection>  dcsList = [SELECT ParentId, DataCategoryGroupName, DataCategoryName
                               FROM Knowledge__DataCategorySelection];
                               system.debug(dcsList);

            PSKnowledgeHierarchyUtils.getKnowledgeArticles('Applications','Automation');
        test.stopTest();
    }

    @isTest(SeeAllData = true) static void getKnowledgeArticlesTestnull(){
        Knowledge__kav kk = new Knowledge__kav(Title='title1',urlName='test-article');
        insert kk;

        Knowledge__kav kk1 = new Knowledge__kav(Title='title12',urlName='test-article1');
        insert kk1;

        Knowledge__kav kk2 = new Knowledge__kav(Title='title3',urlName='test-article2');
        insert kk2;

        Knowledge__kav kk3 = new Knowledge__kav(Title='title4',urlName='test-article3');
        insert kk3;

        // Knowledge__DataCategorySelection dataCategory = new Knowledge__DataCategorySelection();
        // // dataCategory.ParentId = kk.Id;
        // dataCategory.DataCategoryGroupName = 'Help_Center_Categories';
        // dataCategory.DataCategoryName = 'Known_Error';
        // insert dataCategory;

        Knowledge__DataCategorySelection dcs2 = new Knowledge__DataCategorySelection(ParentId=kk.Id,DataCategoryGroupName='Applications',DataCategoryName='Automation');
        insert dcs2;

        test.startTest();
            // PSKnowledgeHierarchyUtils.getKnowledgeHierarchy();
            List<Knowledge__DataCategorySelection>  dcsList = [SELECT ParentId, DataCategoryGroupName, DataCategoryName
                               FROM Knowledge__DataCategorySelection];
                               system.debug(dcsList);

            PSKnowledgeHierarchyUtils.getKnowledgeArticles(null,null);
        test.stopTest();
    }

    @isTest(SeeAllData = true) static void searchKnowledgeArticlesTest(){
        Knowledge__kav kk = new Knowledge__kav(Title='title1',urlName='test-article');
        insert kk;

        Knowledge__kav kk1 = new Knowledge__kav(Title='title12',urlName='test-article1');
        insert kk1;

        Knowledge__kav kk2 = new Knowledge__kav(Title='title3',urlName='test-article2');
        insert kk2;

        Knowledge__kav kk3 = new Knowledge__kav(Title='title4',urlName='test-article3');
        insert kk3;

        // Knowledge__DataCategorySelection dataCategory = new Knowledge__DataCategorySelection();
        // // dataCategory.ParentId = kk.Id;
        // dataCategory.DataCategoryGroupName = 'Help_Center_Categories';
        // dataCategory.DataCategoryName = 'Known_Error';
        // insert dataCategory;

        Knowledge__DataCategorySelection dcs2 = new Knowledge__DataCategorySelection(ParentId=kk.Id,DataCategoryGroupName='Applications',DataCategoryName='Automation');
        insert dcs2;

        test.startTest();
            // PSKnowledgeHierarchyUtils.getKnowledgeHierarchy();
            List<Knowledge__DataCategorySelection>  dcsList = [SELECT ParentId, DataCategoryGroupName, DataCategoryName
                               FROM Knowledge__DataCategorySelection];
                               system.debug(dcsList);

            PSKnowledgeHierarchyUtils.searchKnowledgeArticles('test-article','Applications','Automation');
        test.stopTest();
    }    

    @isTest static void saveOrderTest(){
        Knowledge__kav kk = new Knowledge__kav(Title='title1',urlName='test-article');
        insert kk;

        Knowledge__kav kk1 = new Knowledge__kav(Title='title12',urlName='test-article1');
        insert kk1;

        Knowledge__kav kk2 = new Knowledge__kav(Title='title3',urlName='test-article2');
        insert kk2;

        Knowledge__kav kk3 = new Knowledge__kav(Title='title4',urlName='test-article3');
        insert kk3;

        // Knowledge__DataCategorySelection dataCategory = new Knowledge__DataCategorySelection();
        // // dataCategory.ParentId = kk.Id;
        // dataCategory.DataCategoryGroupName = 'Help_Center_Categories';
        // dataCategory.DataCategoryName = 'Known_Error';
        // insert dataCategory;

        Knowledge__DataCategorySelection dcs2 = new Knowledge__DataCategorySelection(ParentId=kk.Id,DataCategoryGroupName='Applications',DataCategoryName='Automation');
        insert dcs2;

        KA_Hierarchy_Order__c ho;
        ho = new KA_Hierarchy_Order__c();
                            ho.Group_Name__c = 'Applications';
                            ho.Category__c = 'Automation';
                            ho.KA_Id__c = kk.Id;
                            ho.Order__c = 3;
        
        insert ho;

        test.startTest();
            PSKnowledgeHierarchyUtils.saveOrder('Applications', 'Automation', JSON.serialize(new List<PSKnowledgeHierarchyUtils.Order>()));
        test.stopTest();
    }
}